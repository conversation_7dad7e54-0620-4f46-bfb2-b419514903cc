<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择题统计</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .progress-bar {
            height: 20px;
            background-color: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #3b82f6;
            border-radius: 10px;
            transition: width 0.3s ease-in-out;
        }
        
        .progress-text {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #4b5563;
            font-size: 12px;
        }
        
        .option-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .option-item:hover {
            border-color: #3b82f6;
            background-color: #f3f4f6;
        }
        
        .option-item.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">选择题统计</h2>
            
            <div class="mb-6">
                <p class="text-gray-700 mb-2">问题：以下哪个是编程语言？</p>
                
                <div class="space-y-4">
                    <!-- 选项A -->
                    <div class="option-item" onclick="selectOption('A')">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium">A. Java</span>
                            <span class="text-sm text-gray-500">45%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 45%"></div>
                            <span class="progress-text">45%</span>
                        </div>
                    </div>
                    
                    <!-- 选项B -->
                    <div class="option-item" onclick="selectOption('B')">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium">B. HTML</span>
                            <span class="text-sm text-gray-500">30%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 30%"></div>
                            <span class="progress-text">30%</span>
                        </div>
                    </div>
                    
                    <!-- 选项C -->
                    <div class="option-item" onclick="selectOption('C')">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium">C. CSS</span>
                            <span class="text-sm text-gray-500">15%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 15%"></div>
                            <span class="progress-text">15%</span>
                        </div>
                    </div>
                    
                    <!-- 选项D -->
                    <div class="option-item" onclick="selectOption('D')">
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-medium">D. JSON</span>
                            <span class="text-sm text-gray-500">10%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 10%"></div>
                            <span class="progress-text">10%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    总参与人数：200人
                </div>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    提交
                </button>
            </div>
        </div>
    </div>

    <script>
        function selectOption(option) {
            // 移除所有选项的选中状态
            document.querySelectorAll('.option-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加当前选项的选中状态
            const selectedItem = document.querySelector(`.option-item:nth-child(${option.charCodeAt(0) - 64})`);
            selectedItem.classList.add('selected');
        }
    </script>
</body>
</html> 