<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>管理员仪表盘 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 280px;
      transition: all 0.3s;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 10;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .main-content {
      margin-left: 280px;
      width: calc(100% - 280px);
      max-width: 100%;
      padding: 2rem;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: all 0.3s;
    }
    .card:hover {
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    .icon-box {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link active flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>

      <a href="results.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        统计管理
      </div>
      
      <a href="statistics.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-pie mr-3"></i> 统计分析
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>
  
  <!-- 主内容区 -->
  <div class="main-content">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">管理员仪表盘</h1>
        <p class="text-gray-600">欢迎回来，管理员</p>
      </div>
      
      <div class="flex items-center">
        <div class="relative mr-4">
          <i class="fas fa-bell text-gray-500 text-xl"></i>
          <span class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
        </div>
        
        <div class="flex items-center">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="管理员头像" class="h-10 w-10 rounded-full">
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-700">张老师</p>
            <p class="text-xs text-gray-500">超级管理员</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 统计卡片区域 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 查询条件卡片 -->
      <div class="card p-5 lg:col-span-4">
        <h3 class="font-semibold text-gray-700 mb-4">查询条件</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">班级</label>
            <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <option value="">全部班级</option>
              <option value="1">计算机科学与技术1班</option>
              <option value="2">软件工程2班</option>
              <option value="3">人工智能3班</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">学生</label>
            <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <option value="">全部学生</option>
              <option value="1">王小明</option>
              <option value="2">李华</option>
              <option value="3">张雨</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">考试</label>
            <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <option value="">全部考试</option>
              <option value="1">期中考试</option>
              <option value="2">期末考试</option>
              <option value="3">单元测试</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">作业</label>
            <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <option value="">全部作业</option>
              <option value="1">作业1</option>
              <option value="2">作业2</option>
              <option value="3">作业3</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">问卷</label>
            <select class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <option value="">全部问卷</option>
              <option value="1">课程反馈问卷</option>
              <option value="2">教学评价问卷</option>
              <option value="3">学习情况问卷</option>
            </select>
          </div>
        </div>
        <div class="mt-4 flex justify-end">
          <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            查询
          </button>
        </div>
      </div>
      
      <div class="card p-5">
        <div class="flex items-start">
          <div class="icon-box bg-blue-100 text-blue-700">
            <i class="fas fa-users text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-gray-500 text-sm">学生总数</p>
            <h3 class="text-2xl font-bold text-gray-800">2,847</h3>
            <p class="text-green-500 text-xs mt-1"><i class="fas fa-arrow-up mr-1"></i> 12% 本月</p>
          </div>
        </div>
      </div>
      
      <div class="card p-5">
        <div class="flex items-start">
          <div class="icon-box bg-green-100 text-green-700">
            <i class="fas fa-file-alt text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-gray-500 text-sm">进行中考试</p>
            <h3 class="text-2xl font-bold text-gray-800">24</h3>
            <p class="text-red-500 text-xs mt-1"><i class="fas fa-arrow-down mr-1"></i> 5% 本周</p>
          </div>
        </div>
      </div>
      
      <div class="card p-5">
        <div class="flex items-start">
          <div class="icon-box bg-orange-100 text-orange-700">
            <i class="fas fa-book text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-gray-500 text-sm">题库总数</p>
            <h3 class="text-2xl font-bold text-gray-800">1,295</h3>
            <p class="text-green-500 text-xs mt-1"><i class="fas fa-arrow-up mr-1"></i> 8% 本月</p>
          </div>
        </div>
      </div>
      
      <div class="card p-5">
        <div class="flex items-start">
          <div class="icon-box bg-purple-100 text-purple-700">
            <i class="fas fa-chart-line text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-gray-500 text-sm">平均分数</p>
            <h3 class="text-2xl font-bold text-gray-800">76.5</h3>
            <p class="text-green-500 text-xs mt-1"><i class="fas fa-arrow-up mr-1"></i> 3.2% 本月</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图表与表格区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 近期考试 -->
      <div class="card p-5 lg:col-span-2">
        <div class="flex justify-between items-center mb-5">
          <h3 class="font-semibold text-gray-700">近期考试</h3>
          <a href="exam-management.html" class="text-blue-600 text-sm hover:underline">查看全部</a>
        </div>
        
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">考试名称</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">课程</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">开始时间</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与人数</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">期中考试</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm text-gray-500">高等数学</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-04-15 09:00</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">进行中</span>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                187/204
              </td>
            </tr>
            <tr>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">单元测试 #3</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm text-gray-500">大学物理</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-04-12 14:30</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">已完成</span>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                156/156
              </td>
            </tr>
            <tr>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">期末考试</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm text-gray-500">程序设计</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-04-20 10:00</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">未开始</span>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                0/98
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 排名前5学生 -->
      <div class="card p-5">
        <div class="flex justify-between items-center mb-5">
          <h3 class="font-semibold text-gray-700">排名前5学生</h3>
          <a href="results.html" class="text-blue-600 text-sm hover:underline">查看全部</a>
        </div>
        
        <div class="space-y-5">
          <div class="flex items-center">
            <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="学生头像" class="h-10 w-10 rounded-full">
            <div class="ml-3 flex-1">
              <div class="flex justify-between">
                <p class="text-sm font-medium text-gray-900">王小明</p>
                <p class="text-sm font-semibold text-gray-900">97.5分</p>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div class="bg-green-500 h-2 rounded-full" style="width: 97.5%"></div>
              </div>
            </div>
          </div>
          
          <div class="flex items-center">
            <img src="https://randomuser.me/api/portraits/men/42.jpg" alt="学生头像" class="h-10 w-10 rounded-full">
            <div class="ml-3 flex-1">
              <div class="flex justify-between">
                <p class="text-sm font-medium text-gray-900">李华</p>
                <p class="text-sm font-semibold text-gray-900">95.8分</p>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div class="bg-green-500 h-2 rounded-full" style="width: 95.8%"></div>
              </div>
            </div>
          </div>
          
          <div class="flex items-center">
            <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="学生头像" class="h-10 w-10 rounded-full">
            <div class="ml-3 flex-1">
              <div class="flex justify-between">
                <p class="text-sm font-medium text-gray-900">张雨</p>
                <p class="text-sm font-semibold text-gray-900">92.3分</p>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div class="bg-green-500 h-2 rounded-full" style="width: 92.3%"></div>
              </div>
            </div>
          </div>
          
          <div class="flex items-center">
            <img src="https://randomuser.me/api/portraits/men/79.jpg" alt="学生头像" class="h-10 w-10 rounded-full">
            <div class="ml-3 flex-1">
              <div class="flex justify-between">
                <p class="text-sm font-medium text-gray-900">刘强</p>
                <p class="text-sm font-semibold text-gray-900">90.1分</p>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div class="bg-green-500 h-2 rounded-full" style="width: 90.1%"></div>
              </div>
            </div>
          </div>
          
          <div class="flex items-center">
            <img src="https://randomuser.me/api/portraits/women/42.jpg" alt="学生头像" class="h-10 w-10 rounded-full">
            <div class="ml-3 flex-1">
              <div class="flex justify-between">
                <p class="text-sm font-medium text-gray-900">陈晓</p>
                <p class="text-sm font-semibold text-gray-900">89.7分</p>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div class="bg-green-500 h-2 rounded-full" style="width: 89.7%"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>