<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>创建/编辑考试 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 250px;
      transition: all 0.3s;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #1e40af;
    }
    .question-item {
      transition: all 0.2s;
    }
    .question-item:hover {
      background-color: #F9FAFB;
    }
    .question-item.selected {
      background-color: #EFF6FF;
      border-color: #93C5FD;
    }
    .question-type-badge {
      font-size: 0.7rem;
      padding: 0.1rem 0.5rem;
      border-radius: 9999px;
    }
    .tag-fill {
      background-color: #e0e7ff;
      color: #4338ca;
    }
    .tag-single {
      background-color: #d1fae5;
      color: #047857;
    }
    .tag-multiple {
      background-color: #dbeafe;
      color: #1d4ed8;
    }
    .tag-variable {
      background-color: #fef3c7;
      color: #92400e;
    }
  </style>
</head>
<body class="min-h-screen flex">
  <!-- 侧边栏 -->
  <div class="sidebar h-screen fixed">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-tachometer-alt w-6"></i>
        <span>控制面板</span>
      </a>
      
      <a href="user-management.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-users w-6"></i>
        <span>用户管理</span>
      </a>
      
      <a href="question-bank.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-question-circle w-6"></i>
        <span>题库管理</span>
      </a>
      
      <a href="exam-management.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-file-alt w-6"></i>
        <span>考试管理</span>
      </a>

      <div class="px-4 py-2">
        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider">问卷管理</div>
        <div class="mt-2 space-y-1">
          <a href="questionnaire-management.html" class="sidebar-link px-4 py-2 flex items-center">
            <i class="fas fa-clipboard-list w-6"></i>
            <span>问卷管理</span>
          </a>
          <a href="questionnaire-bank.html" class="sidebar-link px-4 py-2 flex items-center">
            <i class="fas fa-book w-6"></i>
            <span>问卷库</span>
          </a>
        </div>
      </div>
      
      <a href="results.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-chart-bar w-6"></i>
        <span>成绩管理</span>
      </a>
      
      <a href="#" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-cog w-6"></i>
        <span>系统设置</span>
      </a>
    </div>
  </div>
  
  <!-- 主内容区 -->
  <div class="ml-64 flex-1 p-8">
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">创建新考试</h1>
        <p class="text-gray-600">设置考试信息和选择试题</p>
      </div>
      
      <div class="flex space-x-3">
        <button class="flex items-center py-2 px-4 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50">
          <i class="fas fa-times mr-2"></i> 取消
        </button>
        <button class="btn-primary flex items-center py-2 px-4 text-white rounded-lg shadow-sm">
          <i class="fas fa-save mr-2"></i> 保存考试
        </button>
      </div>
    </div>
    
    <!-- 考试表单 -->
    <div class="card mb-6">
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-800 mb-4">基本信息</h2>
        
        <div class="grid grid-cols-6 gap-6">
          <div class="col-span-6 sm:col-span-3">
            <label for="exam-title" class="block text-sm font-medium text-gray-700 mb-1">考试名称</label>
            <input type="text" id="exam-title" name="exam-title" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="输入考试名称">
          </div>
          
          <div class="col-span-6 sm:col-span-3">
            <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">所属学科</label>
            <select id="subject" name="subject" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="1">高等数学</option>
              <option value="2">大学物理</option>
              <option value="3">程序设计</option>
              <option value="4">数据结构</option>
            </select>
          </div>
          
          <div class="col-span-6">
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">考试描述</label>
            <textarea id="description" name="description" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md p-3" placeholder="输入考试描述..."></textarea>
          </div>
          
          <div class="col-span-6 sm:col-span-3">
            <label for="start-time" class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
            <input type="datetime-local" id="start-time" name="start-time" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
          </div>
          
          <div class="col-span-6 sm:col-span-3">
            <label for="end-time" class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
            <input type="datetime-local" id="end-time" name="end-time" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
          </div>
          
          <div class="col-span-6 sm:col-span-2">
            <label for="duration" class="block text-sm font-medium text-gray-700 mb-1">考试时长（分钟）</label>
            <input type="number" id="duration" name="duration" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="120">
          </div>
          
          <div class="col-span-6 sm:col-span-2">
            <label for="pass-scores" class="block text-sm font-medium text-gray-700 mb-1">及格分数</label>
            <input type="number" id="pass-scores" name="pass-scores" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="60">
          </div>
          
          <div class="col-span-6 sm:col-span-2">
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">考试状态</label>
            <select id="status" name="status" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="1">草稿</option>
              <option value="2">已发布</option>
            </select>
          </div>
        </div>
      </div>
      
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-800 mb-4">考试设置</h2>
        
        <div class="space-y-4">
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input id="random-order" name="random-order" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
            </div>
            <div class="ml-3 text-sm">
              <label for="random-order" class="font-medium text-gray-700">随机排序试题</label>
              <p class="text-gray-500">每个学生看到的试题顺序都不同</p>
            </div>
          </div>
          
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input id="show-result" name="show-result" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded" checked>
            </div>
            <div class="ml-3 text-sm">
              <label for="show-result" class="font-medium text-gray-700">交卷后显示成绩</label>
              <p class="text-gray-500">学生提交后立即显示考试成绩</p>
            </div>
          </div>
          
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input id="show-answer" name="show-answer" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
            </div>
            <div class="ml-3 text-sm">
              <label for="show-answer" class="font-medium text-gray-700">交卷后显示正确答案</label>
              <p class="text-gray-500">学生提交后可以查看正确答案和解析</p>
            </div>
          </div>
          
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input id="one-time" name="one-time" type="checkbox" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded" checked>
            </div>
            <div class="ml-3 text-sm">
              <label for="one-time" class="font-medium text-gray-700">只允许提交一次</label>
              <p class="text-gray-500">学生只能提交一次答案</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-medium text-gray-800">试题选择</h2>
          <span class="text-sm text-gray-500">已选择 <span class="font-medium text-blue-600">0</span> 题 / 总分 <span class="font-medium text-blue-600">0</span> 分</span>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 题库区域 -->
          <div class="lg:col-span-2">
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-medium text-gray-700">题库</h3>
                
                <div class="flex space-x-2">
                  <button class="px-3 py-1 bg-white text-gray-600 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                    筛选
                  </button>
                  <div class="relative">
                    <input type="text" placeholder="搜索题目..." class="px-3 py-1 bg-white text-gray-600 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <i class="fas fa-search text-gray-400"></i>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="bg-white rounded-md border border-gray-200 overflow-hidden">
                <div class="max-h-96 overflow-y-auto p-1">
                  <!-- 单个题目 -->
                  <div class="question-item p-3 border-b border-gray-200 cursor-pointer">
                    <div class="flex justify-between items-start">
                      <div>
                        <div class="flex items-center mb-1">
                          <span class="question-type-badge tag-single mr-2">单选题</span>
                          <span class="text-xs text-gray-500">ID: #1001</span>
                        </div>
                        <p class="text-sm text-gray-800 line-clamp-2">在C语言中，用于存储字符的数据类型是（）。</p>
                      </div>
                      <div class="flex flex-col items-end">
                        <span class="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">简单</span>
                        <span class="text-xs text-gray-500 mt-1">5分</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 单个题目 -->
                  <div class="question-item p-3 border-b border-gray-200 cursor-pointer">
                    <div class="flex justify-between items-start">
                      <div>
                        <div class="flex items-center mb-1">
                          <span class="question-type-badge tag-fill mr-2">填空题</span>
                          <span class="text-xs text-gray-500">ID: #1002</span>
                        </div>
                        <p class="text-sm text-gray-800 line-clamp-2">求函数 f(x) = x² - 4x + 3 的最小值。</p>
                      </div>
                      <div class="flex flex-col items-end">
                        <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">中等</span>
                        <span class="text-xs text-gray-500 mt-1">8分</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 单个题目 -->
                  <div class="question-item p-3 border-b border-gray-200 cursor-pointer">
                    <div class="flex justify-between items-start">
                      <div>
                        <div class="flex items-center mb-1">
                          <span class="question-type-badge tag-multiple mr-2">多选题</span>
                          <span class="text-xs text-gray-500">ID: #1003</span>
                        </div>
                        <p class="text-sm text-gray-800 line-clamp-2">以下哪些选项是正确的数据结构类型？</p>
                      </div>
                      <div class="flex flex-col items-end">
                        <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-0.5 rounded-full">中等</span>
                        <span class="text-xs text-gray-500 mt-1">10分</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 单个题目 -->
                  <div class="question-item p-3 border-b border-gray-200 cursor-pointer">
                    <div class="flex justify-between items-start">
                      <div>
                        <div class="flex items-center mb-1">
                          <span class="question-type-badge tag-variable mr-2">不定项</span>
                          <span class="text-xs text-gray-500">ID: #1004</span>
                        </div>
                        <p class="text-sm text-gray-800 line-clamp-2">选择可以用于描述牛顿第二定律的表达式。</p>
                      </div>
                      <div class="flex flex-col items-end">
                        <span class="text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full">困难</span>
                        <span class="text-xs text-gray-500 mt-1">15分</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 已选题目区域 -->
          <div>
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div class="mb-4">
                <h3 class="text-base font-medium text-gray-700">已选试题</h3>
                <p class="text-xs text-gray-500 mt-1">拖拽可以调整顺序</p>
              </div>
              
              <div class="bg-white rounded-md border border-gray-200 overflow-hidden">
                <div class="p-8 text-center text-gray-500">
                  <i class="fas fa-file-alt text-3xl mb-2"></i>
                  <p class="text-sm">还没有选择任何试题</p>
                  <p class="text-xs mt-1">从左侧题库中点击题目添加</p>
                </div>
              </div>
              
              <div class="mt-4 flex justify-between">
                <button class="px-3 py-1 bg-red-50 text-red-600 text-sm border border-red-200 rounded-md hover:bg-red-100">
                  清空
                </button>
                <button class="px-3 py-1 bg-blue-50 text-blue-600 text-sm border border-blue-200 rounded-md hover:bg-blue-100">
                  自动组卷
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 提交按钮 -->
    <div class="flex justify-end space-x-3">
      <button class="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        保存为草稿
      </button>
      <button class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        保存并预览
      </button>
      <button class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
        保存并发布
      </button>
    </div>
  </div>
</body>
</html>