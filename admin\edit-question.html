<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>编辑题目 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 280px;
      transition: all 0.3s;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 10;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .main-content {
      margin-left: 280px;
      width: calc(100% - 280px);
      max-width: 100%;
      padding: 2rem;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #1e40af;
    }
    textarea {
      min-height: 120px;
    }
    .option-input {
      border-left: 3px solid #1E3A8A;
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-tachometer-alt w-6"></i>
        <span>控制面板</span>
      </a>
      
      <a href="user-management.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-users w-6"></i>
        <span>用户管理</span>
      </a>
      
      <a href="question-bank.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-question-circle w-6"></i>
        <span>题库管理</span>
      </a>
      
      <a href="exam-management.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-file-alt w-6"></i>
        <span>考试管理</span>
      </a>

      <div class="px-4 py-2">
        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider">问卷管理</div>
        <div class="mt-2 space-y-1">
          <a href="questionnaire-management.html" class="sidebar-link px-4 py-2 flex items-center">
            <i class="fas fa-clipboard-list w-6"></i>
            <span>问卷管理</span>
          </a>
          <a href="questionnaire-bank.html" class="sidebar-link px-4 py-2 flex items-center">
            <i class="fas fa-book w-6"></i>
            <span>问卷库</span>
          </a>
        </div>
      </div>
      
      <a href="results.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-chart-bar w-6"></i>
        <span>成绩管理</span>
      </a>
      
      <a href="#" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-cog w-6"></i>
        <span>系统设置</span>
      </a>
    </div>
  </div>
  
  <!-- 主内容区 -->
  <div class="main-content">
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">编辑题目</h1>
        <p class="text-gray-600">创建或修改题目信息</p>
      </div>
      
      <div class="flex space-x-3">
        <button class="flex items-center py-2 px-4 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50">
          <i class="fas fa-times mr-2"></i> 取消
        </button>
        <button class="btn-primary flex items-center py-2 px-4 text-white rounded-lg shadow-sm">
          <i class="fas fa-save mr-2"></i> 保存题目
        </button>
      </div>
    </div>
    
    <!-- 题目表单 -->
    <div class="card p-6">
      <form>
        <!-- 基本信息 -->
        <div class="mb-6">
          <h2 class="text-lg font-medium text-gray-800 mb-4">基本信息</h2>
          
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="col-span-1 lg:col-span-3">
              <label for="question-title" class="block text-sm font-medium text-gray-700 mb-1">题目内容</label>
              <textarea id="question-title" name="question-title" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md p-3" placeholder="请输入题目内容...">在C语言中，用于存储字符的数据类型是（）。</textarea>
            </div>
            
            <div>
              <label for="question-type" class="block text-sm font-medium text-gray-700 mb-1">题目类型</label>
              <select id="question-type" name="question-type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="1" selected>单选题</option>
                <option value="2">多选题</option>
                <option value="3">填空题</option>
                <option value="4">不定项选择题</option>
                <option value="5">问答题</option>
              </select>
            </div>
            
            <div>
              <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">所属学科</label>
              <select id="subject" name="subject" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="1">高等数学</option>
                <option value="2">大学物理</option>
                <option value="3" selected>程序设计</option>
                <option value="4">数据结构</option>
              </select>
            </div>
            
            <div>
              <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-1">难度等级</label>
              <select id="difficulty" name="difficulty" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                <option value="1" selected>简单</option>
                <option value="2">中等</option>
                <option value="3">困难</option>
              </select>
            </div>
            
            <div>
              <label for="scores" class="block text-sm font-medium text-gray-700 mb-1">分值</label>
              <input type="number" id="scores" name="scores" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="5">
            </div>
            
            <div class="col-span-1 lg:col-span-2">
              <label for="tags" class="block text-sm font-medium text-gray-700 mb-1">标签</label>
              <input type="text" id="tags" name="tags" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="输入标签，用逗号分隔..." value="C语言,数据类型,基础概念">
            </div>
          </div>
        </div>
        
        <!-- 选项和答案 -->
        <div class="border-t border-gray-200 pt-6">
          <h2 class="text-lg font-medium text-gray-800 mb-4">选项和答案</h2>
          
          <!-- 单选题选项 -->
          <div id="single-choice-options">
            <div class="mb-6">
              <div class="flex items-center justify-between mb-2">
                <label class="block text-sm font-medium text-gray-700">选项</label>
                <button type="button" class="flex items-center text-sm text-blue-600 hover:text-blue-800">
                  <i class="fas fa-plus-circle mr-1"></i> 添加选项
                </button>
              </div>
              
              <div class="space-y-3">
                <div class="flex items-center">
                  <div class="flex-shrink-0 mr-3">
                    <input type="radio" name="answer" id="option-a" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300" checked>
                  </div>
                  <div class="flex-grow">
                    <div class="flex items-center">
                      <span class="w-7 h-7 flex items-center justify-center bg-blue-100 text-blue-700 rounded-full font-medium mr-2">A</span>
                      <input type="text" name="option-a" class="option-input flex-grow shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="char">
                    </div>
                  </div>
                  <button type="button" class="ml-2 text-gray-400 hover:text-red-500">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
                
                <div class="flex items-center">
                  <div class="flex-shrink-0 mr-3">
                    <input type="radio" name="answer" id="option-b" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
                  </div>
                  <div class="flex-grow">
                    <div class="flex items-center">
                      <span class="w-7 h-7 flex items-center justify-center bg-blue-100 text-blue-700 rounded-full font-medium mr-2">B</span>
                      <input type="text" name="option-b" class="option-input flex-grow shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="int">
                    </div>
                  </div>
                  <button type="button" class="ml-2 text-gray-400 hover:text-red-500">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
                
                <div class="flex items-center">
                  <div class="flex-shrink-0 mr-3">
                    <input type="radio" name="answer" id="option-c" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
                  </div>
                  <div class="flex-grow">
                    <div class="flex items-center">
                      <span class="w-7 h-7 flex items-center justify-center bg-blue-100 text-blue-700 rounded-full font-medium mr-2">C</span>
                      <input type="text" name="option-c" class="option-input flex-grow shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="float">
                    </div>
                  </div>
                  <button type="button" class="ml-2 text-gray-400 hover:text-red-500">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
                
                <div class="flex items-center">
                  <div class="flex-shrink-0 mr-3">
                    <input type="radio" name="answer" id="option-d" class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300">
                  </div>
                  <div class="flex-grow">
                    <div class="flex items-center">
                      <span class="w-7 h-7 flex items-center justify-center bg-blue-100 text-blue-700 rounded-full font-medium mr-2">D</span>
                      <input type="text" name="option-d" class="option-input flex-grow shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="double">
                    </div>
                  </div>
                  <button type="button" class="ml-2 text-gray-400 hover:text-red-500">
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 问答题答案设置 -->
          <div id="essay-answer-settings" class="hidden mb-6">
            <div class="mb-4">
              <label for="essay-answer" class="block text-sm font-medium text-gray-700 mb-1">参考答案</label>
              <textarea id="essay-answer" name="essay-answer" rows="6" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md p-3" placeholder="请输入标准参考答案..."></textarea>
            </div>
            
            <div class="border border-gray-200 rounded-md p-4 bg-gray-50 mb-4">
              <h3 class="text-sm font-medium text-gray-700 mb-3">AI评分设置</h3>
              
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                <div>
                  <label class="block text-xs font-medium text-gray-700 mb-1">评分结果分类</label>
                  <div class="space-y-2">
                    <div class="flex items-center">
                      <span class="inline-block w-20 text-xs">已完成</span>
                      <input type="text" name="scores-complete" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-xs border-gray-300 rounded-md" placeholder="分数阈值..." value="80">
                      <span class="ml-1 text-xs text-gray-500">分以上</span>
                    </div>
                    <div class="flex items-center">
                      <span class="inline-block w-20 text-xs">待优化</span>
                      <input type="text" name="scores-optimize" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-xs border-gray-300 rounded-md" placeholder="分数阈值..." value="60">
                      <span class="ml-1 text-xs text-gray-500">~80分</span>
                    </div>
                    <div class="flex items-center">
                      <span class="inline-block w-20 text-xs">待重答</span>
                      <span class="ml-1 text-xs text-gray-500">60分以下</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <label for="ai-feedback-style" class="block text-xs font-medium text-gray-700 mb-1">反馈风格</label>
                  <select id="ai-feedback-style" name="ai-feedback-style" class="mt-1 block w-full py-1 px-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-xs">
                    <option value="detailed">详细反馈（列出具体优缺点）</option>
                    <option value="simple">简洁反馈（仅给出关键建议）</option>
                    <option value="encouraging">鼓励式反馈（突出优点并温和指出不足）</option>
                  </select>
                </div>
              </div>
              
              <div class="mb-3">
                <label for="ai-prompt-template" class="block text-xs font-medium text-gray-700 mb-1">AI评分提示词模板</label>
                <textarea id="ai-prompt-template" name="ai-prompt-template" rows="3" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full text-xs border-gray-300 rounded-md p-2" placeholder="配置AI评分的提示词模板...">请根据参考答案对学生回答进行评分，总分为100分。评分标准：1）内容完整性（40%）；2）概念准确性（30%）；3）逻辑性（20%）；4）表达清晰度（10%）。请给出具体分数并提供针对性的修改建议。</textarea>
              </div>
              
              <div class="flex items-center">
                <input id="allow-ai-customize" name="allow-ai-customize" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="allow-ai-customize" class="ml-2 block text-xs text-gray-700">允许教师自定义AI评分标准</label>
              </div>
            </div>
            
            <div class="border border-gray-200 rounded-md p-4 bg-gray-50">
              <h3 class="text-sm font-medium text-gray-700 mb-3">语音作答设置</h3>
              
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div>
                  <div class="flex items-center mb-3">
                    <input id="enable-voice-answer" name="enable-voice-answer" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                    <label for="enable-voice-answer" class="ml-2 block text-xs text-gray-700">启用语音作答功能</label>
                  </div>
                  
                  <div class="flex items-center mb-2">
                    <input id="require-network-check" name="require-network-check" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                    <label for="require-network-check" class="ml-2 block text-xs text-gray-700">开始语音答题前检查网络状态</label>
                  </div>
                </div>
                
                <div>
                  <label for="voice-recognition-language" class="block text-xs font-medium text-gray-700 mb-1">语音识别语言</label>
                  <select id="voice-recognition-language" name="voice-recognition-language" class="mt-1 block w-full py-1 px-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-xs">
                    <option value="zh-CN" selected>简体中文</option>
                    <option value="en-US">英语（美国）</option>
                    <option value="en-GB">英语（英国）</option>
                    <option value="ja-JP">日语</option>
                  </select>
                  
                  <div class="mt-2">
                    <label for="max-voice-duration" class="block text-xs font-medium text-gray-700 mb-1">最大语音录制时长（秒）</label>
                    <input type="number" id="max-voice-duration" name="max-voice-duration" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-xs border-gray-300 rounded-md" value="180">
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 答案解析 -->
          <div class="mb-6">
            <label for="explanation" class="block text-sm font-medium text-gray-700 mb-1">答案解析</label>
            <textarea id="explanation" name="explanation" rows="4" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md p-3" placeholder="请输入答案解析...">在C语言中，char 是用于存储字符的基本数据类型。int 用于存储整数，float 和 double 用于存储浮点数。</textarea>
          </div>
        </div>
        
        <!-- 高级设置 -->
        <div class="border-t border-gray-200 pt-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-medium text-gray-800">高级设置</h2>
            <button type="button" id="toggle-advanced" class="flex items-center text-sm text-gray-500 hover:text-gray-700">
              <i class="fas fa-chevron-down mr-1"></i> 展开设置
            </button>
          </div>
          
          <div id="advanced-settings" class="hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <label for="time-limit" class="block text-sm font-medium text-gray-700 mb-1">答题时间限制（秒）</label>
                <input type="number" id="time-limit" name="time-limit" class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md" value="60">
              </div>
              
              <div>
                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">题目分类</label>
                <select id="category" name="category" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                  <option value="1">基础知识</option>
                  <option value="2">进阶内容</option>
                  <option value="3">实际应用</option>
                  <option value="4">概念理解</option>
                </select>
              </div>
              
              <div id="question-preview-container" class="col-span-1 lg:col-span-2 border border-gray-200 rounded-md p-4 bg-gray-50">
                <div class="flex items-center justify-between mb-2">
                  <h3 class="text-sm font-medium text-gray-700">题目预览样式</h3>
                  <button type="button" class="text-xs text-blue-600 hover:text-blue-800">刷新预览</button>
                </div>
                <div class="bg-white border border-gray-200 p-3 rounded-md">
                  <p class="text-sm text-gray-800 mb-2">在C语言中，用于存储字符的数据类型是（）。</p>
                  <div class="space-y-1 text-sm">
                    <div class="flex items-center">
                      <span class="inline-block w-5 h-5 rounded-full bg-blue-100 text-blue-700 text-xs flex items-center justify-center mr-2">A</span>
                      <span>char</span>
                    </div>
                    <div class="flex items-center">
                      <span class="inline-block w-5 h-5 rounded-full bg-gray-100 text-gray-700 text-xs flex items-center justify-center mr-2">B</span>
                      <span>int</span>
                    </div>
                    <div class="flex items-center">
                      <span class="inline-block w-5 h-5 rounded-full bg-gray-100 text-gray-700 text-xs flex items-center justify-center mr-2">C</span>
                      <span>float</span>
                    </div>
                    <div class="flex items-center">
                      <span class="inline-block w-5 h-5 rounded-full bg-gray-100 text-gray-700 text-xs flex items-center justify-center mr-2">D</span>
                      <span>double</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-span-1 lg:col-span-2">
                <div class="flex items-center">
                  <input id="shuffle-options" name="shuffle-options" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <label for="shuffle-options" class="ml-2 block text-sm text-gray-700">随机打乱选项顺序</label>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 提交按钮 -->
        <div class="border-t border-gray-200 pt-6 flex justify-end space-x-3">
          <button type="button" class="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            预览
          </button>
          <button type="button" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            保存草稿
          </button>
          <button type="button" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
            保存并创建新题目
          </button>
          <button type="submit" class="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            保存题目
          </button>
        </div>
      </form>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const questionTypeSelect = document.getElementById('question-type');
      const singleChoiceOptions = document.getElementById('single-choice-options');
      const essayAnswerSettings = document.getElementById('essay-answer-settings');
      const toggleAdvancedBtn = document.getElementById('toggle-advanced');
      const advancedSettings = document.getElementById('advanced-settings');
      const questionPreviewContainer = document.getElementById('question-preview-container');
      
      // 题目类型切换处理
      questionTypeSelect.addEventListener('change', function() {
        const selectedType = this.value;
        
        // 隐藏所有选项配置
        singleChoiceOptions.classList.add('hidden');
        essayAnswerSettings.classList.add('hidden');
        
        // 根据选择的题型显示对应配置
        if (selectedType === '1' || selectedType === '2' || selectedType === '4') {
          singleChoiceOptions.classList.remove('hidden');
        } else if (selectedType === '5') {
          essayAnswerSettings.classList.remove('hidden');
        }
        
        // 更新预览区域
        updateQuestionPreview(selectedType);
      });
      
      // 高级设置切换
      toggleAdvancedBtn.addEventListener('click', function() {
        advancedSettings.classList.toggle('hidden');
        
        const icon = this.querySelector('i');
        if (advancedSettings.classList.contains('hidden')) {
          icon.classList.remove('fa-chevron-up');
          icon.classList.add('fa-chevron-down');
          this.innerHTML = '<i class="fas fa-chevron-down mr-1"></i> 展开设置';
        } else {
          icon.classList.remove('fa-chevron-down');
          icon.classList.add('fa-chevron-up');
          this.innerHTML = '<i class="fas fa-chevron-up mr-1"></i> 收起设置';
        }
      });
      
      // 更新题目预览
      function updateQuestionPreview(questionType) {
        const previewHTML = generatePreviewHTML(questionType);
        
        if (questionType === '5') { // 问答题
          questionPreviewContainer.classList.add('hidden');
        } else {
          questionPreviewContainer.classList.remove('hidden');
        }
      }
      
      // 生成预览HTML
      function generatePreviewHTML(questionType) {
        // 实际实现会根据不同题型生成不同的预览
        return '';
      }
    });
  </script>
</body>
</html> 