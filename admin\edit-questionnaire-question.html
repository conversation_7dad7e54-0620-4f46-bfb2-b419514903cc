<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>添加题目 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 250px;
      transition: all 0.3s;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #1e40af;
    }
    .tag {
      font-size: 0.75rem;
      padding: 0.15rem 0.5rem;
      border-radius: 9999px;
    }
    .tag-choice {
      background-color: #e0e7ff;
      color: #4338ca;
    }
    .tag-text {
      background-color: #d1fae5;
      color: #047857;
    }
    .tag-scale {
      background-color: #dbeafe;
      color: #1d4ed8;
    }
    .tag-matrix {
      background-color: #fef3c7;
      color: #92400e;
    }
    .option-item {
      transition: all 0.2s ease;
    }
    .option-item:hover {
      background-color: #f3f4f6;
    }
    .option-item:focus-within {
      background-color: #f0f9ff;
    }
  </style>
</head>
<body class="min-h-screen flex">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>

      <a href="results.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="flex-1 p-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">添加题目</h1>
      <div class="flex space-x-4">
        <button class="btn-primary text-white px-4 py-2 rounded-lg flex items-center">
          <i class="fas fa-save mr-2"></i> 保存
        </button>
        <button class="bg-gray-500 text-white px-4 py-2 rounded-lg flex items-center">
          <i class="fas fa-times mr-2"></i> 取消
        </button>
      </div>
    </div>

    <!-- 题目基本信息 -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <h2 class="text-lg font-semibold text-gray-800 mb-4">基本信息</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">题目类型</label>
          <select id="questionType" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="updateQuestionType()">
            <option value="choice">选择题</option>
            <option value="text">文本题</option>
            <option value="scale">量表题</option>
            <option value="matrix">矩阵题</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">题目标签</label>
          <input type="text" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入题目标签，多个标签用逗号分隔">
        </div>
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-2">题目内容</label>
          <textarea class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="请输入题目内容"></textarea>
        </div>
      </div>
    </div>

    <!-- 题目选项设置 -->
    <div id="optionsSection" class="bg-white rounded-lg shadow p-6 mb-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-800">选项设置</h2>
        <button class="btn-primary text-white px-4 py-2 rounded-lg flex items-center" onclick="addOption()">
          <i class="fas fa-plus mr-2"></i> 添加选项
        </button>
      </div>

      <!-- 选项列表 -->
      <div id="optionsList" class="space-y-2">
        <!-- 选项示例 -->
        <div class="option-item flex items-center p-2 rounded-lg">
          <input type="radio" class="mr-3" name="correctOption" checked>
          <input type="text" class="flex-1 px-3 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500" value="选项1">
          <button class="text-red-600 hover:text-red-800 ml-2" onclick="removeOption(this)">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 题目设置 -->
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-semibold text-gray-800 mb-4">题目设置</h2>
      <div class="space-y-4">
        <div class="flex items-center">
          <input type="checkbox" class="mr-2" checked>
          <span class="text-gray-700">必答题</span>
        </div>
        <div class="flex items-center">
          <input type="checkbox" class="mr-2">
          <span class="text-gray-700">允许其他选项</span>
        </div>
        <div class="flex items-center">
          <input type="checkbox" class="mr-2">
          <span class="text-gray-700">随机选项顺序</span>
        </div>
      </div>
    </div>
  </div>

  <script>
    function updateQuestionType() {
      const questionType = document.getElementById('questionType').value;
      const optionsSection = document.getElementById('optionsSection');
      const optionsList = document.getElementById('optionsList');

      // 清空现有选项
      optionsList.innerHTML = '';

      switch(questionType) {
        case 'choice':
          optionsSection.style.display = 'block';
          // 添加默认选项
          addOption();
          addOption();
          break;
        case 'text':
          optionsSection.style.display = 'none';
          break;
        case 'scale':
          optionsSection.style.display = 'block';
          // 添加量表选项
          const scaleOptions = ['非常不满意', '不满意', '一般', '满意', '非常满意'];
          scaleOptions.forEach(option => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item flex items-center p-2 rounded-lg';
            optionDiv.innerHTML = `
              <input type="radio" class="mr-3" name="correctOption">
              <input type="text" class="flex-1 px-3 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500" value="${option}">
              <button class="text-red-600 hover:text-red-800 ml-2" onclick="removeOption(this)">
                <i class="fas fa-trash"></i>
              </button>
            `;
            optionsList.appendChild(optionDiv);
          });
          break;
        case 'matrix':
          optionsSection.style.display = 'block';
          // 添加矩阵题选项
          const matrixOptions = ['非常不同意', '不同意', '一般', '同意', '非常同意'];
          matrixOptions.forEach(option => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-item flex items-center p-2 rounded-lg';
            optionDiv.innerHTML = `
              <input type="radio" class="mr-3" name="correctOption">
              <input type="text" class="flex-1 px-3 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500" value="${option}">
              <button class="text-red-600 hover:text-red-800 ml-2" onclick="removeOption(this)">
                <i class="fas fa-trash"></i>
              </button>
            `;
            optionsList.appendChild(optionDiv);
          });
          break;
      }
    }

    function addOption() {
      const optionsList = document.getElementById('optionsList');
      const optionCount = optionsList.children.length;
      
      const optionDiv = document.createElement('div');
      optionDiv.className = 'option-item flex items-center p-2 rounded-lg';
      optionDiv.innerHTML = `
        <input type="radio" class="mr-3" name="correctOption">
        <input type="text" class="flex-1 px-3 py-1 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="选项${optionCount + 1}">
        <button class="text-red-600 hover:text-red-800 ml-2" onclick="removeOption(this)">
          <i class="fas fa-trash"></i>
        </button>
      `;
      optionsList.appendChild(optionDiv);
    }

    function removeOption(button) {
      const optionItem = button.parentElement;
      optionItem.remove();
    }

    // 初始化页面
    updateQuestionType();
  </script>
</body>
</html> 