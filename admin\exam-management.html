<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>考试管理 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 250px;
      transition: all 0.3s;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #1e40af;
    }
    .exam-card {
      transition: all 0.3s ease;
    }
    .exam-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    .status-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
    }
    .status-active {
      background-color: #d1fae5;
      color: #047857;
    }
    .status-upcoming {
      background-color: #fef3c7;
      color: #92400e;
    }
    .status-completed {
      background-color: #dbeafe;
      color: #1d4ed8;
    }
    .status-draft {
      background-color: #f3f4f6;
      color: #4b5563;
    }
  </style>
</head>
<body class="min-h-screen flex">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>

      <a href="results.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link active flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>
  
  <!-- 主内容区 -->
  <div class="flex-1 p-8">
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">考试管理</h1>
        <p class="text-gray-600">管理所有考试和测验</p>
      </div>
      
      <a href="edit-exam.html" class="btn-primary flex items-center py-2 px-4 text-white rounded-lg shadow-sm">
        <i class="fas fa-plus mr-2"></i> 创建新考试
      </a>
    </div>
    
    <!-- 过滤和搜索区域 -->
    <div class="card p-5 mb-6">
      <div class="grid grid-cols-4 gap-4">
        <div class="col-span-2">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">搜索考试</label>
          <div class="relative rounded-md shadow-sm">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text" id="search" class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md" placeholder="输入关键词搜索考试...">
          </div>
        </div>
        
        <div>
          <label for="class" class="block text-sm font-medium text-gray-700 mb-1">班级</label>
          <select id="class" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <option value="">全部班级</option>
            <option value="1">计算机科学2022-1班</option>
            <option value="2">计算机科学2022-2班</option>
            <option value="3">软件工程2022-1班</option>
            <option value="4">软件工程2022-2班</option>
          </select>
        </div>
        
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <select id="status" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <option value="">全部状态</option>
            <option value="1">草稿</option>
            <option value="2">未开始</option>
            <option value="3">进行中</option>
            <option value="4">已完成</option>
          </select>
        </div>
      </div>
      
      <div class="flex justify-between mt-5">
        <button class="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 text-sm">
          重置筛选
        </button>
        
        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
          应用筛选
        </button>
      </div>
    </div>
    
    <!-- 考试列表 - 卡片视图 -->
    <div class="mb-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium text-gray-800">全部考试</h2>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">
            <i class="fas fa-th-large"></i> 卡片视图
          </button>
          <button class="px-3 py-1 bg-white text-gray-600 rounded-md text-sm border border-gray-300">
            <i class="fas fa-list"></i> 列表视图
          </button>
        </div>
      </div>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 考试卡片 1 -->
        <div class="exam-card card p-5 border border-gray-200">
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-medium text-gray-800">期中考试</h3>
            <span class="status-badge status-active">进行中</span>
          </div>
          
          <p class="text-gray-600 text-sm mb-4">高等数学</p>
          
          <div class="mb-4">
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <p class="text-gray-500">开始时间</p>
                <p class="font-medium">2023-04-15 09:00</p>
              </div>
              <div>
                <p class="text-gray-500">结束时间</p>
                <p class="font-medium">2023-04-15 11:00</p>
              </div>
              <div>
                <p class="text-gray-500">试题数量</p>
                <p class="font-medium">40 题</p>
              </div>
              <div>
                <p class="text-gray-500">总分值</p>
                <p class="font-medium">100 分</p>
              </div>
            </div>
          </div>
          
          <div class="border-t border-gray-200 pt-4 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              参与人数: <span class="font-medium text-gray-800">187/204</span>
            </div>
            
            <div class="flex space-x-2">
              <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-edit"></i>
              </a>
              <a href="#" class="text-red-600 hover:text-red-800 text-sm">
                <i class="fas fa-trash-alt"></i>
              </a>
              <a href="#" class="text-green-600 hover:text-green-800 text-sm">
                <i class="fas fa-chart-bar"></i>
              </a>
            </div>
          </div>
        </div>
        
        <!-- 考试卡片 2 -->
        <div class="exam-card card p-5 border border-gray-200">
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-medium text-gray-800">单元测试 #3</h3>
            <span class="status-badge status-completed">已完成</span>
          </div>
          
          <p class="text-gray-600 text-sm mb-4">大学物理</p>
          
          <div class="mb-4">
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <p class="text-gray-500">开始时间</p>
                <p class="font-medium">2023-04-12 14:30</p>
              </div>
              <div>
                <p class="text-gray-500">结束时间</p>
                <p class="font-medium">2023-04-12 15:30</p>
              </div>
              <div>
                <p class="text-gray-500">试题数量</p>
                <p class="font-medium">25 题</p>
              </div>
              <div>
                <p class="text-gray-500">总分值</p>
                <p class="font-medium">50 分</p>
              </div>
            </div>
          </div>
          
          <div class="border-t border-gray-200 pt-4 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              参与人数: <span class="font-medium text-gray-800">156/156</span>
            </div>
            
            <div class="flex space-x-2">
              <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-edit"></i>
              </a>
              <a href="#" class="text-red-600 hover:text-red-800 text-sm">
                <i class="fas fa-trash-alt"></i>
              </a>
              <a href="#" class="text-green-600 hover:text-green-800 text-sm">
                <i class="fas fa-chart-bar"></i>
              </a>
            </div>
          </div>
        </div>
        
        <!-- 考试卡片 3 -->
        <div class="exam-card card p-5 border border-gray-200">
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-medium text-gray-800">期末考试</h3>
            <span class="status-badge status-upcoming">未开始</span>
          </div>
          
          <p class="text-gray-600 text-sm mb-4">程序设计</p>
          
          <div class="mb-4">
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <p class="text-gray-500">开始时间</p>
                <p class="font-medium">2023-04-20 10:00</p>
              </div>
              <div>
                <p class="text-gray-500">结束时间</p>
                <p class="font-medium">2023-04-20 12:30</p>
              </div>
              <div>
                <p class="text-gray-500">试题数量</p>
                <p class="font-medium">35 题</p>
              </div>
              <div>
                <p class="text-gray-500">总分值</p>
                <p class="font-medium">100 分</p>
              </div>
            </div>
          </div>
          
          <div class="border-t border-gray-200 pt-4 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              参与人数: <span class="font-medium text-gray-800">0/98</span>
            </div>
            
            <div class="flex space-x-2">
              <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-edit"></i>
              </a>
              <a href="#" class="text-red-600 hover:text-red-800 text-sm">
                <i class="fas fa-trash-alt"></i>
              </a>
              <a href="#" class="text-gray-400 text-sm pointer-events-none">
                <i class="fas fa-chart-bar"></i>
              </a>
            </div>
          </div>
        </div>
        
        <!-- 考试卡片 4 -->
        <div class="exam-card card p-5 border border-gray-200">
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-medium text-gray-800">数据库原理复习测试</h3>
            <span class="status-badge status-draft">草稿</span>
          </div>
          
          <p class="text-gray-600 text-sm mb-4">数据结构</p>
          
          <div class="mb-4">
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <p class="text-gray-500">开始时间</p>
                <p class="font-medium">未设置</p>
              </div>
              <div>
                <p class="text-gray-500">结束时间</p>
                <p class="font-medium">未设置</p>
              </div>
              <div>
                <p class="text-gray-500">试题数量</p>
                <p class="font-medium">20 题</p>
              </div>
              <div>
                <p class="text-gray-500">总分值</p>
                <p class="font-medium">40 分</p>
              </div>
            </div>
          </div>
          
          <div class="border-t border-gray-200 pt-4 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              参与人数: <span class="font-medium text-gray-800">0/0</span>
            </div>
            
            <div class="flex space-x-2">
              <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-edit"></i>
              </a>
              <a href="#" class="text-red-600 hover:text-red-800 text-sm">
                <i class="fas fa-trash-alt"></i>
              </a>
              <a href="#" class="text-gray-400 text-sm pointer-events-none">
                <i class="fas fa-chart-bar"></i>
              </a>
            </div>
          </div>
        </div>
        
        <!-- 考试卡片 5 -->
        <div class="exam-card card p-5 border border-gray-200">
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-medium text-gray-800">二级C语言模拟考试</h3>
            <span class="status-badge status-upcoming">未开始</span>
          </div>
          
          <p class="text-gray-600 text-sm mb-4">程序设计</p>
          
          <div class="mb-4">
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <p class="text-gray-500">开始时间</p>
                <p class="font-medium">2023-04-25 14:00</p>
              </div>
              <div>
                <p class="text-gray-500">结束时间</p>
                <p class="font-medium">2023-04-25 16:00</p>
              </div>
              <div>
                <p class="text-gray-500">试题数量</p>
                <p class="font-medium">60 题</p>
              </div>
              <div>
                <p class="text-gray-500">总分值</p>
                <p class="font-medium">100 分</p>
              </div>
            </div>
          </div>
          
          <div class="border-t border-gray-200 pt-4 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              参与人数: <span class="font-medium text-gray-800">45/120</span>
            </div>
            
            <div class="flex space-x-2">
              <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-edit"></i>
              </a>
              <a href="#" class="text-red-600 hover:text-red-800 text-sm">
                <i class="fas fa-trash-alt"></i>
              </a>
              <a href="#" class="text-gray-400 text-sm pointer-events-none">
                <i class="fas fa-chart-bar"></i>
              </a>
            </div>
          </div>
        </div>
        
        <!-- 考试卡片 6 -->
        <div class="exam-card card p-5 border border-gray-200">
          <div class="flex justify-between items-start mb-3">
            <h3 class="text-lg font-medium text-gray-800">概率论基础测验</h3>
            <span class="status-badge status-completed">已完成</span>
          </div>
          
          <p class="text-gray-600 text-sm mb-4">高等数学</p>
          
          <div class="mb-4">
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <p class="text-gray-500">开始时间</p>
                <p class="font-medium">2023-04-08 10:30</p>
              </div>
              <div>
                <p class="text-gray-500">结束时间</p>
                <p class="font-medium">2023-04-08 11:30</p>
              </div>
              <div>
                <p class="text-gray-500">试题数量</p>
                <p class="font-medium">20 题</p>
              </div>
              <div>
                <p class="text-gray-500">总分值</p>
                <p class="font-medium">50 分</p>
              </div>
            </div>
          </div>
          
          <div class="border-t border-gray-200 pt-4 flex justify-between items-center">
            <div class="text-sm text-gray-500">
              参与人数: <span class="font-medium text-gray-800">198/204</span>
            </div>
            
            <div class="flex space-x-2">
              <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">
                <i class="fas fa-edit"></i>
              </a>
              <a href="#" class="text-red-600 hover:text-red-800 text-sm">
                <i class="fas fa-trash-alt"></i>
              </a>
              <a href="#" class="text-green-600 hover:text-green-800 text-sm">
                <i class="fas fa-chart-bar"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div class="flex items-center justify-between">
      <div class="text-sm text-gray-700">
        显示 <span class="font-medium">1</span> 到 <span class="font-medium">6</span> 项，共 <span class="font-medium">24</span> 项
      </div>
      
      <div class="flex space-x-2">
        <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
          上一页
        </button>
        <button class="px-3 py-1 rounded-md bg-blue-50 border border-blue-500 text-blue-600 text-sm">
          1
        </button>
        <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
          2
        </button>
        <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
          3
        </button>
        <span class="px-3 py-1 text-gray-500 text-sm">...</span>
        <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
          4
        </button>
        <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
          下一页
        </button>
      </div>
    </div>
  </div>
</body>
</html> 