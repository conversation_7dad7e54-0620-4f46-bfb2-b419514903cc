<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>作业管理 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 280px;
      transition: all 0.3s;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 10;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .main-content {
      margin-left: 280px;
      width: calc(100% - 280px);
      max-width: 100%;
      padding: 2rem;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #1e40af;
    }
    .status-badge {
      font-size: 0.75rem;
      padding: 0.15rem 0.5rem;
      border-radius: 9999px;
    }
    .status-draft {
      background-color: #F3F4F6;
      color: #6B7280;
    }
    .status-published {
      background-color: #D1FAE5;
      color: #047857;
    }
    .status-ended {
      background-color: #FEE2E2;
      color: #DC2626;
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>
      
      <a href="results.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link active flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>
  
  <!-- 主内容区 -->
  <div class="main-content">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">作业管理</h1>
        <p class="text-gray-600">管理所有作业</p>
      </div>
      
      <a href="edit-homework.html" class="btn-primary flex items-center py-2 px-4 text-white rounded-lg shadow-sm">
        <i class="fas fa-plus mr-2"></i> 创建新作业
      </a>
    </div>
    
    <!-- 过滤和搜索区域 -->
    <div class="card p-5 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">搜索作业</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text" id="search" class="pl-10 w-full py-2 px-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="输入关键词搜索...">
          </div>
        </div>
        
        <div>
          <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">学科</label>
          <select id="subject" class="w-full py-2 px-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">全部学科</option>
            <option value="1">高等数学</option>
            <option value="2">大学物理</option>
            <option value="3">程序设计</option>
            <option value="4">数据结构</option>
          </select>
        </div>
        
        <div>
          <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <select id="status" class="w-full py-2 px-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">全部状态</option>
            <option value="1">草稿</option>
            <option value="2">已发布</option>
            <option value="3">已结束</option>
          </select>
        </div>
        
        <div>
          <label for="date" class="block text-sm font-medium text-gray-700 mb-1">截止日期</label>
          <input type="date" id="date" class="w-full py-2 px-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        </div>
      </div>
      
      <div class="flex justify-between mt-4">
        <div class="flex space-x-2">
          <button class="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 text-sm">
            重置筛选
          </button>
        </div>
        
        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
          应用筛选
        </button>
      </div>
    </div>
    
    <!-- 作业列表 -->
    <div class="card">
      <div class="flex justify-between items-center p-5 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-800">作业列表</h2>
        <div class="text-sm text-gray-500">共 24 个作业</div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <input id="select-all" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <label for="select-all" class="ml-2">ID</label>
                </div>
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                作业名称
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学科
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                截止日期
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                提交人数
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="ml-2 text-sm text-gray-900">#HW001</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm font-medium text-gray-900">高等数学作业 #1</div>
                <div class="text-sm text-gray-500">函数与极限</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">高等数学</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-published">已发布</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-04-30</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">45/50</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="edit-homework.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-green-600 hover:text-green-900 mr-3">查看提交</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
            
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="ml-2 text-sm text-gray-900">#HW002</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm font-medium text-gray-900">程序设计作业 #2</div>
                <div class="text-sm text-gray-500">数组与字符串</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">程序设计</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-draft">草稿</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-05-15</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">0/48</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="edit-homework.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-green-600 hover:text-green-900 mr-3">查看提交</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
            
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="ml-2 text-sm text-gray-900">#HW003</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm font-medium text-gray-900">大学物理作业 #3</div>
                <div class="text-sm text-gray-500">力学基础</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">大学物理</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-ended">已结束</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-04-20</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">52/52</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="edit-homework.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-green-600 hover:text-green-900 mr-3">查看提交</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-500">
          显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 项，共 <span class="font-medium">24</span> 项
        </div>
        
        <div class="flex space-x-2">
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            上一页
          </button>
          <button class="px-3 py-1 rounded-md bg-blue-50 border border-blue-500 text-blue-600 text-sm">
            1
          </button>
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            2
          </button>
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            3
          </button>
          <span class="px-3 py-1 text-gray-500 text-sm">...</span>
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            8
          </button>
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 