<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>管理员登录 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #EEF2FF;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .login-container {
      max-width: 400px;
    }
    .login-card {
      background-color: white;
      border-radius: 10px;
      box-shadow: 0 10px 25px rgba(30, 58, 138, 0.1);
    }
    .login-button {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .login-button:hover {
      background-color: #1e40af;
      transform: translateY(-2px);
    }
  </style>
</head>
<body class="min-h-screen flex items-center justify-center">
  <div class="login-container w-full px-6">
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-blue-900">学生答题系统</h1>
      <p class="text-gray-600 mt-2">管理员门户</p>
    </div>
    
    <div class="login-card p-8">
      <div class="text-center mb-6">
        <div class="inline-block p-3 rounded-full bg-blue-100 text-blue-800 mb-3">
          <i class="fas fa-user-shield text-2xl"></i>
        </div>
        <h2 class="text-2xl font-semibold text-gray-800">管理员登录</h2>
        <p class="text-gray-500 text-sm mt-1">请输入您的账号信息</p>
      </div>
      
      <form>
        <div class="mb-4">
          <label for="username" class="block text-gray-700 text-sm font-medium mb-2">用户名</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-user text-gray-400"></i>
            </div>
            <input type="text" id="username" name="username" class="pl-10 w-full py-2 px-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入用户名">
          </div>
        </div>
        
        <div class="mb-6">
          <label for="password" class="block text-gray-700 text-sm font-medium mb-2">密码</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-lock text-gray-400"></i>
            </div>
            <input type="password" id="password" name="password" class="pl-10 w-full py-2 px-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入密码">
          </div>
        </div>
        
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center">
            <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="remember-me" class="ml-2 block text-sm text-gray-700">记住我</label>
          </div>
          
          <a href="#" class="text-sm text-blue-600 hover:text-blue-800">忘记密码?</a>
        </div>
        
        <button type="button" onclick="window.location.href='dashboard.html'" class="login-button w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <i class="fas fa-sign-in-alt mr-2"></i> 登录
        </button>
      </form>
    </div>
    
    <div class="text-center mt-6">
      <p class="text-xs text-gray-500">© 2023 学生答题系统. 保留所有权利.</p>
    </div>
  </div>
</body>
</html> 