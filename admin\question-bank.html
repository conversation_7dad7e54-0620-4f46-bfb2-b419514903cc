<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>题库管理 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 250px;
      transition: all 0.3s;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #1e40af;
    }
    .tag {
      font-size: 0.75rem;
      padding: 0.15rem 0.5rem;
      border-radius: 9999px;
    }
    .tag-fill {
      background-color: #e0e7ff;
      color: #4338ca;
    }
    .tag-single {
      background-color: #d1fae5;
      color: #047857;
    }
    .tag-multiple {
      background-color: #dbeafe;
      color: #1d4ed8;
    }
    .tag-variable {
      background-color: #fef3c7;
      color: #92400e;
    }
  </style>
</head>
<body class="min-h-screen flex">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>

      <a href="results.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link active flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>
  
  <!-- 主内容区 -->
  <div class="flex-1 p-8">
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">题库管理</h1>
        <p class="text-gray-600">管理所有题目和题库</p>
      </div>
      
      <a href="edit-question.html" class="btn-primary flex items-center py-2 px-4 text-white rounded-lg shadow-sm">
        <i class="fas fa-plus mr-2"></i> 添加新题目
      </a>
    </div>
    
    <!-- 过滤和搜索区域 -->
    <div class="card p-5 mb-6">
      <div class="grid grid-cols-5 gap-4">
        <div class="col-span-2">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">搜索题目</label>
          <div class="relative rounded-md shadow-sm">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text" id="search" class="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md" placeholder="输入关键词搜索题目...">
          </div>
        </div>
        
        <div>
          <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">学科</label>
          <select id="subject" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <option value="">全部学科</option>
            <option value="1">高等数学</option>
            <option value="2">大学物理</option>
            <option value="3">程序设计</option>
            <option value="4">数据结构</option>
          </select>
        </div>
        
        <div>
          <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-1">难度</label>
          <select id="difficulty" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <option value="">全部难度</option>
            <option value="1">简单</option>
            <option value="2">中等</option>
            <option value="3">困难</option>
          </select>
        </div>
        
        <div>
          <label for="type" class="block text-sm font-medium text-gray-700 mb-1">题型</label>
          <select id="type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            <option value="">全部题型</option>
            <option value="1">填空题</option>
            <option value="2">单选题</option>
            <option value="3">多选题</option>
            <option value="4">不定项选择题</option>
          </select>
        </div>
      </div>
      
      <div class="flex justify-between mt-5">
        <div class="flex space-x-2">
          <button class="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 text-sm">
            重置筛选
          </button>
          <button class="px-4 py-2 bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 text-sm">
            批量导入
          </button>
          <button class="px-4 py-2 bg-green-50 text-green-700 rounded-md hover:bg-green-100 text-sm">
            导出题库
          </button>
        </div>
        
        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
          应用筛选
        </button>
      </div>
    </div>
    
    <!-- 题目列表 -->
    <div class="card">
      <div class="flex justify-between items-center p-5 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-800">题目列表</h2>
        <div class="text-sm text-gray-500">共 1,295 个题目</div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <input id="select-all" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <label for="select-all" class="ml-2">ID</label>
                </div>
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                题目
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学科
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                难度
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="ml-2 text-sm text-gray-900">#1001</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900">在C语言中，用于存储字符的数据类型是（）。</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="tag tag-single">单选题</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">程序设计</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  简单
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-03-15</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="edit-question.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
            
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="ml-2 text-sm text-gray-900">#1002</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900">求函数 f(x) = x² - 4x + 3 的最小值。</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="tag tag-fill">填空题</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">高等数学</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  中等
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-03-17</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="edit-question.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
            
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="ml-2 text-sm text-gray-900">#1003</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900">以下哪些选项是正确的数据结构类型？</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="tag tag-multiple">多选题</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">数据结构</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  中等
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-03-20</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="edit-question.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
            
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="ml-2 text-sm text-gray-900">#1004</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900">选择可以用于描述牛顿第二定律的表达式。</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="tag tag-variable">不定项</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">大学物理</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  困难
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-03-22</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="edit-question.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
            
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                  <span class="ml-2 text-sm text-gray-900">#1005</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900">求解微分方程 dy/dx + P(x)y = Q(x) 的通解。</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="tag tag-fill">填空题</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">高等数学</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  困难
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-03-25</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="edit-question.html" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-500">
          显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 项，共 <span class="font-medium">1,295</span> 项
        </div>
        
        <div class="flex space-x-2">
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            上一页
          </button>
          <button class="px-3 py-1 rounded-md bg-blue-50 border border-blue-500 text-blue-600 text-sm">
            1
          </button>
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            2
          </button>
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            3
          </button>
          <span class="px-3 py-1 text-gray-500 text-sm">...</span>
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            259
          </button>
          <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 text-sm hover:bg-gray-50">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 