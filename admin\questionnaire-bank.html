<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>问卷题库管理 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 250px;
      transition: all 0.3s;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #1e40af;
    }
    .tag {
      font-size: 0.75rem;
      padding: 0.15rem 0.5rem;
      border-radius: 9999px;
    }
    .tag-choice {
      background-color: #e0e7ff;
      color: #4338ca;
    }
    .tag-text {
      background-color: #d1fae5;
      color: #047857;
    }
    .tag-scale {
      background-color: #dbeafe;
      color: #1d4ed8;
    }
    .tag-matrix {
      background-color: #fef3c7;
      color: #92400e;
    }
    .question-card {
      transition: all 0.3s ease;
    }
    .question-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    #importModal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }
    .modal-content {
      position: relative;
      background-color: white;
      margin: 15% auto;
      padding: 20px;
      width: 80%;
      max-width: 500px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>
<body class="min-h-screen flex">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>

      <a href="results.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link active flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="flex-1 p-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">问卷题库管理</h1>
      <div class="flex space-x-4">
        <button onclick="showImportModal()" class="btn-primary text-white px-4 py-2 rounded-lg flex items-center">
          <i class="fas fa-file-import mr-2"></i> 导入题目
        </button>
        <a href="edit-questionnaire-question.html" class="btn-primary text-white px-4 py-2 rounded-lg flex items-center">
          <i class="fas fa-plus mr-2"></i> 添加题目
        </a>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1">
          <input type="text" placeholder="搜索题目内容..." class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <select class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">所有类型</option>
          <option value="collection">收集问卷</option>
          <option value="personalized">个性化问卷</option>
        </select>
        <button class="btn-primary text-white px-6 py-2 rounded-lg">搜索</button>
      </div>
    </div>

    <!-- 题目列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 收集问卷题目示例 -->
      <div class="question-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <span class="text-gray-500 mr-2">1.</span>
              <span class="text-gray-800 font-medium">您对当前课程的教学质量满意吗？</span>
            </div>
            <div class="flex space-x-2">
              <span class="tag tag-scale">收集问卷</span>
              <span class="tag tag-fill">满意度调查</span>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="text-sm text-gray-500">
          <div class="flex items-center mb-1">
            <i class="fas fa-calendar-alt mr-2"></i>
            <span>创建时间：2024-04-27</span>
          </div>
          <div class="flex items-center">
            <i class="fas fa-chart-bar mr-2"></i>
            <span>使用次数：12次</span>
          </div>
        </div>
      </div>

      <div class="question-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <span class="text-gray-500 mr-2">2.</span>
              <span class="text-gray-800 font-medium">您认为课程内容是否实用？</span>
            </div>
            <div class="flex space-x-2">
              <span class="tag tag-scale">收集问卷</span>
              <span class="tag tag-fill">课程评价</span>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="text-sm text-gray-500">
          <div class="flex items-center mb-1">
            <i class="fas fa-calendar-alt mr-2"></i>
            <span>创建时间：2024-04-26</span>
          </div>
          <div class="flex items-center">
            <i class="fas fa-chart-bar mr-2"></i>
            <span>使用次数：8次</span>
          </div>
        </div>
      </div>

      <div class="question-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <span class="text-gray-500 mr-2">3.</span>
              <span class="text-gray-800 font-medium">您对课程进度安排是否满意？</span>
            </div>
            <div class="flex space-x-2">
              <span class="tag tag-scale">收集问卷</span>
              <span class="tag tag-fill">课程评价</span>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="text-sm text-gray-500">
          <div class="flex items-center mb-1">
            <i class="fas fa-calendar-alt mr-2"></i>
            <span>创建时间：2024-04-25</span>
          </div>
          <div class="flex items-center">
            <i class="fas fa-chart-bar mr-2"></i>
            <span>使用次数：15次</span>
          </div>
        </div>
      </div>

      <!-- 个性化问卷题目示例 -->
      <div class="question-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <span class="text-gray-500 mr-2">4.</span>
              <span class="text-gray-800 font-medium">您希望获得哪些方面的学习支持？</span>
            </div>
            <div class="flex space-x-2">
              <span class="tag tag-text">个性化问卷</span>
              <span class="tag tag-fill">学习需求</span>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="text-sm text-gray-500">
          <div class="flex items-center mb-1">
            <i class="fas fa-calendar-alt mr-2"></i>
            <span>创建时间：2024-04-27</span>
          </div>
          <div class="flex items-center">
            <i class="fas fa-chart-bar mr-2"></i>
            <span>使用次数：5次</span>
          </div>
        </div>
      </div>

      <div class="question-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <span class="text-gray-500 mr-2">5.</span>
              <span class="text-gray-800 font-medium">您希望如何安排课后辅导时间？</span>
            </div>
            <div class="flex space-x-2">
              <span class="tag tag-text">个性化问卷</span>
              <span class="tag tag-fill">时间安排</span>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="text-sm text-gray-500">
          <div class="flex items-center mb-1">
            <i class="fas fa-calendar-alt mr-2"></i>
            <span>创建时间：2024-04-26</span>
          </div>
          <div class="flex items-center">
            <i class="fas fa-chart-bar mr-2"></i>
            <span>使用次数：3次</span>
          </div>
        </div>
      </div>

      <div class="question-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <span class="text-gray-500 mr-2">6.</span>
              <span class="text-gray-800 font-medium">您对个性化学习计划有什么建议？</span>
            </div>
            <div class="flex space-x-2">
              <span class="tag tag-text">个性化问卷</span>
              <span class="tag tag-fill">学习计划</span>
            </div>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="text-sm text-gray-500">
          <div class="flex items-center mb-1">
            <i class="fas fa-calendar-alt mr-2"></i>
            <span>创建时间：2024-04-25</span>
          </div>
          <div class="flex items-center">
            <i class="fas fa-chart-bar mr-2"></i>
            <span>使用次数：7次</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="mt-6 flex justify-center">
      <nav class="flex items-center space-x-2">
        <button class="px-3 py-1 rounded border text-gray-600 hover:bg-gray-100">上一页</button>
        <button class="px-3 py-1 rounded border bg-blue-600 text-white">1</button>
        <button class="px-3 py-1 rounded border text-gray-600 hover:bg-gray-100">2</button>
        <button class="px-3 py-1 rounded border text-gray-600 hover:bg-gray-100">3</button>
        <button class="px-3 py-1 rounded border text-gray-600 hover:bg-gray-100">下一页</button>
      </nav>
    </div>
  </div>

  <!-- 导入题目模态框 -->
  <div id="importModal" class="modal">
    <div class="modal-content">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">导入题目</h3>
        <button onclick="hideImportModal()" class="text-gray-500 hover:text-gray-700">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-2">请选择Excel文件（.xlsx）</p>
        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <input type="file" id="excelFile" accept=".xlsx" class="hidden">
          <label for="excelFile" class="cursor-pointer">
            <i class="fas fa-file-excel text-4xl text-green-600 mb-2"></i>
            <p class="text-sm text-gray-600">点击或拖拽文件到此处</p>
            <p class="text-xs text-gray-500 mt-1">仅支持Excel文件</p>
          </label>
        </div>
      </div>
      <div class="flex justify-end space-x-3">
        <button onclick="hideImportModal()" class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">取消</button>
        <button onclick="importExcel()" class="btn-primary text-white px-4 py-2 rounded-lg">导入</button>
      </div>
    </div>
  </div>

  <script>
    function showImportModal() {
      document.getElementById('importModal').style.display = 'block';
    }

    function hideImportModal() {
      document.getElementById('importModal').style.display = 'none';
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
      const modal = document.getElementById('importModal');
      if (event.target == modal) {
        hideImportModal();
      }
    }

    function importExcel() {
      const fileInput = document.getElementById('excelFile');
      if (fileInput.files.length === 0) {
        alert('请选择要导入的Excel文件');
        return;
      }
      
      const file = fileInput.files[0];
      if (!file.name.endsWith('.xlsx')) {
        alert('请选择Excel文件（.xlsx格式）');
        return;
      }

      // 这里添加文件上传逻辑
      // 可以使用FormData和fetch API发送文件到服务器
      const formData = new FormData();
      formData.append('file', file);

      // 模拟上传过程
      alert('开始导入题目...');
      hideImportModal();
    }
  </script>
</body>
</html> 