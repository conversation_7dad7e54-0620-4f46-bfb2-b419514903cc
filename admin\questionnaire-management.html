<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>问卷管理 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 250px;
      transition: all 0.3s;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background-color: #1E3A8A;
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      background-color: #1e40af;
    }
    .questionnaire-card {
      transition: all 0.3s ease;
    }
    .questionnaire-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    .status-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
    }
    .status-active {
      background-color: #d1fae5;
      color: #047857;
    }
    .status-draft {
      background-color: #f3f4f6;
      color: #4b5563;
    }
    .status-closed {
      background-color: #fee2e2;
      color: #b91c1c;
    }
    #importModal, #copyModal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
    }
    .modal-content {
      position: relative;
      background-color: white;
      margin: 15% auto;
      padding: 20px;
      width: 80%;
      max-width: 500px;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .tag-collection {
      background-color: #e0e7ff;
      color: #4338ca;
    }
    .tag-personalized {
      background-color: #d1fae5;
      color: #047857;
    }
  </style>
</head>
<body class="min-h-screen flex">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>

      <a href="results.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link active flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>

  <!-- 主内容区 -->
  <div class="flex-1 p-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">问卷管理</h1>
      <div class="flex space-x-4">
        <a href="edit-questionnaire.html" class="btn-primary text-white px-4 py-2 rounded-lg flex items-center">
          <i class="fas fa-plus mr-2"></i> 创建问卷
        </a>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white rounded-lg shadow p-4 mb-6">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1">
          <input type="text" placeholder="搜索问卷名称..." class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <select class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">所有类型</option>
          <option value="collection">收集问卷</option>
          <option value="personalized">个性化问卷</option>
        </select>
        <select class="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
          <option value="">所有状态</option>
          <option value="active">进行中</option>
          <option value="draft">草稿</option>
          <option value="closed">已结束</option>
        </select>
        <button class="btn-primary text-white px-6 py-2 rounded-lg">搜索</button>
      </div>
    </div>

    <!-- 问卷列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 收集问卷示例 -->
      <div class="questionnaire-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-800">学生课程满意度调查</h3>
            <div class="flex space-x-2 mt-2">
              <span class="tag tag-collection">收集问卷</span>
              <span class="status-badge status-active">进行中</span>
            </div>
          </div>
        </div>
        <p class="text-gray-600 mb-4">收集学生对课程内容、教学方法和教师表现的反馈意见</p>
        <div class="flex justify-between text-sm text-gray-500 mb-4">
          <span>创建时间：2023-04-15</span>
          <span>截止时间：2023-05-15</span>
        </div>
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            <span>参与人数：</span>
            <span class="font-semibold">156/200</span>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800" onclick="showCopyModal('学生课程满意度调查', 'collection')">
              <i class="fas fa-copy"></i>
            </button>
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 个性化问卷示例 -->
      <div class="questionnaire-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-800">新生入学适应情况调查</h3>
            <div class="flex space-x-2 mt-2">
              <span class="tag tag-personalized">个性化问卷</span>
              <span class="status-badge status-active">进行中</span>
            </div>
          </div>
        </div>
        <p class="text-gray-600 mb-4">针对不同专业新生定制的入学适应情况调查问卷</p>
        <div class="flex justify-between text-sm text-gray-500 mb-4">
          <span>创建时间：2023-04-10</span>
          <span>截止时间：2023-04-30</span>
        </div>
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            <span>参与人数：</span>
            <span class="font-semibold">89/120</span>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 更多问卷卡片... -->
      <div class="questionnaire-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-800">教师教学质量评估</h3>
            <div class="flex space-x-2 mt-2">
              <span class="tag tag-collection">收集问卷</span>
              <span class="status-badge status-draft">草稿</span>
            </div>
          </div>
        </div>
        <p class="text-gray-600 mb-4">全面评估教师的教学质量和课堂表现</p>
        <div class="flex justify-between text-sm text-gray-500 mb-4">
          <span>创建时间：2023-04-18</span>
          <span>截止时间：2023-05-18</span>
        </div>
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            <span>参与人数：</span>
            <span class="font-semibold">0/300</span>
          </div>
		  <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800" onclick="showCopyModal('学生课程满意度调查', 'collection')">
              <i class="fas fa-copy"></i>
            </button>
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <div class="questionnaire-card bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-800">毕业生就业意向调查</h3>
            <div class="flex space-x-2 mt-2">
              <span class="tag tag-personalized">个性化问卷</span>
              <span class="status-badge status-closed">已结束</span>
            </div>
          </div>
        </div>
        <p class="text-gray-600 mb-4">针对不同专业毕业生的就业意向和职业规划调查</p>
        <div class="flex justify-between text-sm text-gray-500 mb-4">
          <span>创建时间：2023-03-01</span>
          <span>截止时间：2023-03-31</span>
        </div>
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500">
            <span>参与人数：</span>
            <span class="font-semibold">245/245</span>
          </div>
          <div class="flex space-x-2">
            <button class="text-blue-600 hover:text-blue-800">
              <i class="fas fa-edit"></i>
            </button>
            <button class="text-red-600 hover:text-red-800">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="mt-6 flex justify-center">
      <nav class="flex items-center space-x-2">
        <button class="px-3 py-1 rounded border text-gray-600 hover:bg-gray-100">上一页</button>
        <button class="px-3 py-1 rounded border bg-blue-600 text-white">1</button>
        <button class="px-3 py-1 rounded border text-gray-600 hover:bg-gray-100">2</button>
        <button class="px-3 py-1 rounded border text-gray-600 hover:bg-gray-100">3</button>
        <button class="px-3 py-1 rounded border text-gray-600 hover:bg-gray-100">下一页</button>
      </nav>
    </div>
  </div>

  <!-- 导入问卷模态框 -->
  <div id="importModal" class="fixed inset-0 z-50 flex items-center justify-center">
    <div class="modal-overlay absolute inset-0"></div>
    <div class="bg-white rounded-lg shadow-xl w-96 relative z-10">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">导入问卷</h3>
          <button onclick="hideImportModal()" class="text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">请选择Excel文件（.xlsx）</p>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input type="file" id="excelFile" accept=".xlsx" class="hidden">
            <label for="excelFile" class="cursor-pointer">
              <i class="fas fa-file-excel text-4xl text-green-600 mb-2"></i>
              <p class="text-sm text-gray-600">点击或拖拽文件到此处</p>
              <p class="text-xs text-gray-500 mt-1">仅支持Excel文件</p>
            </label>
          </div>
        </div>
        <div class="flex justify-end space-x-3">
          <button onclick="hideImportModal()" class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">取消</button>
          <button onclick="importExcel()" class="btn-primary text-white px-4 py-2 rounded-lg">导入</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 复制问卷模态框 -->
  <div id="copyModal" class="modal">
    <div class="modal-content">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">复制问卷</h3>
        <button onclick="hideCopyModal()" class="text-gray-500 hover:text-gray-700">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="mb-4">
        <p class="text-sm text-gray-600 mb-2">请输入新问卷名称</p>
        <input type="text" id="newQuestionnaireName" class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="新问卷名称">
      </div>
      <div class="mb-4">
        <label class="flex items-center">
          <input type="checkbox" id="copyQuestions" class="mr-2" checked>
          <span class="text-sm text-gray-600">复制原问卷的所有题目</span>
        </label>
      </div>
      <div class="flex justify-end space-x-3">
        <button onclick="hideCopyModal()" class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg">取消</button>
        <button onclick="copyQuestionnaire()" class="btn-primary text-white px-4 py-2 rounded-lg">复制</button>
      </div>
    </div>
  </div>

  <script>
    function showImportModal() {
      document.getElementById('importModal').style.display = 'flex';
    }

    function hideImportModal() {
      document.getElementById('importModal').style.display = 'none';
    }

    function importExcel() {
      const fileInput = document.getElementById('excelFile');
      if (fileInput.files.length === 0) {
        alert('请选择要导入的Excel文件');
        return;
      }
      
      const file = fileInput.files[0];
      if (!file.name.endsWith('.xlsx')) {
        alert('请选择Excel文件（.xlsx格式）');
        return;
      }

      // 这里添加文件上传逻辑
      // 可以使用FormData和fetch API发送文件到服务器
      const formData = new FormData();
      formData.append('file', file);

      // 模拟上传过程
      alert('开始导入问卷...');
      hideImportModal();
    }

    function showCopyModal(originalName, type) {
      if (type !== 'collection') {
        alert('只有收集问卷可以复制！');
        return;
      }
      document.getElementById('copyModal').style.display = 'block';
      document.getElementById('newQuestionnaireName').value = originalName + ' (副本)';
    }

    function hideCopyModal() {
      document.getElementById('copyModal').style.display = 'none';
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
      const importModal = document.getElementById('importModal');
      const copyModal = document.getElementById('copyModal');
      if (event.target == importModal) {
        hideImportModal();
      }
      if (event.target == copyModal) {
        hideCopyModal();
      }
    }

    function copyQuestionnaire() {
      const newName = document.getElementById('newQuestionnaireName').value;
      const copyQuestions = document.getElementById('copyQuestions').checked;
      
      if (!newName.trim()) {
        alert('请输入新问卷名称');
        return;
      }

      // 这里添加复制问卷的逻辑
      // 1. 创建新问卷
      // 2. 如果copyQuestions为true，则复制原问卷的所有题目
      // 3. 将复制的题目绑定到新问卷
      
      alert('问卷复制成功！');
      hideCopyModal();
    }
  </script>
</body>
</html> 