<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>成绩管理 - 后台管理系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      background-color: #F8FAFC;
    }
    .sidebar {
      width: 260px;
      background: linear-gradient(180deg, #1F2937 0%, #111827 100%);
      color: #F9FAFB;
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      overflow-y: auto;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    .sidebar-link {
      color: #D1D5DB;
      transition: all 0.3s ease;
      border-left: 3px solid transparent;
      position: relative;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.05);
      color: #F9FAFB;
      border-left-color: #10B981;
    }
    .sidebar-link:hover::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 3px;
      height: 100%;
      background-color: #10B981;
    }
    .main-content {
      margin-left: 260px;
      padding: 2rem;
      min-height: 100vh;
    }
    .card {
      background-color: white;
      border-radius: 0.75rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
    }
    .card:hover {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    .btn-primary {
      background: linear-gradient(135deg, #10B981 0%, #059669 100%);
      transition: all 0.3s ease;
    }
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.2);
    }
    .btn-secondary {
      background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
      transition: all 0.3s ease;
    }
    .btn-secondary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.2);
    }
    .status-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
      font-weight: 500;
    }
    .status-passed {
      background-color: #D1FAE5;
      color: #065F46;
    }
    .status-failed {
      background-color: #FEE2E2;
      color: #B91C1C;
    }
    .status-pending {
      background-color: #FEF3C7;
      color: #92400E;
    }
    .table-row {
      transition: all 0.3s ease;
    }
    .table-row:hover {
      background-color: #F8FAFC;
      transform: translateX(4px);
    }
    .search-input {
      background-color: #F8FAFC;
      border: 1px solid #E2E8F0;
      transition: all 0.3s ease;
    }
    .search-input:focus {
      background-color: white;
      border-color: #10B981;
      box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }
    .pagination-btn {
      transition: all 0.3s ease;
    }
    .pagination-btn:hover {
      background-color: #F8FAFC;
      color: #10B981;
    }
    .pagination-btn.active {
      background-color: #10B981;
      color: white;
    }
  </style>
</head>
<body>
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>

      <a href="results.html" class="sidebar-link active flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>
  
  <!-- 主内容 -->
  <div class="flex-1 p-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">成绩管理</h1>
      
      <div class="flex space-x-2">
        <button class="btn-primary text-white px-4 py-2 rounded-md text-sm font-medium">
          <i class="fas fa-download mr-2"></i>导出成绩数据
        </button>
      </div>
    </div>
    
    <!-- 筛选区域 -->
    <div class="card p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">测评类型</label>
          <select id="assessmentType" class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <option value="">所有测评类型</option>
            <option value="exam">所有考试</option>
            <option value="questionnaire">所有问卷</option>
          </select>
        </div>

        <div id="examSelect" class="hidden">
          <label class="block text-sm font-medium text-gray-700 mb-1">考试</label>
          <select class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <option value="">选择考试</option>
            <option value="1">期中考试</option>
            <option value="2">期末考试</option>
            <option value="3">单元测试</option>
          </select>
        </div>

        <div id="questionnaireSelect" class="hidden">
          <label class="block text-sm font-medium text-gray-700 mb-1">问卷</label>
          <select class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <option value="">选择问卷</option>
            <option value="1">学生满意度调查</option>
            <option value="2">课程反馈问卷</option>
            <option value="3">教学评估问卷</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">班级</label>
          <select class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <option value="">所有班级</option>
            <option value="1">计算机科学2022-1班</option>
            <option value="2">计算机科学2022-2班</option>
            <option value="3">软件工程2022-1班</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">成绩状态</label>
          <select class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <option value="">所有状态</option>
            <option value="passed">通过</option>
            <option value="failed">未通过</option>
            <option value="pending">待批阅</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
          <div class="flex space-x-2">
            <input type="date" class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <input type="date" class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
          </div>
        </div>
      </div>
      
      <div class="mt-4 flex items-center justify-between">
        <div class="relative w-64">
          <input type="text" placeholder="搜索学生姓名/学号..." class="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
        
        <button class="btn-primary text-white px-4 py-2 rounded-md text-sm font-medium">
          <i class="fas fa-filter mr-2"></i>筛选
        </button>
      </div>
    </div>
    
    
    <!-- 成绩列表 -->
    <div class="card overflow-hidden mb-6">
      <div class="flex justify-between items-center p-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-800">成绩列表</h2>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">批量操作</button>
          <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">
            <i class="fas fa-cog"></i>
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                学生信息
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                考试
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                完成时间
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用时
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                分数
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">王小明</div>
                    <div class="text-sm text-gray-500">学号: 2022010101</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">高等数学期中考试</div>
                <div class="text-sm text-gray-500">必修课程</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-04-15 10:45</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">01:35:22</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-green-600">98.5/100</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-passed">通过</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">详情</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">修改</a>
              </td>
            </tr>
            
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/44.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">李小红</div>
                    <div class="text-sm text-gray-500">学号: 2022010102</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">高等数学期中考试</div>
                <div class="text-sm text-gray-500">必修课程</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-04-15 11:20</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">01:42:15</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-red-600">58/100</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-failed">未通过</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">详情</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">修改</a>
              </td>
            </tr>
            
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/45.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">张大山</div>
                    <div class="text-sm text-gray-500">学号: 2022010103</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">程序设计期末考试</div>
                <div class="text-sm text-gray-500">必修课程</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-04-18 16:30</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">01:58:42</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-500">-- / 100</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-pending">待批阅</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">批阅</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">详情</a>
              </td>
            </tr>
            
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/22.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">陈小华</div>
                    <div class="text-sm text-gray-500">学号: 2022010104</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">大学物理单元测试</div>
                <div class="text-sm text-gray-500">必修课程</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">2023-04-12 15:30</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">00:42:18</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-green-600">45/50</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-passed">通过</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">详情</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">修改</a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
          <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            上一页
          </a>
          <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            下一页
          </a>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 的 <span class="font-medium">12</span> 条结果
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <i class="fas fa-chevron-left"></i>
              </a>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-green-50 text-sm font-medium text-green-600 hover:bg-green-100">
                1
              </a>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                2
              </a>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                3
              </a>
              <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <i class="fas fa-chevron-right"></i>
              </a>
            </nav>
          </div>
        </div>
      </div>
    </div>
    
    </div>
  </div>
</body>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const assessmentType = document.getElementById('assessmentType');
    const examSelect = document.getElementById('examSelect');
    const questionnaireSelect = document.getElementById('questionnaireSelect');

    assessmentType.addEventListener('change', function() {
      if (this.value === 'exam') {
        examSelect.classList.remove('hidden');
        questionnaireSelect.classList.add('hidden');
      } else if (this.value === 'questionnaire') {
        examSelect.classList.add('hidden');
        questionnaireSelect.classList.remove('hidden');
      } else {
        examSelect.classList.add('hidden');
        questionnaireSelect.classList.add('hidden');
      }
    });
  });
</script>
</html> 