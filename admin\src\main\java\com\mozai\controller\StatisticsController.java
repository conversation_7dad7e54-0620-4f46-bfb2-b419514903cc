package com.mozai.controller;

import com.mozai.dto.StatisticsQueryDTO;
import com.mozai.entity.Statistics;
import com.mozai.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;

    /**
     * 查询统计数据
     * @param queryDTO 查询参数
     * @return 统计列表
     */
    @PostMapping("/query")
    public List<Statistics> queryStatistics(@RequestBody StatisticsQueryDTO queryDTO) {
        return statisticsService.queryStatistics(queryDTO);
    }

    /**
     * 获取成绩分布数据
     * @param queryDTO 查询参数
     * @return 成绩分布数据
     */
    @PostMapping("/score-distribution")
    public List<Map<String, Object>> getScoreDistribution(@RequestBody StatisticsQueryDTO queryDTO) {
        return statisticsService.getScoreDistribution(queryDTO);
    }

    /**
     * 获取通过率数据
     * @param queryDTO 查询参数
     * @return 通过率数据
     */
    @PostMapping("/pass-rate")
    public List<Map<String, Object>> getPassRate(@RequestBody StatisticsQueryDTO queryDTO) {
        return statisticsService.getPassRate(queryDTO);
    }
} 