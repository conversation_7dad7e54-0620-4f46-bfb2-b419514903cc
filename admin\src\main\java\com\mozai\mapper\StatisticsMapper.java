package com.mozai.mapper;

import com.mozai.entity.Statistics;
import com.mozai.dto.StatisticsQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

@Mapper
public interface StatisticsMapper {
    /**
     * 查询统计数据
     * @param queryDTO 查询参数
     * @return 统计列表
     */
    List<Statistics> queryStatistics(@Param("query") StatisticsQueryDTO queryDTO);

    /**
     * 获取成绩分布数据
     * @param queryDTO 查询参数
     * @return 成绩分布数据
     */
    List<Statistics> getScoreDistribution(@Param("query") StatisticsQueryDTO queryDTO);

    /**
     * 获取通过率数据
     * @param queryDTO 查询参数
     * @return 通过率数据
     */
    List<Statistics> getPassRate(@Param("query") StatisticsQueryDTO queryDTO);
} 