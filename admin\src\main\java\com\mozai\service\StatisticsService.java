package com.mozai.service;

import com.mozai.entity.Statistics;
import com.mozai.dto.StatisticsQueryDTO;
import java.util.List;
import java.util.Map;

public interface StatisticsService {
    /**
     * 查询统计数据
     * @param queryDTO 查询参数
     * @return 统计列表
     */
    List<Statistics> queryStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取成绩分布数据
     * @param queryDTO 查询参数
     * @return 成绩分布数据
     */
    List<Map<String, Object>> getScoreDistribution(StatisticsQueryDTO queryDTO);

    /**
     * 获取通过率数据
     * @param queryDTO 查询参数
     * @return 通过率数据
     */
    List<Map<String, Object>> getPassRate(StatisticsQueryDTO queryDTO);
} 