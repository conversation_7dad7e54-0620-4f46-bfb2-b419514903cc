package com.mozai.service.impl;

import com.mozai.entity.Statistics;
import com.mozai.dto.StatisticsQueryDTO;
import com.mozai.mapper.StatisticsMapper;
import com.mozai.service.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;

@Service
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Override
    public List<Statistics> queryStatistics(StatisticsQueryDTO queryDTO) {
        return statisticsMapper.queryStatistics(queryDTO);
    }

    @Override
    public List<Map<String, Object>> getScoreDistribution(StatisticsQueryDTO queryDTO) {
        return statisticsMapper.getScoreDistribution(queryDTO);
    }

    @Override
    public List<Map<String, Object>> getPassRate(StatisticsQueryDTO queryDTO) {
        return statisticsMapper.getPassRate(queryDTO);
    }
} 