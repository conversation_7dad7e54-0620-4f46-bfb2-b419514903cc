<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mozai.mapper.StatisticsMapper">
    <resultMap id="BaseResultMap" type="com.mozai.entity.Statistics">
        <id column="id" property="id"/>
        <result column="student_name" property="studentName"/>
        <result column="class_name" property="className"/>
        <result column="assessment_name" property="assessmentName"/>
        <result column="scores" property="scores"/>
        <result column="complete_time" property="completeTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, student_name, class_name, assessment_name, scores, complete_time
    </sql>

    <select id="queryStatistics" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM statistics
        <where>
            <if test="query.className != null and query.className != ''">
                AND class_name LIKE CONCAT('%', #{query.className}, '%')
            </if>
            <if test="query.studentName != null and query.studentName != ''">
                AND student_name LIKE CONCAT('%', #{query.studentName}, '%')
            </if>
            <if test="query.assessmentName != null and query.assessmentName != ''">
                AND assessment_name LIKE CONCAT('%', #{query.assessmentName}, '%')
            </if>
            <if test="query.homeworkName != null and query.homeworkName != ''">
                AND assessment_name LIKE CONCAT('%', #{query.homeworkName}, '%')
            </if>
            <if test="query.questionnaireName != null and query.questionnaireName != ''">
                AND assessment_name LIKE CONCAT('%', #{query.questionnaireName}, '%')
            </if>
        </where>
        ORDER BY complete_time DESC
    </select>

    <select id="getScoreDistribution" resultType="java.util.Map">
        SELECT
            CASE
                WHEN scores &lt; 60 THEN '0-59'
                WHEN scores &lt; 70 THEN '60-69'
                WHEN scores &lt; 80 THEN '70-79'
                WHEN scores &lt; 90 THEN '80-89'
                ELSE '90-100'
            END as score_range,
            COUNT(*) as count
        FROM statistics
        <where>
            <if test="query.className != null and query.className != ''">
                AND class_name LIKE CONCAT('%', #{query.className}, '%')
            </if>
            <if test="query.studentName != null and query.studentName != ''">
                AND student_name LIKE CONCAT('%', #{query.studentName}, '%')
            </if>
            <if test="query.assessmentName != null and query.assessmentName != ''">
                AND assessment_name LIKE CONCAT('%', #{query.assessmentName}, '%')
            </if>
        </where>
        GROUP BY score_range
        ORDER BY score_range
    </select>

    <select id="getPassRate" resultType="java.util.Map">
        SELECT
            CASE
                WHEN scores &gt;= 60 THEN '通过'
                ELSE '未通过'
            END as status,
            COUNT(*) as count
        FROM statistics
        <where>
            <if test="query.className != null and query.className != ''">
                AND class_name LIKE CONCAT('%', #{query.className}, '%')
            </if>
            <if test="query.studentName != null and query.studentName != ''">
                AND student_name LIKE CONCAT('%', #{query.studentName}, '%')
            </if>
            <if test="query.assessmentName != null and query.assessmentName != ''">
                AND assessment_name LIKE CONCAT('%', #{query.assessmentName}, '%')
            </if>
        </where>
        GROUP BY status
    </select>
</mapper> 