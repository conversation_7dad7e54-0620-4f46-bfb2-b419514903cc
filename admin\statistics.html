<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>统计管理 - 学生答题系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      background-color: #1E3A8A;
      width: 280px;
      transition: all 0.3s;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
      z-index: 10;
    }
    .sidebar-link {
      color: #E5E7EB;
      transition: all 0.2s;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: rgba(255, 255, 255, 0.1);
      color: white;
    }
    .sidebar-link.active {
      border-left: 4px solid #F59E0B;
    }
    .main-content {
      margin-left: 280px;
      width: calc(100% - 280px);
      max-width: 100%;
      padding: 2rem;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      transition: all 0.3s;
    }
    .card:hover {
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }
    .icon-box {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12px;
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="px-6 py-5 flex items-center border-b border-blue-800">
      <i class="fas fa-graduation-cap text-2xl text-white mr-3"></i>
      <h1 class="text-white text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="pt-5">
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        主菜单
      </div>
      
      <a href="dashboard.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tachometer-alt mr-3"></i> 仪表盘
      </a>
      
      <a href="user-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-users mr-3"></i> 用户管理
      </a>

      <a href="results.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-chart-bar mr-3"></i> 成绩管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        考试管理
      </div>
      
      <a href="exam-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-file-alt mr-3"></i> 考试列表
      </a>
      
      <a href="homework-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-tasks mr-3"></i> 作业管理
      </a>
      
      <a href="question-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        问卷管理
      </div>
      
      <a href="questionnaire-management.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-clipboard-list mr-3"></i> 问卷列表
      </a>
      
      <a href="questionnaire-bank.html" class="sidebar-link flex items-center px-6 py-3 ml-6">
        <i class="fas fa-book mr-3"></i> 题库管理
      </a>
      
      <div class="px-4 py-3 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        统计管理
      </div>
      
      <a href="statistics.html" class="sidebar-link active flex items-center px-6 py-3">
        <i class="fas fa-chart-pie mr-3"></i> 统计分析
      </a>
      
      <div class="px-4 py-3 mt-6 text-xs font-semibold text-gray-300 uppercase tracking-wider">
        系统
      </div>
      
      <a href="#" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-cog mr-3"></i> 系统设置
      </a>
      
      <a href="login.html" class="sidebar-link flex items-center px-6 py-3">
        <i class="fas fa-sign-out-alt mr-3"></i> 退出登录
      </a>
    </div>
  </div>
  
  <!-- 主内容区 -->
  <div class="main-content">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-800">统计管理</h1>
        <p class="text-gray-600">查看和分析各类数据统计</p>
      </div>
      
      <div class="flex items-center">
        <div class="relative mr-4">
          <i class="fas fa-bell text-gray-500 text-xl"></i>
          <span class="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500"></span>
        </div>
        
        <div class="flex items-center">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="管理员头像" class="h-10 w-10 rounded-full">
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-700">张老师</p>
            <p class="text-xs text-gray-500">超级管理员</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 查询条件区域 -->
    <div class="card p-5 mb-8">
      <h3 class="font-semibold text-gray-700 mb-4">查询条件</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">班级</label>
          <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="请输入班级名称">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">学生</label>
          <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="请输入学生姓名">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">考试</label>
          <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="请输入考试名称">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">作业</label>
          <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="请输入作业名称">
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">问卷</label>
          <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" placeholder="请输入问卷名称">
        </div>
      </div>
      <div class="mt-4 flex justify-end">
        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          查询
        </button>
      </div>
    </div>
    
    
    <!-- 详细数据表格 -->
    <div class="card p-5">
      <h3 class="font-semibold text-gray-700 mb-4">详细数据</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学生姓名</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">班级</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">测评名称</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成绩</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">王小明</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">计算机科学与技术1班</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">期中考试</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">95</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2023-04-15 10:30</td>
            </tr>
            <tr>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">李华</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">软件工程2班</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">期中考试</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">92</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2023-04-15 11:15</td>
            </tr>
            <tr>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">张雨</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">人工智能3班</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">期中考试</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">88</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2023-04-15 09:45</td>
            </tr>
			 <tr>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">张雨</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">人工智能3班</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">期中考试</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">88</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2023-04-15 09:45</td>
            </tr>
			 <tr>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">张雨</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">人工智能3班</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">期中考试</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">88</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2023-04-15 09:45</td>
            </tr>
			 <tr>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">张雨</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">人工智能3班</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">期中考试</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">88</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2023-04-15 09:45</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <script>
    // 成绩分布图
    const scoreDistributionCtx = document.getElementById('scoreDistributionChart').getContext('2d');
    new Chart(scoreDistributionCtx, {
      type: 'bar',
      data: {
        labels: ['0-59', '60-69', '70-79', '80-89', '90-100'],
        datasets: [{
          label: '人数',
          data: [5, 10, 15, 20, 10],
          backgroundColor: [
            'rgba(239, 68, 68, 0.8)',
            'rgba(249, 115, 22, 0.8)',
            'rgba(234, 179, 8, 0.8)',
            'rgba(34, 197, 94, 0.8)',
            'rgba(59, 130, 246, 0.8)'
          ],
          borderColor: [
            'rgba(239, 68, 68, 1)',
            'rgba(249, 115, 22, 1)',
            'rgba(234, 179, 8, 1)',
            'rgba(34, 197, 94, 1)',
            'rgba(59, 130, 246, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 5
            }
          }
        }
      }
    });

    // 考试通过率图
    const passRateCtx = document.getElementById('passRateChart').getContext('2d');
    new Chart(passRateCtx, {
      type: 'doughnut',
      data: {
        labels: ['通过', '未通过'],
        datasets: [{
          data: [75, 25],
          backgroundColor: [
            'rgba(34, 197, 94, 0.8)',
            'rgba(239, 68, 68, 0.8)'
          ],
          borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(239, 68, 68, 1)'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          }
        }
      }
    });
  </script>
</body>
</html> 