<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>用户管理 - 后台管理系统</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #F3F4F6;
    }
    .sidebar {
      width: 260px;
      background-color: #1F2937;
      color: #F9FAFB;
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      overflow-y: auto;
    }
    .sidebar-link {
      color: #D1D5DB;
      transition: all 0.2s;
      border-left: 3px solid transparent;
    }
    .sidebar-link:hover, .sidebar-link.active {
      background-color: #374151;
      color: #F9FAFB;
      border-left-color: #10B981;
    }
    .main-content {
      margin-left: 260px;
      padding: 1.5rem;
    }
    .card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .btn-primary {
      background-color: #10B981;
      transition: all 0.2s;
    }
    .btn-primary:hover {
      background-color: #059669;
    }
    .btn-secondary {
      background-color: #6B7280;
      transition: all 0.2s;
    }
    .btn-secondary:hover {
      background-color: #4B5563;
    }
    .status-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
    }
    .status-active {
      background-color: #D1FAE5;
      color: #065F46;
    }
    .status-inactive {
      background-color: #FEE2E2;
      color: #B91C1C;
    }
    .status-pending {
      background-color: #FEF3C7;
      color: #92400E;
    }
    .table-row {
      transition: all 0.2s;
    }
    .table-row:hover {
      background-color: #F9FAFB;
    }
    .user-role {
      font-size: 0.75rem;
      padding: 0.15rem 0.5rem;
      border-radius: 0.25rem;
    }
    .role-admin {
      background-color: #FEE2E2;
      color: #B91C1C;
    }
    .role-teacher {
      background-color: #E0E7FF;
      color: #4338CA;
    }
    .role-student {
      background-color: #D1FAE5;
      color: #065F46;
    }
  </style>
</head>
<body>
  <!-- 侧边栏 -->
  <div class="sidebar">
    <div class="p-4 flex items-center border-b border-gray-700">
      <i class="fas fa-graduation-cap text-2xl text-green-500 mr-3"></i>
      <h1 class="text-xl font-bold">答题系统后台</h1>
    </div>
    
    <div class="mt-4">
      <a href="dashboard.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-tachometer-alt w-6"></i>
        <span>控制面板</span>
      </a>
      
      <a href="user-management.html" class="sidebar-link active px-4 py-3 flex items-center">
        <i class="fas fa-users w-6"></i>
        <span>用户管理</span>
      </a>
      
      <a href="question-bank.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-question-circle w-6"></i>
        <span>题库管理</span>
      </a>
      
      <a href="exam-management.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-file-alt w-6"></i>
        <span>考试管理</span>
      </a>

      <div class="px-4 py-2">
        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider">问卷管理</div>
        <div class="mt-2 space-y-1">
          <a href="questionnaire-management.html" class="sidebar-link px-4 py-2 flex items-center">
            <i class="fas fa-clipboard-list w-6"></i>
            <span>问卷管理</span>
          </a>
          <a href="questionnaire-bank.html" class="sidebar-link px-4 py-2 flex items-center">
            <i class="fas fa-book w-6"></i>
            <span>问卷库</span>
          </a>
        </div>
      </div>
      
      <a href="results.html" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-chart-bar w-6"></i>
        <span>成绩管理</span>
      </a>
      
      <a href="#" class="sidebar-link px-4 py-3 flex items-center">
        <i class="fas fa-cog w-6"></i>
        <span>系统设置</span>
      </a>
    </div>
  </div>
  
  <!-- 主内容 -->
  <div class="main-content">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">用户管理</h1>
      
      <div class="flex space-x-2">
        <button class="btn-primary text-white px-4 py-2 rounded-md text-sm font-medium">
          <i class="fas fa-plus mr-2"></i>添加用户
        </button>
        <button class="btn-secondary text-white px-4 py-2 rounded-md text-sm font-medium">
          <i class="fas fa-upload mr-2"></i>批量导入
        </button>
      </div>
    </div>
    
    <!-- 用户统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="card p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">总用户</p>
            <h3 class="text-xl font-bold text-gray-800">2,541</h3>
          </div>
          <div class="bg-blue-100 p-3 rounded-full">
            <i class="fas fa-users text-blue-600"></i>
          </div>
        </div>
      </div>
      
      <div class="card p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">学生用户</p>
            <h3 class="text-xl font-bold text-gray-800">2,345</h3>
          </div>
          <div class="bg-green-100 p-3 rounded-full">
            <i class="fas fa-user-graduate text-green-600"></i>
          </div>
        </div>
      </div>
      
      <div class="card p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">教师用户</p>
            <h3 class="text-xl font-bold text-gray-800">189</h3>
          </div>
          <div class="bg-purple-100 p-3 rounded-full">
            <i class="fas fa-chalkboard-teacher text-purple-600"></i>
          </div>
        </div>
      </div>
      
      <div class="card p-4">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">管理员</p>
            <h3 class="text-xl font-bold text-gray-800">7</h3>
          </div>
          <div class="bg-red-100 p-3 rounded-full">
            <i class="fas fa-user-shield text-red-600"></i>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 筛选区域 -->
    <div class="card p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">用户角色</label>
          <select class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <option value="">所有角色</option>
            <option value="admin">管理员</option>
            <option value="teacher">教师</option>
            <option value="student">学生</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">班级/部门</label>
          <select class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <option value="">所有班级/部门</option>
            <option value="cs2022">计算机科学2022级</option>
            <option value="cs2023">计算机科学2023级</option>
            <option value="se2022">软件工程2022级</option>
            <option value="math">数学教研室</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <select class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <option value="">所有状态</option>
            <option value="active">在线</option>
            <option value="inactive">离线</option>
            <option value="pending">待激活</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">注册日期</label>
          <div class="flex space-x-2">
            <input type="date" class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
            <input type="date" class="w-full border border-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
          </div>
        </div>
      </div>
      
      <div class="mt-4 flex items-center justify-between">
        <div class="relative w-64">
          <input type="text" placeholder="搜索用户名/ID..." class="w-full border border-gray-300 rounded-md py-2 pl-10 pr-4 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
          <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
        
        <button class="btn-primary text-white px-4 py-2 rounded-md text-sm font-medium">
          <i class="fas fa-filter mr-2"></i>筛选
        </button>
      </div>
    </div>
    
    <!-- 用户列表 -->
    <div class="card overflow-hidden mb-6">
      <div class="flex justify-between items-center p-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-800">用户列表</h2>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">批量操作</button>
          <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">
            <i class="fas fa-cog"></i>
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户信息
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                角色
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                班级/部门
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                邮箱
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                注册日期
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <!-- 管理员 -->
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/1.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">陈大明</div>
                    <div class="text-sm text-gray-500">ID: admin001</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="user-role role-admin">管理员</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">系统管理</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500"><EMAIL></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-01-15</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-active">在线</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">禁用</a>
              </td>
            </tr>
            
            <!-- 教师 -->
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/67.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">李教授</div>
                    <div class="text-sm text-gray-500">ID: teacher101</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="user-role role-teacher">教师</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">数学教研室</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500"><EMAIL></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-02-10</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-active">在线</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">禁用</a>
              </td>
            </tr>
            
            <!-- 学生1 -->
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">王小明</div>
                    <div class="text-sm text-gray-500">ID: 2022010101</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="user-role role-student">学生</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">计算机科学2022-1班</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500"><EMAIL></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2022-09-01</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-active">在线</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">禁用</a>
              </td>
            </tr>
            
            <!-- 学生2 -->
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/44.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">李小红</div>
                    <div class="text-sm text-gray-500">ID: 2022010102</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="user-role role-student">学生</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">计算机科学2022-1班</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500"><EMAIL></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2022-09-01</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-inactive">离线</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">禁用</a>
              </td>
            </tr>
            
            <!-- 新学生 -->
            <tr class="table-row">
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="h-4 w-4 rounded border-gray-300 text-green-500 focus:ring-green-500">
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/45.jpg" alt="">
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">张大山</div>
                    <div class="text-sm text-gray-500">ID: 2023010101</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="user-role role-student">学生</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">计算机科学2023-1班</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500"><EMAIL></div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-500">2023-09-01</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="status-badge status-pending">待激活</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a href="#" class="text-blue-600 hover:text-blue-900 mr-3">编辑</a>
                <a href="#" class="text-gray-600 hover:text-gray-900">激活</a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
          <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            上一页
          </a>
          <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            下一页
          </a>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 的 <span class="font-medium">2,541</span> 条结果
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <i class="fas fa-chevron-left"></i>
              </a>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-green-50 text-sm font-medium text-green-600 hover:bg-green-100">
                1
              </a>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                2
              </a>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                3
              </a>
              <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
              <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                254
              </a>
              <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                <i class="fas fa-chevron-right"></i>
              </a>
            </nav>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 用户活跃度统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="card p-4">
        <h3 class="text-lg font-medium text-gray-800 mb-4">用户活跃度</h3>
        <div class="h-64 flex items-end justify-between space-x-2">
          <div class="flex flex-col items-center">
            <div class="w-10 bg-blue-500 rounded-t-md" style="height: 30%"></div>
            <p class="text-xs text-gray-500 mt-1">周一</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="w-10 bg-blue-500 rounded-t-md" style="height: 60%"></div>
            <p class="text-xs text-gray-500 mt-1">周二</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="w-10 bg-blue-500 rounded-t-md" style="height: 45%"></div>
            <p class="text-xs text-gray-500 mt-1">周三</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="w-10 bg-blue-500 rounded-t-md" style="height: 75%"></div>
            <p class="text-xs text-gray-500 mt-1">周四</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="w-10 bg-blue-500 rounded-t-md" style="height: 65%"></div>
            <p class="text-xs text-gray-500 mt-1">周五</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="w-10 bg-blue-500 rounded-t-md" style="height: 25%"></div>
            <p class="text-xs text-gray-500 mt-1">周六</p>
          </div>
          <div class="flex flex-col items-center">
            <div class="w-10 bg-blue-500 rounded-t-md" style="height: 15%"></div>
            <p class="text-xs text-gray-500 mt-1">周日</p>
          </div>
        </div>
      </div>
      
      <div class="card p-4">
        <h3 class="text-lg font-medium text-gray-800 mb-4">用户类型分布</h3>
        <div class="flex items-center justify-center h-64">
          <div class="flex flex-col items-center mr-8">
            <div class="w-32 h-32 rounded-full border-8 border-green-500 flex items-center justify-center relative">
              <div class="text-xl font-bold text-gray-800">92.3%</div>
            </div>
            <p class="mt-2 text-sm font-medium text-gray-600">学生</p>
          </div>
          
          <div class="flex flex-col space-y-4">
            <div class="flex items-center">
              <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600">学生: 92.3%</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600">教师: 7.4%</span>
            </div>
            <div class="flex items-center">
              <div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600">管理员: 0.3%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>