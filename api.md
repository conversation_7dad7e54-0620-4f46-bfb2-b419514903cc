# 学生答题系统接口文档

## 1. 认证模块

### 1.1 管理员登录
```typescript
/**
 * 管理员登录接口
 * @description 管理员通过用户名和密码进行登录
 */
POST /api/auth/admin/login

请求参数:
{
  username: string,  // 必传 - 管理员用户名
  password: string   // 必传 - 管理员密码
}

响应:
{
  code: number,      // 状态码：200成功，其他失败
  message: string,   // 状态信息
  data: {
    token: string,    // JWT token，用于后续接口认证
    adminId: string,  // 管理员ID
    username: string, // 管理员用户名
    role: string      // 角色：admin
  }
}
```

### 1.2 学生登录
```typescript
/**
 * 学生登录接口
 * @description 学生通过学号和密码进行登录
 */
POST /api/auth/student/login

请求参数:
{
  studentId: string,    // 必传 - 学号
  password: string,     // 必传 - 密码
  rememberMe: boolean   // 非必传 - 是否记住登录状态
}

响应:
{
  code: number,         // 状态码：200成功，其他失败
  message: string,      // 状态信息
  data: {
    token: string,      // JWT token，用于后续接口认证
    studentId: string,  // 学号
    name: string,       // 学生姓名
    class: string,      // 班级
    role: string        // 角色：student
  }
}
```

## 2. 仪表盘模块

### 2.1 获取管理员仪表盘数据
```typescript
/**
 * 获取管理员仪表盘数据
 * @description 获取系统概览数据，包括学生数量、考试数量、题目数量等
 */
GET /api/admin/dashboard

请求参数: 无

响应:
{
  code: number,      // 状态码
  message: string,   // 状态信息
  data: {
    totalStudents: number,    // 学生总数
    totalExams: number,       // 进行中考试数
    totalQuestions: number,   // 题库总数
    averageScore: number,     // 平均分数
    recentExams: Array<{      // 最近考试列表
      examId: string,         // 考试ID
      title: string,          // 考试名称
      subject: string,        // 课程名称
      startTime: string,      // 开始时间
      status: string,         // 状态：进行中/已结束/未开始
      participants: number    // 参与人数
    }>,
    recentTop5: Array<{       // 排名TOP5
      studentId: string,      // 学号
      name: string,           // 学生名称
      avatar: string,         // 头像URL
      scores: number           // 成绩
    }>
  }
}
```

### 2.2 获取学生仪表盘数据
```typescript
/**
 * 获取学生仪表盘数据
 * @description 获取学生个人学习数据概览
 */
GET /api/student/dashboard

请求参数: 无

响应:
{
  code: number,      // 状态码
  message: string,   // 状态信息
  data: {
    upcomingExams: Array<{  // 即将开始的考试
      examId: string,       // 考试ID
      title: string,        // 考试标题
      subject: string,      // 科目名称
      startTime: string,    // 开始时间
      endTime: string       // 结束时间
    }>,
    recentResults: Array<{  // 进行中考试
      examId: string,       // 考试ID
      title: string,        // 考试标题
      subject: string,      // 科目名称
      startTime: string,    // 开始时间
      endTime: string,      // 结束时间
      duration: number,     // 时长（分钟）
      totalScore: number,   // 总分
      remainingTime: string // 剩余时长
    }>,
    recentlyCompleted: Array<{  // 最近完成的考试
      examId: string,       // 考试ID
      title: string,        // 考试标题
      subject: string,      // 科目名称
      finishTime: string,   // 完成时间
      scores: number         // 分数
    }>,
    statistics: {           // 学习统计
      completedExams: number, // 已完成考试数
      averageScore: number,  // 平均分
      isComing: number      // 即将开始的考试数
    }
  }
}
```

## 3. 用户管理模块

### 3.1 获取用户列表
```typescript
/**
 * 获取用户列表
 * @description 分页获取用户信息，支持搜索和筛选
 */
GET /api/admin/users

请求参数:
{
  page: number,          // 必传 - 页码，默认1
  size: number,          // 必传 - 每页大小，默认10
  role: string,          // 非必传 - 用户角色：student/teacher/admin
  class: string,         // 非必传 - 班级筛选
  status: string,        // 非必传 - 状态筛选：active/inactive
  startTime: string,     // 非必传 - 开始时间范围
  endTime: string,       // 非必传 - 结束时间范围
  name: string           // 非必传 - 用户名
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    totalNum: number,    // 总用户数
    studentNum: number,  // 学生用户数
    teacherNum: number,  // 教师用户数
    adminNum: number,    // 管理员数
    users: Array<{
      userId: string,    // 用户ID
      name: string,      // 姓名
      class: string,     // 班级
      role: string,      // 用户角色
      email: string,     // 邮箱
      createTime: string,// 注册日期
      status: string     // 状态
    }>,
    userTypes: {         // 用户类型分布（百分比）
      student: number,   // 学生占比
      teacher: number,   // 教师占比
      admin: number      // 管理员占比
    }
  }
}
```

### 3.2 添加用户
```typescript
/**
 * 添加新用户
 * @description 管理员添加新的用户账号
 */
POST /api/admin/users

请求参数:
{
  userId: string,        // 必传 - 用户ID（学号/工号）
  name: string,          // 必传 - 姓名
  password: string,      // 必传 - 密码
  class: string,         // 必传 - 班级
  role: string,          // 必传 - 角色：student/teacher/admin
  email: string,         // 非必传 - 邮箱
  phone: string          // 非必传 - 手机号
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    userId: string       // 用户ID
  }
}
```

### 3.3 更新用户信息
```typescript
/**
 * 更新用户信息
 * @description 更新指定用户的信息
 */
PUT /api/admin/users/{userId}

请求参数:
{
  name: string,          // 非必传 - 姓名
  class: string,         // 非必传 - 班级
  status: string,        // 非必传 - 状态
  email: string,         // 非必传 - 邮箱
  phone: string          // 非必传 - 手机号
}

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

### 3.4 删除用户
```typescript
/**
 * 删除用户
 * @description 删除指定用户账号
 */
DELETE /api/admin/users/{userId}

请求参数: 无

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

## 4. 题库管理模块

### 4.1 获取题目列表
```typescript
/**
 * 获取题目列表
 * @description 分页获取题目信息，支持多种筛选条件
 */
GET /api/admin/questions

请求参数:
{
  page: number,          // 非必传 - 页码，默认1
  size: number,          // 非必传 - 每页大小，默认10
  content: string,       // 非必传 - 题目内容
  type: string,          // 非必传 - 题目类型：single/multiple/fill/variable/essay
  difficulty: string,    // 非必传 - 难度：easy/medium/hard
  subject: string        // 非必传 - 科目
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    total: number,       // 总题目数
    questions: Array<{
      questionId: string, // 题目ID
      type: string,       // 题型
      content: string,    // 题目内容
      difficulty: string, // 难易程度
      subject: string,    // 学科
      createTime: string  // 创建时间
    }>
  }
}
```

### 4.2 创建题目
```typescript
/**
 * 创建新题目
 * @description 创建新的题目
 */
POST /api/admin/questions

请求参数:
{
  content: string,       // 必传 - 题目内容
  type: string,          // 必传 - 题目类型
  subject: string,       // 必传 - 科目
  difficulty: string,    // 必传 - 难度
  label: string,         // 必传 - 标签
  points: number,        // 必传 - 分值
  options: Array<{       // 必传（选择题） - 选项
    key: string,         // 选项键
    value: string        // 选项值
  }>,
  answer: string,        // 必传 - 答案
  analysis: string,      // 非必传 - 解析
  answeringTime: string, // 非必传 - 答题时间限制（秒）
  topicClassification: string,  // 非必传 - 题目分类
  randomly: boolean      // 非必传 - 随机打乱选项顺序
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    questionId: string   // 题目ID
  }
}
```

### 4.3 更新题目
```typescript
/**
 * 更新题目
 * @description 更新指定题目的信息
 */
PUT /api/admin/questions/{questionId}

请求参数:
{
  content: string,       // 非必传 - 题目内容
  type: string,          // 非必传 - 题目类型
  subject: string,       // 非必传 - 科目
  difficulty: string,    // 非必传 - 难度
  label: string,         // 非必传 - 标签
  points: number,        // 非必传 - 分值
  options: Array<{       // 非必传（选择题） - 选项
    key: string,         // 选项键
    value: string        // 选项值
  }>,
  answer: string,        // 非必传 - 答案
  analysis: string,      // 非必传 - 解析
  answeringTime: string, // 非必传 - 答题时间限制（秒）
  topicClassification: string,  // 非必传 - 题目分类
  randomly: boolean      // 非必传 - 随机打乱选项顺序
}

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

### 4.4 删除题目
```typescript
/**
 * 删除题目
 * @description 删除指定题目
 */
DELETE /api/admin/questions/{questionId}

请求参数: 无

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

## 5. 考试管理模块

### 5.1 获取考试列表
```typescript
/**
 * 获取考试列表
 * @description 分页获取考试信息，支持多种筛选条件
 */
GET /api/admin/exams

请求参数:
{
  page: number,          // 必传 - 页码，默认1
  size: number,          // 必传 - 每页大小，默认10
  status: string,        // 非必传 - 状态：upcoming/ongoing/ended
  subject: string,       // 非必传 - 学科
  title: string          // 非必传 - 考试名称
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    total: number,       // 总考试数
    exams: Array<{
      examId: string,    // 考试ID
      title: string,     // 考试标题
      subject: string,   // 学科
      startTime: string, // 开始时间
      endTime: string,   // 结束时间
      status: string,    // 状态
      totalScore: number,// 总分
      questionCount: number // 题目数量
    }>
  }
}
```

### 5.2 创建考试
```typescript
/**
 * 创建新考试
 * @description 创建新的考试
 */
POST /api/admin/exams

请求参数:
{
  title: string,         // 必传 - 考试名称
  description: string,   // 非必传 - 考试描述
  subject: string,       // 必传 - 学科
  startTime: string,     // 必传 - 开始时间
  endTime: string,       // 必传 - 结束时间
  duration: number,      // 必传 - 考试时长（分钟）
  passingMark: number,   // 必传 - 及格分数
  status: string,        // 必传 - 考试状态
  questions: Array<{     // 必传 - 考试题目
    questionId: string,  // 题目ID
    scores: number        // 分值
  }>,
  allowedClasses: Array<string>  // 非必传 - 允许参加的班级
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    examId: string       // 考试ID
  }
}
```

### 5.3 更新考试
```typescript
/**
 * 更新考试
 * @description 更新指定考试的信息
 */
PUT /api/admin/exams/{examId}

请求参数:
{
  title: string,         // 非必传 - 考试标题
  description: string,   // 非必传 - 考试描述
  startTime: string,     // 非必传 - 开始时间
  endTime: string,       // 非必传 - 结束时间
  duration: number,      // 非必传 - 考试时长
  questions: Array<{     // 非必传 - 考试题目
    questionId: string,  // 题目ID
    scores: number        // 分值
  }>,
  allowedClasses: Array<string>  // 非必传 - 允许参加的班级
}

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

### 5.4 删除考试
```typescript
/**
 * 删除考试
 * @description 删除指定考试
 */
DELETE /api/admin/exams/{examId}

请求参数: 无

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

## 6. 成绩管理模块

### 6.1 获取考试成绩
```typescript
/**
 * 获取考试成绩
 * @description 获取指定考试的成绩列表
 */
GET /api/admin/exams/{examId}/results

请求参数:
{
  page: number,          // 非必传 - 页码，默认1
  size: number,          // 非必传 - 每页大小，默认10
  class: string,         // 非必传 - 班级筛选
  keyword: string,       // 非必传 - 搜索关键词（学号/姓名）
  sortBy: string,        // 非必传 - 排序字段：scores/time
  order: string          // 非必传 - 排序方式：asc/desc
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    total: number,       // 总记录数
    results: Array<{
      studentId: string, // 学号
      name: string,      // 姓名
      class: string,     // 班级
      scores: number,     // 分数
      submitTime: string,// 提交时间
      status: string,    // 状态：graded/ungraded
      details: Array<{   // 题目详情
        questionId: string,  // 题目ID
        scores: number,       // 得分
        answer: string,      // 答案
        correctAnswer: string // 正确答案
      }>
    }>
  }
}
```

### 6.2 批改成绩
```typescript
/**
 * 批改成绩
 * @description 批改指定学生的考试成绩
 */
POST /api/admin/exams/{examId}/results/{studentId}/grade

请求参数:
{
  scores: Array<{        // 必传 - 各题得分
    questionId: string,  // 题目ID
    scores: number        // 得分
  }>,
  comment: string        // 非必传 - 评语
}

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

### 6.3 导出成绩
```typescript
/**
 * 导出成绩
 * @description 导出指定考试的成绩为Excel文件
 */
GET /api/admin/exams/{examId}/results/export

请求参数:
{
  format: string    // 非必传 - 导出格式：excel/csv，默认excel
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    url: string          // 文件下载URL
  }
}
```

## 7. 学生考试模块

### 7.1 获取考试列表
```typescript
/**
 * 获取考试列表
 * @description 获取学生可参加的考试列表
 */
GET /api/student/exams

请求参数:
{
  page: number,          // 非必传 - 页码，默认1
  size: number,          // 非必传 - 每页大小，默认10
  status: string,        // 非必传 - 状态：upcoming/ongoing/ended
  subject: string,       // 非必传 - 学科筛选
  examType: string,      // 非必传 - 考试类型
  title: string          // 非必传 - 考试名称
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    total: number,       // 总考试数
    exams: Array<{
      examId: string,    // 考试ID
      title: string,     // 考试标题
      subject: string,   // 学科
      startTime: string, // 开始时间
      endTime: string,   // 结束时间
      duration: number,  // 时长
      status: string,    // 状态：未开始/进行中/已结束
      totalScore: number // 总分
    }>
  }
}
```

### 7.2 获取考试详情
```typescript
/**
 * 获取考试详情
 * @description 获取指定考试的详细信息
 */
GET /api/student/exams/{examId}

请求参数: 无

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    examId: string,      // 考试ID
    title: string,       // 考试标题
    startTime: string,   // 开始时间
    endTime: string,     // 结束时间
    duration: number,    // 总时长
    subject: string,     // 学科
    totalScore: number,  // 总分
    remainingTime: string, // 剩余时长
    questions: Array<{
      questionId: string,  // 题目ID
      type: string,        // 题目类型
      content: string,     // 题目内容
      options: Array<{     // 选择题选项
        key: string,       // 选项键
        value: string      // 选项值
      }>,
      scores: number,       // 分值
      order: number        // 题目序号
    }>
  }
}
```

### 7.3 开始考试
```typescript
/**
 * 开始考试
 * @description 学生开始参加考试
 */
POST /api/student/exams/{examId}/start

请求参数: 无

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    examId: string,      // 考试ID
    startTime: string,   // 开始时间
    endTime: string,     // 结束时间
    remainingTime: number // 剩余时间（秒）
  }
}
```

### 7.4 提交答案
```typescript
/**
 * 提交答案
 * @description 提交单个题目的答案
 */
POST /api/student/exams/{examId}/answers

请求参数:
{
  questionId: string,    // 必传 - 题目ID
  answer: string,        // 必传 - 答案内容
  timeSpent: number      // 非必传 - 答题用时（秒）
}

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

### 7.5 提交考试
```typescript
/**
 * 提交考试
 * @description 提交整个考试的所有答案
 */
POST /api/student/exams/{examId}/submit

请求参数:
{
  answers: Array<{       // 必传 - 所有题目的答案
    questionId: string,  // 题目ID
    answer: string       // 答案内容
  }>,
  timeSpent: number      // 非必传 - 总用时（分）
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    examId: string,      // 考试ID
    submitTime: string,  // 提交时间
    scores: number        // 客观题得分
  }
}
```

## 8. 成绩查询模块

### 8.1 获取成绩列表
```typescript
/**
 * 获取成绩列表
 * @description 获取学生的所有考试成绩
 */
GET /api/student/results

请求参数:
{
  page: number,          // 必传 - 页码，默认1
  size: number,          // 必传 - 每页大小，默认10
  title: string          // 非必传 - 考试名称
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    total: number,       // 总记录数
    results: Array<{
      examId: string,    // 考试ID
      title: string,     // 考试标题
      subject: string,   // 学科
      scores: number,     // 分数
      submitTime: string,// 完成时间
      status: string     // 状态：graded/ungraded
    }>
  }
}
```

### 8.2 获取成绩详情
```typescript
/**
 * 获取成绩详情
 * @description 获取指定考试的成绩详情
 */
GET /api/student/results/{examId}

请求参数: 无

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    examId: string,      // 考试ID
    title: string,       // 考试名称
    subject: string,     // 课程
    scores: number,       // 分数
    totalScore: number,  // 总分
    submitTime: string,  // 提交时间
    timeSpent: number,   // 答题用时（分）
    examParticipants: number, // 考试人数
    details: Array<{     // 题目详情
      questionId: string,  // 题目ID
      content: string,     // 题目内容
      type: string,        // 题目类型
      yourAnswer: string,  // 你的答案
      correctAnswer: string, // 正确答案
      scores: number,       // 得分
      analysis: string,    // 解析
      isCorrect: boolean   // 是否正确
    }>,
    statistics: {        // 成绩统计
      classAverage: number,  // 班级平均分
      highestScore: number,  // 最高分
      lowestScore: number,   // 最低分
      ranking: number        // 班级排名
    },
    questionTypesScore: {    // 各题型得分情况
      TypesScore: Array<{     
        type: string,        // 题目类型
        number: number,      // 题目数量
        totalScore: number,  // 总分
        yourScore: number    // 你的分数
      }>,
      accuracy: number       // 正确率
    }
  }
}
```

## 9. 个人中心模块

### 9.1 获取个人信息
```typescript
/**
 * 获取个人信息
 * @description 获取学生的个人信息
 */
GET /api/student/profile

请求参数: 无

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    studentId: string,   // 学号
    name: string,        // 姓名
    class: string,       // 班级
    email: string,       // 邮箱
    phone: string,       // 手机号
    avatar: string,      // 头像URL
    lastLogin: string    // 最后登录时间
  }
}
```

### 9.2 更新个人信息
```typescript
/**
 * 更新个人信息
 * @description 更新学生的个人信息
 */
PUT /api/student/profile

请求参数:
{
  name: string,         // 非必传 - 姓名
  email: string,        // 非必传 - 邮箱
  phone: string,        // 非必传 - 手机号
  avatar: string        // 非必传 - 头像URL
}

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

### 9.3 修改密码
```typescript
/**
 * 修改密码
 * @description 修改学生账号密码
 */
PUT /api/student/password

请求参数:
{
  oldPassword: string,  // 必传 - 旧密码
  newPassword: string   // 必传 - 新密码
}

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

## 10. 通用接口

### 10.1 文件上传
```typescript
/**
 * 文件上传
 * @description 上传图片或其他文件
 */
POST /api/upload

请求参数:
{
  file: File,           // 必传 - 文件对象
  type: string,         // 必传 - 文件类型：image/document
  maxSize: number       // 非必传 - 最大文件大小（MB），默认10
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    url: string,         // 文件访问URL
    filename: string     // 文件名
  }
}
```

### 10.2 获取系统通知
```typescript
/**
 * 获取系统通知
 * @description 获取学生的系统通知
 */
GET /api/student/notifications

请求参数:
{
  page: number,          // 非必传 - 页码，默认1
  size: number,          // 非必传 - 每页大小，默认10
  type: string,          // 非必传 - 通知类型
  isRead: boolean        // 非必传 - 是否已读
}

响应:
{
  code: number,          // 状态码
  message: string,       // 状态信息
  data: {
    total: number,       // 总通知数
    notifications: Array<{
      id: string,        // 通知ID
      title: string,     // 通知标题
      content: string,   // 通知内容
      type: string,      // 通知类型
      createTime: string,// 创建时间
      isRead: boolean    // 是否已读
    }>
  }
}
```

### 10.3 标记通知已读
```typescript
/**
 * 标记通知已读
 * @description 将通知标记为已读
 */
PUT /api/student/notifications/{notificationId}/read

请求参数: 无

响应:
{
  code: number,          // 状态码
  message: string        // 状态信息
}
```

## 11. 错误码说明

| 错误码 | 说明                 |
| ------ | -------------------- |
| 200    | 成功                 |
| 400    | 请求参数错误         |
| 401    | 未授权               |
| 403    | 禁止访问             |
| 404    | 资源不存在           |
| 500    | 服务器内部错误       |
| 1001   | 用户名或密码错误     |
| 1002   | 账号已被禁用         |
| 1003   | 验证码错误           |
| 2001   | 考试已开始，无法修改 |
| 2002   | 考试已结束，无法修改 |
| 3001   | 文件大小超过限制     |
| 3002   | 文件类型不支持       |

## 12. 接口规范

1. 所有接口使用 RESTful 风格
2. 请求和响应数据格式统一使用 JSON
3. 所有接口都需要进行身份验证（除登录接口外）
4. 分页接口统一使用 page 和 size 参数
5. 时间格式统一使用 ISO 8601 标准
6. 文件上传大小限制为 10MB
7. 接口响应时间应在 1 秒内

## 13. 安全规范

1. 使用 HTTPS 协议
2. 使用 JWT 进行身份验证
3. 密码使用 bcrypt 加密存储
4. 实现请求频率限制
5. 实现跨域资源共享（CORS）
6. 实现 XSS 防护
7. 实现 CSRF 防护