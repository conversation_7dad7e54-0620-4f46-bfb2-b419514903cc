package com.ruoyi.web.controller.interview;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.base.BaseResponse;
import com.ruoyi.system.domain.InterviewSpeechRecord;
import com.ruoyi.system.domain.InterviewTranscription;
import com.ruoyi.system.domain.dto.FeedbackDto;
import com.ruoyi.system.domain.dto.InterviewSpeechChapterDto;
import com.ruoyi.system.domain.vo.InterviewQueryVO;
import com.ruoyi.system.domain.vo.InterviewVO;
import com.ruoyi.system.service.InterviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 面试管理控制器
 */
@RestController
@RequestMapping("/interview")
    public class InterviewController {

    @Autowired
    private InterviewService interviewService;
    
    /**
     * 分页查询面试列表
     */
    @PostMapping("/page")
    public BaseResponse<Page<InterviewVO>> pageInterviews(@RequestBody InterviewQueryVO queryVO) {
        Page<InterviewVO> page = interviewService.pageInterviews(queryVO);
        return BaseResponse.ok(page);
    }
    
    /**
     * 获取面试详情
     */
    @GetMapping("/detail/{id}")
    public BaseResponse<InterviewVO> getInterviewDetail(@PathVariable String id) {
        InterviewVO interviewVO = interviewService.getInterviewDetail(id);
        return BaseResponse.ok(interviewVO);
    }

    /**
     * 根据章节获取转写记录
     */
    @GetMapping("/speech/{id}")
    public BaseResponse<List<InterviewSpeechRecord>> getSpeechRecord(@PathVariable String id) {
        List<InterviewSpeechRecord> records = interviewService.getSpeechRecord(id);
        return BaseResponse.ok(records);
    }

   /* *
     * 根据面试id查询所有QA
     */
    @GetMapping("/transcription/{interviewId}")
    public BaseResponse<List<InterviewTranscription>> getTranscriptionList(@PathVariable String interviewId) {
        List<InterviewTranscription> transcriptions = interviewService.getTranscriptionList(interviewId);
        return BaseResponse.ok(transcriptions);
    }

    /**
     * 更新面试评分和反馈
     */
    @PutMapping("/feedback")
    public BaseResponse<Boolean> updateInterviewFeedback(@RequestBody FeedbackDto feedbackDto) {
        boolean success = interviewService.updateInterviewFeedback(feedbackDto);
        return BaseResponse.ok(success);
    }
} 