package com.ruoyi.web.controller.resume;

import com.ruoyi.common.base.BaseResponse;
import com.ruoyi.system.domain.ResumeHrQuestion;
import com.ruoyi.system.domain.dto.ResumeHrQueDTO;
import com.ruoyi.system.service.HrQuestionsService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * HR问题管理
 */
@Slf4j
@RestController
@RequestMapping("/resume/questions")
public class HrQuestionsController {
    @Resource
    private HrQuestionsService hrQuestionsService;

    @ApiOperation("HR问题列表")
    @PostMapping("/hrQuestionList")
    public BaseResponse hrQuestionList(@RequestBody ResumeHrQueDTO resumeHrQueDTO) {
        List<ResumeHrQuestion> list = hrQuestionsService.hrQuestionList(resumeHrQueDTO);
        return BaseResponse.ok(list);
    }

    @ApiOperation("添加HR问题")
    @PostMapping("/add")
    public BaseResponse addQuestion(@RequestBody ResumeHrQuestion question) {
        try {
            boolean result = hrQuestionsService.addQuestion(question);
            return result ? BaseResponse.ok("添加成功") : BaseResponse.error("添加失败");
        } catch (Exception e) {
            log.error("添加HR问题失败", e);
            return BaseResponse.error("添加失败：" + e.getMessage());
        }
    }

    @GetMapping("hrQuestion/{queId}")
    public BaseResponse getQuestion(@PathVariable Long queId) {
        ResumeHrQuestion question = hrQuestionsService.getQuestion(queId);
        return BaseResponse.ok(question);
    }

    @ApiOperation("修改HR问题")
    @PutMapping("/update")
    public BaseResponse updateQuestion(@RequestBody ResumeHrQuestion question) {
        try {
            if (question.getQueId() == null) {
                return BaseResponse.error("问题ID不能为空");
            }
            boolean result = hrQuestionsService.updateQuestion(question);
            return result ? BaseResponse.ok("修改成功") : BaseResponse.error("修改失败");
        } catch (Exception e) {
            log.error("修改HR问题失败", e);
            return BaseResponse.error("修改失败：" + e.getMessage());
        }
    }

    @ApiOperation("删除HR问题")
    @DeleteMapping("/delete/{queId}")
    public BaseResponse deleteQuestion(
            @ApiParam(value = "问题ID", required = true)
            @PathVariable Long queId) {
        try {
            boolean result = hrQuestionsService.deleteQuestion(queId);
            return result ? BaseResponse.ok("删除成功") : BaseResponse.error("删除失败");
        } catch (Exception e) {
            log.error("删除HR问题失败", e);
            return BaseResponse.error("删除失败：" + e.getMessage());
        }
    }
}
