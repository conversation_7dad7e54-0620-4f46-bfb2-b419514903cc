package com.ruoyi.web.controller.resume;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.ruoyi.common.base.BaseResponse;
import com.ruoyi.system.domain.vo.AIQuestionsVo;
import com.ruoyi.system.domain.vo.ProjectVO;
import com.ruoyi.system.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目管理
 */
@RestController
@RequestMapping("/project")
@Slf4j
public class ProjectController {

    @Autowired
    private ProjectService projectService;
    
    /**
     * 获取所有项目列表
     */
    @GetMapping("/list")
    public BaseResponse<List<ProjectVO>> list(@RequestParam(required = false) String keyword) {
        log.info("获取所有项目列表");
        List<ProjectVO> projects = projectService.listAllProjects(keyword);
        return BaseResponse.ok(projects);
    }
    
    /**
     * 分页查询项目列表
     */
    @GetMapping("/page/list")
    public BaseResponse<Page<ProjectVO>> page(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "role", required = false) String role) {
        log.info("分页查询项目列表, pageNum={}, pageSize={}, name={}, role={}", 
                pageNum, pageSize, name, role);
        
        Page<ProjectVO> page = projectService.pageProjects(pageNum, pageSize, name, role);
        return BaseResponse.ok(page);
    }
    
    /**
     * 根据ID获取项目详情
     */
    @GetMapping("/{id}")
    public BaseResponse<ProjectVO> getById(@PathVariable Long id) {
        log.info("获取项目详情，id={}", id);
        ProjectVO project = projectService.getProjectById(id);
        return project != null ? BaseResponse.ok(project) : BaseResponse.error("项目不存在");
    }
    
    /**
     * 添加项目
     */
    @PostMapping("/add")
    public BaseResponse<Void> add(@RequestBody ProjectVO projectVO) {
        log.info("添加项目：{}", projectVO);
        boolean result = projectService.saveProjectWithDetails(projectVO);
        return result ? BaseResponse.ok(null) : BaseResponse.error("添加项目失败");
    }
    
    /**
     * 更新项目
     */
    @PutMapping("/{id}")
    public BaseResponse<Void> update(@PathVariable Long id, @RequestBody ProjectVO projectVO) {
        log.info("更新项目，id={}，projectVO={}", id, projectVO);
        projectVO.setProId(id);
        boolean result = projectService.updateProjectWithDetails(projectVO);
        return result ? BaseResponse.ok(null) : BaseResponse.error("更新项目失败");
    }
    
    /**
     * 删除项目
     */
    @DeleteMapping("/delete/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        log.info("删除项目，id={}", id);
        boolean result = projectService.deleteProjectWithDetails(id);
        return result ? BaseResponse.ok(null) : BaseResponse.error("删除项目失败");
    }

    /**
     * 通过项目段落调用AI接口，将AI接口响应的JSON接收形成对象返回到前端渲染，
     * 前端拿到后，将需要的问题及答案编辑好，点击保存按钮，存储到项目问题表中resume_project_question
     */
    @PostMapping("/selectByContent")
    public BaseResponse selectByContent(@RequestBody String content) throws Exception {
        log.info("项目内容：{}", content);
        List<AIQuestionsVo> list = projectService.selectByContent(content);
        log.info("AI生成问题内容：{}", list);
        return BaseResponse.ok(list);
    }

} 