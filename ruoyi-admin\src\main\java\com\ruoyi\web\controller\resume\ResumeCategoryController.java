package com.ruoyi.web.controller.resume;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.common.base.BaseResponse;
import com.ruoyi.system.domain.ResumeCategory;
import com.ruoyi.system.mapper.ResumeCategoryMapper;
import java.util.List;

/**
 * 简历类别
 **/
@RestController
@RequestMapping("/category")
public class ResumeCategoryController {

    @Autowired
    private ResumeCategoryMapper resumeCategoryMapper;

    @GetMapping("/list")
    public BaseResponse<List<ResumeCategory>> list(){
        // 查询所有简历类别
        List<ResumeCategory> categoryList = resumeCategoryMapper.selectList(null);
        return BaseResponse.ok(categoryList);
    }
}
