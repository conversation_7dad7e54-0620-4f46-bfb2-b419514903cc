package com.ruoyi.web.controller.resume;

import com.ruoyi.common.base.BaseResponse;
import com.ruoyi.system.domain.KsUser;
import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.ResumeHrQuestion;
import com.ruoyi.system.domain.dto.ResumeAuditDTO;
import com.ruoyi.system.domain.dto.ResumeDTO;
import com.ruoyi.system.domain.dto.ResumeHrQueDTO;
import com.ruoyi.system.domain.vo.ResumeFullSaveDto;
import com.ruoyi.system.service.ResumeService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * 简历管理
 */
@RestController
@RequestMapping("/resume")
@Slf4j
public class ResumeController {

    @Resource
    private ResumeService resumeService;
    /**
     * 查询所有完整简历
     */
    @PostMapping("/list")
    public BaseResponse myResumeList(@RequestBody ResumeDTO resumeDto) {
        List<ResumeFullSaveDto> list = resumeService.getList(resumeDto);
        return BaseResponse.ok(list);
    }

    @ApiOperation("获取简历详情")
    @GetMapping("/detail/{resumeId}")
    public BaseResponse getResumeDetail(
            @ApiParam(value = "简历ID", required = true)
            @PathVariable Long resumeId) {
        ResumeFullSaveDto resumeDetail = resumeService.getResumeDetail(resumeId);
        return BaseResponse.ok(resumeDetail);
    }

    @ApiOperation("审核简历")
    @PutMapping("/audit/{resumeId}")
    public BaseResponse auditResume(@PathVariable Long resumeId, @RequestBody ResumeAuditDTO auditDTO) {
        if (resumeId == null) {
            return BaseResponse.error("简历ID不能为空");
        }
        
        // 如果是拒绝审核且没有审核意见，则返回错误
        if (auditDTO.getStatus() == 2 && (auditDTO.getAuditOpinion() == null || auditDTO.getAuditOpinion().trim().isEmpty())) {
            return BaseResponse.error("审核拒绝时，审核意见不能为空");
        }
        
        boolean result = resumeService.auditResume(resumeId, auditDTO.getStatus(), auditDTO.getAuditOpinion());
        return result ? BaseResponse.ok("审核成功") : BaseResponse.error("审核失败");
    }
}
