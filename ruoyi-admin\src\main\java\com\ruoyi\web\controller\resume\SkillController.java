package com.ruoyi.web.controller.resume;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.ruoyi.common.base.BaseResponse;
import com.ruoyi.system.domain.Skill;
import com.ruoyi.system.domain.vo.SkillSegmentVO;
import com.ruoyi.system.domain.vo.SkillVO;
import com.ruoyi.system.service.SkillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 个人技能管理
 */
@RestController
@RequestMapping("/skill")
@Slf4j
public class SkillController {
    
    @Autowired
    private SkillService skillService;
    
    /**
     * 获取所有技能列表
     */
    @GetMapping("/list")
    public BaseResponse<List<SkillVO>> list(@RequestParam(required = false) String keyword) {
        log.info("获取所有技能列表");
        List<SkillVO> skills = skillService.listAllSkills(keyword);
        return BaseResponse.ok(skills);
    }
    
    /**
     * 分页查询技能段落列表
     */
    @GetMapping("/page/list")
    public BaseResponse<Page<SkillSegmentVO>> page(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "proficiency", required = false) String proficiency) {
        log.info("分页查询技能段落列表, pageNum={}, pageSize={}, name={}, proficiency={}", 
                pageNum, pageSize, name, proficiency);
        
        Page<SkillSegmentVO> page = skillService.pageSkillSegments(pageNum, pageSize, name, proficiency);
        return BaseResponse.ok(page);
    }
    
    /**
     * 根据ID获取技能段落详情
     */
    @GetMapping("/{id}")
    public BaseResponse<SkillSegmentVO> getById(@PathVariable Long id) {
        log.info("获取技能段落详情，id={}", id);
        SkillSegmentVO segment = skillService.getSkillSegmentById(id);
        return segment != null ? BaseResponse.ok(segment) : BaseResponse.error("技能段落不存在");
    }
    
    /**
     * 添加技能段落
     */
    @PostMapping("/add")
    public BaseResponse<Void> add(@RequestBody SkillSegmentVO segmentVO) {
        log.info("添加技能段落：{}", segmentVO);
        boolean result = skillService.saveSkillSegment(segmentVO);
        return result ? BaseResponse.ok(null) : BaseResponse.error("添加技能段落失败");
    }
    
    /**
     * 添加新技能点
     */
    @PostMapping("/addSkillPoint")
    public BaseResponse<SkillVO> addSkillPoint(@RequestBody Skill skill) {
        log.info("添加新技能点：{}", skill);
        boolean result = skillService.save(skill);
        if (result) {
            SkillVO skillVO = new SkillVO();
            skillVO.setSkiId(skill.getSkiId());
            skillVO.setName(skill.getName());
            return BaseResponse.ok(skillVO);
        } else {
            return BaseResponse.error("添加新技能点失败");
        }
    }
    
    /**
     * 更新技能段落
     */
    @PutMapping("/{id}")
    public BaseResponse<Void> update(@PathVariable Long id, @RequestBody SkillSegmentVO segmentVO) {
        log.info("更新技能段落，id={}，segmentVO={}", id, segmentVO);
        segmentVO.setSegId(id);
        boolean result = skillService.updateSkillSegment(segmentVO);
        return result ? BaseResponse.ok(null) : BaseResponse.error("更新技能段落失败");
    }
    
    /**
     * 删除技能段落
     */
    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        log.info("删除技能段落，id={}", id);
        boolean result = skillService.deleteSkillSegment(id);
        return result ? BaseResponse.ok(null) : BaseResponse.error("删除技能段落失败");
    }
} 