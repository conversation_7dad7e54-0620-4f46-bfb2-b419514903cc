package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Assignments;
import com.ruoyi.system.domain.dto.AssignmentsDTO;
import com.ruoyi.system.domain.vo.AssignmentsPageVo;
import com.ruoyi.system.domain.vo.AssignmentsCreateVO;
import com.ruoyi.system.service.AssignmentsService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
/**
 * 作业管理
 * */
@RestController
@RequestMapping("/aqsystem/assignments")
public class AssignmentsController extends BaseController {

    @Resource
    private AssignmentsService assignmentsService;

    /**
     * 查询作业管理列表
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:assignments:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssignmentsDTO assignmentsDTO)
    {
        startPage();
        List<AssignmentsPageVo> list = assignmentsService.selectAssignmentsLists(assignmentsDTO);
        return getDataTable(list);
    }

    /**
     * 获取作业管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:assignments:query')")
    @GetMapping(value = "/{assignmentId}")
    public AjaxResult getInfo(@PathVariable("assignmentId") String assignmentId)
    {
        return success(assignmentsService.selectAssignmentsByAssignmentId(assignmentId));
    }

    /**
     * 获取作业管理编辑
     */
    @GetMapping(value = "select/{assignmentId}")
    public AjaxResult selectInfo(@PathVariable("assignmentId") String assignmentId)
    {
        return success(assignmentsService.selectByAssignmentId(assignmentId));
    }

    /**
     * 新增作业管理
     *//*
    @PreAuthorize("@ss.hasPermi('aqsystem:assignments:add')")
    @Log(title = "作业管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssignmentsCreateVO assignmentsCreateVO)
    {
        return toAjax(assignmentsService.insertAssignmentsWithQuestions(assignmentsCreateVO));
    }
*/
    /**
     * 修改作业管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:assignments:edit')")
    @Log(title = "作业管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssignmentsCreateVO assignmentsCreateVO)
    {
        return toAjax(assignmentsService.updateAssignmentsWithQuestions(assignmentsCreateVO));
    }

    /**
     * 删除作业管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:assignments:remove')")
    @Log(title = "作业管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{assignmentIds}")
    public AjaxResult remove(@PathVariable String[] assignmentIds)
    {
        List<Assignments> stringList = new ArrayList<>();
        for (String assignmentId : assignmentIds) {
            Assignments assignments = new Assignments();
            assignments.setIsDelete(1);
            assignments.setAssignmentId(assignmentId);
            stringList.add(assignments);
        }
        return toAjax(assignmentsService.deleteAssignmentsByAssignmentIds(stringList));
    }
}

