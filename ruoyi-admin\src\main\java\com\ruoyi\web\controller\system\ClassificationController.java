package com.ruoyi.web.controller.system;

import com.ruoyi.system.domain.Classification;
import com.ruoyi.system.service.ClassificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 管理
 * */
@RestController
@RequestMapping("/aqsystem/classification")
public class ClassificationController {

    @Autowired
    private ClassificationService classificationService;

    @GetMapping
    public List<Classification> selectList(){
        return classificationService.selectList();
    }

}
