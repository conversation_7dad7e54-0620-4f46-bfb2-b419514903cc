package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Exams;
import com.ruoyi.system.domain.dto.ExamsDTO;
import com.ruoyi.system.domain.dto.ExamsSelectDTO;
import com.ruoyi.system.domain.vo.ExamsPageVo;
import com.ruoyi.system.domain.vo.ExamsCreateVO;
import com.ruoyi.system.domain.ExamQuestions;
import com.ruoyi.system.service.IExamsService;
import com.ruoyi.system.mapper.ExamQuestionsMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考试管理Controller
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/aqsystem/exams")
public class ExamsController extends BaseController
{
    @Autowired
    private IExamsService examsService;


    /**
     * 查询考试管理列表
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:exams:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamsDTO exams)
    {
        startPage();
        List<ExamsPageVo> list = examsService.selectExamsLists(exams);
        return getDataTable(list);
    }


    /**
     * 获取考试管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:exams:query')")
    @GetMapping(value = "/{examId}")
    public AjaxResult getInfo(@PathVariable("examId") String examId)
    {
        return success(examsService.selectExamsByExamId(examId));
    }

    /**
     * 条件查询考试管理详细列表
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:exams:query')")
    @GetMapping(value = "/selectList")
    public AjaxResult getInfo(@RequestBody ExamsSelectDTO examsSelectDTO)
    {
        return success(examsService.selectListByDto(examsSelectDTO));
    }

    /**
     * 获取考试管理编辑
     */
    @GetMapping(value = "select/{examId}")
    public AjaxResult selectInfo(@PathVariable("examId") String examId)
    {
        return success(examsService.selectByExamId(examId));
    }

    /**
     * 新增考试管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:exams:add')")
    @Log(title = "考试管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExamsCreateVO examsCreateVO)
    {
        return toAjax(examsService.insertExamsWithQuestions(examsCreateVO));
    }

    /**
     * 修改考试管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:exams:edit')")
    @Log(title = "考试管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExamsCreateVO examsCreateVO)
    {
        return toAjax(examsService.updateExamsWithQuestions(examsCreateVO));
    }

    /**
     * 删除考试管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:exams:remove')")
    @Log(title = "考试管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{examIds}")
    public AjaxResult remove(@PathVariable String[] examIds)
    {
        List<Exams> stringList = new ArrayList<>();
        for (String examId : examIds) {
            Exams exams = new Exams();
            exams.setIsDelete(1L);
            exams.setExamId(examId);
            stringList.add(exams);
        }
        return toAjax(examsService.deleteExamsByExamIds(stringList));
    }
}
