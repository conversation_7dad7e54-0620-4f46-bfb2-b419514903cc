package com.ruoyi.web.controller.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.Labels;
import com.ruoyi.system.domain.dto.ExamsDTO;
import com.ruoyi.system.domain.vo.ExamsPageVo;
import com.ruoyi.system.mapper.LabelsMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
/**
 * 标签管理
 * */
@RestController
@RequestMapping("/aqsystem/label")
public class LabelController extends BaseController {
    @Resource
    private LabelsMapper labelsMapper;

    /**
     * 查询标签列表
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:exams:list')")
    @GetMapping("/list")
    public TableDataInfo list()
    {
        startPage();
        List<Labels> list = labelsMapper.selectList(new LambdaQueryWrapper<>());
        return getDataTable(list);
    }
}
