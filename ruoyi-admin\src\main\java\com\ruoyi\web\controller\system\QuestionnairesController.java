package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.Exams;
import com.ruoyi.system.domain.Questionnaires;
import com.ruoyi.system.domain.dto.QuestionnaireCreateDTO;
import com.ruoyi.system.domain.dto.QuestionnairesPageDTO;
import com.ruoyi.system.domain.vo.QuestionnaireCreateVO;
import com.ruoyi.system.domain.vo.QuestionnairesVo;
import com.ruoyi.system.service.IQuestionnairesService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 问卷管理Controller
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/aqsystem/questionnaires")
public class QuestionnairesController extends BaseController
{
    @Autowired
    private IQuestionnairesService questionnairesService;

    /**
     * 查询问卷管理列表
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questionnaires:list')")
    @GetMapping("/list")
    public TableDataInfo list(QuestionnairesPageDTO questionnaires)
    {
        startPage();
        Questionnaires questionnaires1 = new Questionnaires();
        BeanUtils.copyProperties(questionnaires,questionnaires1);
        questionnaires1.setLableId(questionnaires.getLabelId());
        List<QuestionnairesVo> list = questionnairesService.selectQuestionnairesList(questionnaires1);
        return getDataTable(list);
    }

     /**
     * 导出问卷管理列表
     *//*
    @PreAuthorize("@ss.hasPermi('aqsystem:questionnaires:export')")
    @Log(title = "问卷管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Questionnaires questionnaires)
    {
        List<Questionnaires> list = questionnairesService.selectQuestionnairesList(questionnaires);
        ExcelUtil<Questionnaires> util = new ExcelUtil<Questionnaires>(Questionnaires.class);
        util.exportExcel(response, list, "问卷管理数据");
    }*/

    /**
     * 获取问卷管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questionnaires:query')")
    @GetMapping(value = "/{questionnaireId}")
    public AjaxResult getInfo(@PathVariable("questionnaireId") String questionnaireId)
    {
        return success(questionnairesService.selectQuestionnaireDetail(questionnaireId));
    }

    /**
     * 根据条件查询问卷管理详细信息
     */
    /*@PreAuthorize("@ss.hasPermi('aqsystem:questionnaires:query')")
    @PostMapping(value = "/selectBy")
    public AjaxResult getInfoBySelect(@RequestBody )
    {
        return success(questionnairesService.selectQuestionnaireDetail(questionnaireId));
    }*/

    /**
     * 新增问卷管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questionnaires:add')")
    @Log(title = "问卷管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QuestionnaireCreateDTO questionnaireCreateDTO)
    {
        return toAjax(questionnairesService.insertQuestionnaires(questionnaireCreateDTO));
    }

    /**
     * 修改问卷管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questionnaires:edit')")
    @Log(title = "问卷管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QuestionnaireCreateVO questionnaires)
    {
        return toAjax(questionnairesService.updateQuestionnaires(questionnaires));
    }

    /**
     * 删除问卷管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questionnaires:remove')")
    @Log(title = "问卷管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{questionnaireIds}")
    public AjaxResult remove(@PathVariable String[] questionnaireIds)
    {
        List<Questionnaires> questionnairesList = new ArrayList<>();
        for (String q : questionnaireIds) {
            Questionnaires questionnaires = new Questionnaires();
            questionnaires.setIsDelete(1L);
            questionnaires.setQuestionnaireId(q);
            questionnairesList.add(questionnaires);
        }
        return toAjax(questionnairesService.deleteQuestionnairesByQuestionnaireIds(questionnairesList));
    }

    /**
     * 获取问卷单个详细信息包含题目
     */
    @GetMapping(value = "select/{questionnaireId}")
    public AjaxResult selectInfo(@PathVariable("questionnaireId") String questionnaireId)
    {
        return success(questionnairesService.selectByQuestionnaireId(questionnaireId));
    }
}
