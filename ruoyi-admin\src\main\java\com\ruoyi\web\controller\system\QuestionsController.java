package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.base.BaseResponse;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.CommonUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.Questions;
import com.ruoyi.system.service.IQuestionsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 题库管理Controller
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/aqsystem/questions")
public class QuestionsController extends BaseController
{
    @Autowired
    private IQuestionsService questionsService;

    /**
     * 查询题库管理列表
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questions:list')")
    @GetMapping("/list")
    public TableDataInfo list(Questions questions)
    {
        startPage();
        List<Questions> list = questionsService.selectQuestionsList(questions);
        return getDataTable(list);
    }

    /**
     * 导出题库管理列表
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questions:export')")
    @Log(title = "题库管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Questions questions)
    {
        List<Questions> list = questionsService.selectQuestionsList(questions);
        ExcelUtil<Questions> util = new ExcelUtil<Questions>(Questions.class);
        util.exportExcel(response, list, "题库管理数据");
    }

    /**
     * 获取题库管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questions:query')")
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") String questionId)
    {
        return success(questionsService.selectQuestionsByQuestionId(questionId));
    }

    /**
     * 新增题库管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questions:add')")
    @Log(title = "题库管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Questions questions)
    {
        return toAjax(questionsService.insertQuestions(questions));
    }

    /**
     * 修改题库管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questions:edit')")
    @Log(title = "题库管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Questions questions)
    {
        return toAjax(questionsService.updateQuestions(questions));
    }

    /**
     * 删除题库管理
     */
    @PreAuthorize("@ss.hasPermi('aqsystem:questions:remove')")
    @Log(title = "题库管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable String[] questionIds)
    {
        List<Questions> stringList = new ArrayList<>();
        for (String q : questionIds) {
            Questions questions = new Questions();
            questions.setIsDelete(1L);
            questions.setQuestionId(q);
            stringList.add(questions);
        }
        return toAjax(questionsService.deleteQuestionsByQuestionIds(stringList));
    }

    /**
     * 导入题库
     * */
    @PostMapping("/import")
    public BaseResponse importExcel(@RequestParam("file") MultipartFile file) {
        try {
            //获取输入流
            InputStream inputStream = file.getInputStream();
            //创建读取工作簿
            Workbook workbook = WorkbookFactory.create(inputStream);
            //获取工作表
            Sheet sheet = workbook.getSheetAt(0);
            //获取总行
            int rows = sheet.getPhysicalNumberOfRows();
            if (rows > 1) { // 表头占一行，所以判断条件为>1
                //获取单元格
                for (int i = 1; i < rows; i++) {
                    Row row = sheet.getRow(i);
                    Questions question = new Questions();
                    String subject = CommonUtil.getCellValue(row.getCell(0));
                    switch (subject) {
                        case "Java基础":
                            question.setTopicClassification("1");
                        case "Java高级":
                            question.setTopicClassification("2");
                        case "Spring框架":
                            question.setTopicClassification("3");
                        case "前端开发":
                            question.setTopicClassification("4");
                        case "数据库":
                            question.setTopicClassification("5");
                        case "算法与数据结构":
                            question.setTopicClassification("6");
                        case "系统设计":
                            question.setTopicClassification("7");
                        case "面试题":
                            question.setTopicClassification("8");
                        default:
                            question.setTopicClassification("1");
                            break;
                    };
                    String content = CommonUtil.getCellValue(row.getCell(1));
                    String type = CommonUtil.getCellValue(row.getCell(2));
                    String a = CommonUtil.getCellValue(row.getCell(4));
                    String b = CommonUtil.getCellValue(row.getCell(5));
                    String c = CommonUtil.getCellValue(row.getCell(6));
                    String d = CommonUtil.getCellValue(row.getCell(7));
                    String options = "{}"; // 默认空JSON

                    // 检查题目类型是否有选项（单选题、多选题、不定项选择题）
                    boolean hasOptions = "单选题".equals(type) || "多选题".equals(type) || "不定项选择题".equals(type);

                    if (hasOptions) {
                        // 使用 StringBuilder 动态构建 options JSON
                        StringBuilder optionsBuilder = new StringBuilder("{");

                        // 检查每个选项是否有值，有则加入
                        if (StringUtils.isNotBlank(a)) optionsBuilder.append("\"A\": \"").append(a).append("\", ");
                        if (StringUtils.isNotBlank(b)) optionsBuilder.append("\"B\": \"").append(b).append("\", ");
                        if (StringUtils.isNotBlank(c)) optionsBuilder.append("\"C\": \"").append(c).append("\", ");
                        if (StringUtils.isNotBlank(d)) optionsBuilder.append("\"D\": \"").append(d).append("\", ");

                        // 移除最后的 ", "（如果有选项）
                        if (optionsBuilder.length() > 1) {
                            optionsBuilder.setLength(optionsBuilder.length() - 2);
                        }

                        optionsBuilder.append("}");
                        options = optionsBuilder.toString();
                    }
                    String difficulty = CommonUtil.getCellValue(row.getCell(3));
                    switch (difficulty) {
                        case "简单":
                            question.setDifficulty("easy");
                            break;  // 防止穿透
                        case "中等":
                            question.setDifficulty("medium");
                            break;
                        case "困难":
                            question.setDifficulty("hard");
                            break;
                        default:  // 处理未知情况
                            question.setDifficulty("medium");  // 或者抛异常、设默认值
                            break;
                    }
                    String answer = CommonUtil.getCellValue(row.getCell(8));
                    String analysis = CommonUtil.getCellValue(row.getCell(9));

                    question.setSubject(subject);
                    question.setContent(content);
                    question.setType(type);
                    question.setOptions(options);
                    question.setAnswer(answer);
                    question.setAnalysis(analysis);
                    questionsService.insertQuestions(question);
                }
            }
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            throw new RuntimeException(e);
        }
        return BaseResponse.ok("导入题目成功");
    }


}
