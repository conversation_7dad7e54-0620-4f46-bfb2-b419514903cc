package com.ruoyi.web.controller.system;

import com.ruoyi.system.domain.Classification;
import com.ruoyi.system.domain.StudentClass;
import com.ruoyi.system.service.ClassificationService;
import com.ruoyi.system.service.StudentClassService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
 * 班级管理
 * */
@RestController
@RequestMapping("/aqsystem/class")
public class StudentClassController {
    @Autowired
    private StudentClassService studentClassService;

    @GetMapping
    public List<StudentClass> selectList(){
        return studentClassService.list();
    }
}
