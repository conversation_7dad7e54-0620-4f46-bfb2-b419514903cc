package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.SysTodo;
import com.ruoyi.system.service.ISysTodoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 待办Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/todo")
public class SysTodoController extends BaseController
{
    @Autowired
    private ISysTodoService sysTodoService;

    /**
     * 查询待办列表
     */
    @PreAuthorize("@ss.hasPermi('system:todo:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysTodo sysTodo)
    {
        startPage();
        List<SysTodo> list = sysTodoService.selectSysTodoList(sysTodo);
        return getDataTable(list);
    }

    /**
     * 获取当前用户待办列表
     */
    @GetMapping("/my")
    public AjaxResult myTodoList()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysTodo> list = sysTodoService.selectSysTodoByUserId(userId);
        return AjaxResult.success(list);
    }

    /**
     * 导出待办列表
     */
    @PreAuthorize("@ss.hasPermi('system:todo:export')")
    @Log(title = "待办", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysTodo sysTodo)
    {
        List<SysTodo> list = sysTodoService.selectSysTodoList(sysTodo);
        ExcelUtil<SysTodo> util = new ExcelUtil<SysTodo>(SysTodo.class);
        util.exportExcel(response, list, "待办数据");
    }

    /**
     * 获取待办详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:todo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(sysTodoService.selectSysTodoById(id));
    }

    /**
     * 新增待办
     */
    @PreAuthorize("@ss.hasPermi('system:todo:add')")
    @Log(title = "待办", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysTodo sysTodo)
    {
        return toAjax(sysTodoService.insertSysTodo(sysTodo));
    }

    /**
     * 修改待办
     */
    @PreAuthorize("@ss.hasPermi('system:todo:edit')")
    @Log(title = "待办", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysTodo sysTodo)
    {
        return toAjax(sysTodoService.updateSysTodo(sysTodo));
    }

    /**
     * 删除待办
     */
    @PreAuthorize("@ss.hasPermi('system:todo:remove')")
    @Log(title = "待办", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sysTodoService.deleteSysTodoByIds(ids));
    }
    
    /**
     * 更新待办状态
     */
    @PreAuthorize("@ss.hasPermi('system:todo:edit')")
    @Log(title = "待办", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody SysTodo sysTodo)
    {
        return toAjax(sysTodoService.updateSysTodo(sysTodo));
    }
} 