package com.ruoyi.web.controller.system;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.base.BaseResponse;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.CommonUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.Labels;
import com.ruoyi.system.domain.Questions;
import com.ruoyi.system.domain.WjQuestions;
import com.ruoyi.system.domain.vo.WjQuestionsVO;
import com.ruoyi.system.mapper.LabelsMapper;
import com.ruoyi.system.service.IWjQuestionsService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 问卷题库Controller
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/aqsystem/wjquestions")
public class WjQuestionsController extends BaseController {
    @Autowired
    private IWjQuestionsService wjQuestionsService;

    @Autowired
    private LabelsMapper labelsMapper;

    /**
     * 查询问卷题库列表
     */
    @PreAuthorize("@ss.hasPermi('system:wjquestions:list')")
    @GetMapping("/list")
    public TableDataInfo list(WjQuestions wjQuestions) {
        // 查询总数
        long total = wjQuestionsService.selectWjQuestionsCount(wjQuestions);
        // 设置分页参数
        startPage();
        List<WjQuestions> list = wjQuestionsService.selectWjQuestionsList(wjQuestions);
        
        // 转换为VO
        List<WjQuestionsVO> voList = list.stream().map(q -> {
            WjQuestionsVO vo = new WjQuestionsVO();
            BeanUtils.copyProperties(q, vo);
            Labels labels = labelsMapper.selectById(q.getLabelId());
            vo.setLabelName(labels != null ? labels.getLabelName() : null);
            return vo;
        }).collect(Collectors.toList());
        
        // 构建返回结果
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
        rspData.setMsg("查询成功");
        rspData.setRows(voList);
        rspData.setTotal(total);
        return rspData;
    }

  /*  *//**
     * 导出问卷题库列表
     *//*
    @PreAuthorize("@ss.hasPermi('system:wjquestions:export')")
    @Log(title = "问卷题库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WjQuestions wjQuestions)
    {
        List<WjQuestionsVO> list = wjQuestionsService.selectWjQuestionsList(wjQuestions);
        ExcelUtil<WjQuestions> util = new ExcelUtil<WjQuestions>(WjQuestions.class);
        util.exportExcel(response, list, "问卷题库数据");
    }*/

    /**
     * 获取问卷题库详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:wjquestions:query')")
    @GetMapping(value = "/{questionsId}")
    public AjaxResult getInfo(@PathVariable("questionsId") Long questionsId)
    {
        return success(wjQuestionsService.selectWjQuestionsByQuestionsId(questionsId));
    }

    /**
     * 新增问卷题库
     */
    @PreAuthorize("@ss.hasPermi('system:wjquestions:add')")
    @Log(title = "问卷题库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WjQuestions wjQuestions)
    {
        return toAjax(wjQuestionsService.insertWjQuestions(wjQuestions));
    }

    /**
     * 修改问卷题库
     */
    @PreAuthorize("@ss.hasPermi('system:wjquestions:edit')")
    @Log(title = "问卷题库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WjQuestions wjQuestions)
    {
        return toAjax(wjQuestionsService.updateWjQuestions(wjQuestions));
    }

    /**
     * 删除问卷题库
     */
    @PreAuthorize("@ss.hasPermi('system:wjquestions:remove')")
    @Log(title = "问卷题库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionsIds}")
    public AjaxResult remove(@PathVariable Long[] questionsIds)
    {
        return toAjax(wjQuestionsService.deleteWjQuestionsByQuestionsIds(questionsIds));
    }


    /**
     * 导入问卷题库
     * */
    /**
     * 导入题库
     * */
    @PostMapping("/import")
    public BaseResponse importExcel(@RequestParam("file") MultipartFile file) {
        try {
            //获取输入流
            InputStream inputStream = file.getInputStream();
            //创建读取工作簿
            Workbook workbook = WorkbookFactory.create(inputStream);
            //获取工作表
            Sheet sheet = workbook.getSheetAt(0);
            //获取总行
            int rows = sheet.getPhysicalNumberOfRows();
            if (rows > 1) { // 表头占一行，所以判断条件为>1
                //获取单元格
                for (int i = 1; i < rows; i++) {
                    Row row = sheet.getRow(i);
                    WjQuestions wjQuestions = new WjQuestions();
                    String label = CommonUtil.getCellValue(row.getCell(0));
                    switch (label){
                        case "个性化问卷":
                            wjQuestions.setLabelId(3L);
                            break;
                        case "收集问卷":
                            wjQuestions.setLabelId(4L);
                            break;
                        default:
                            wjQuestions.setLabelId(null);
                            break;
                    }
                    String content = CommonUtil.getCellValue(row.getCell(1));
                    String type = CommonUtil.getCellValue(row.getCell(2));
                    String cueword = CommonUtil.getCellValue(row.getCell(3));
                    String a = CommonUtil.getCellValue(row.getCell(4));
                    String b = CommonUtil.getCellValue(row.getCell(5));
                    String c = CommonUtil.getCellValue(row.getCell(6));
                    String d = CommonUtil.getCellValue(row.getCell(7));
                    String options = "{}"; // 默认空JSON

                    // 检查题目类型是否有选项（单选题、多选题、不定项选择题）
                    boolean hasOptions = "单选题".equals(type) || "多选题".equals(type) || "不定项选择题".equals(type);

                    if (hasOptions) {
                        // 使用 StringBuilder 动态构建 options JSON
                        StringBuilder optionsBuilder = new StringBuilder("{");

                        // 检查每个选项是否有值，有则加入
                        if (StringUtils.isNotBlank(a)) optionsBuilder.append("\"A\": \"").append(a).append("\", ");
                        if (StringUtils.isNotBlank(b)) optionsBuilder.append("\"B\": \"").append(b).append("\", ");
                        if (StringUtils.isNotBlank(c)) optionsBuilder.append("\"C\": \"").append(c).append("\", ");
                        if (StringUtils.isNotBlank(d)) optionsBuilder.append("\"D\": \"").append(d).append("\", ");

                        // 移除最后的 ", "（如果有选项）
                        if (optionsBuilder.length() > 1) {
                            optionsBuilder.setLength(optionsBuilder.length() - 2);
                        }

                        optionsBuilder.append("}");
                        options = optionsBuilder.toString();
                    }
                    wjQuestions.setContent(content);
                    wjQuestions.setType(type);
                    wjQuestions.setCueword(cueword);
                    wjQuestions.setOptions(options);

                    wjQuestionsService.insertWjQuestions(wjQuestions);
                }
            }
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (InvalidFormatException e) {
            throw new RuntimeException(e);
        }
        return BaseResponse.ok("导入题目成功");
    }


}
