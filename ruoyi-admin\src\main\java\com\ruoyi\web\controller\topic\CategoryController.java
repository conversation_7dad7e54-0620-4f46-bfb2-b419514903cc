package com.ruoyi.web.controller.topic;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Category;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.service.CategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 分类管理
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/system/cate")
public class CategoryController extends BaseController {

    @Autowired
    private CategoryService categoryService;

    /**
     * 查询分类列表
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody QueryDTO category) {
        return success(categoryService.searchCate(category));
    }

    /**
     * 新增分类
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Category category) {
        return toAjax(categoryService.addCate(category));
    }

    @GetMapping("selectById/{categoryId}")
    public AjaxResult selectById(@PathVariable Integer categoryId) {
        Category category = categoryService.selectById(categoryId);
        return success(category);
    }
    /**
     * 修改分类
     */
    @PutMapping("/update")
    public AjaxResult update(@RequestBody Category category) {
        return toAjax(categoryService.updateCate(category));
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/delete/{categoryId}")
    public AjaxResult remove(@PathVariable Integer categoryId) {
        return toAjax(categoryService.removeCate(categoryId));
    }
}
