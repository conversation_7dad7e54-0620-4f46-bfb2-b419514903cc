package com.ruoyi.web.controller.topic;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Supervisor;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.service.SupervisorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 导师管理
 **/
@RestController
@RequestMapping("/system/supervisor")
public class SupervisorController extends BaseController {
    @Autowired
    private SupervisorService supervisorService;

    /**
     * 获取导师列表
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody QueryDTO supervisor) {
        return success(supervisorService.searchSuper(supervisor));
    }

    /**
     * 查看详情
     * */
    @GetMapping("/{supervisorId}")
    public AjaxResult get(@PathVariable Integer supervisorId) {
        return success(supervisorService.selectBySupervisorId(supervisorId));
    }

    /**
     * 添加导师
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Supervisor supervisor) {
        return toAjax(supervisorService.insertSupervisor(supervisor));
    }

    @GetMapping("selectById/{supervisorId}")
    public AjaxResult selectById(@PathVariable Integer supervisorId){
        return success(supervisorService.selectById(supervisorId));
    }

    /**
     * 修改导师
     */
    @PutMapping("/edit")
    public AjaxResult edit(@RequestBody Supervisor supervisor) {
        return toAjax(supervisorService.updateSupervisor(supervisor));
    }

    /**
     * 删除导师
     */
    @DeleteMapping("/remove/{superId}")
    public AjaxResult remove(@PathVariable Integer superId) {
        return toAjax(supervisorService.deleteSupervisorByIds(superId));
    }
}
