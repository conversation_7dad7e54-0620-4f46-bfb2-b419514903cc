package com.ruoyi.web.controller.topic;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.Topic;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.service.TopicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 毕设题目管理
 **/
@RestController
@RequestMapping("/system/topic")
public class TopicController extends BaseController {
    @Autowired
    private TopicService topicService;

    /**
     * 查询选题
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody QueryDTO topicQueryDTO) {
        return success(topicService.searchTopics(topicQueryDTO));
    }

    /**
     * 导出选题信息列表
     */
//    @PreAuthorize("@ss.hasPermi('system:topic:export')")
//    @Log(title = "选题信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, Topic topic)
//    {
//        List<Topic> list = topicService.selectTopicList(topic);
//        ExcelUtil<Topic> util = new ExcelUtil<Topic>(Topic.class);
//        util.exportExcel(response, list, "选题信息数据");
//    }


    /**
     * 后台-新增选题
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody Topic topic) {
        topicService.addTopic(topic);
        return success();
    }

    @GetMapping("/selectById/{topicId}")
    public AjaxResult selectById(@PathVariable Integer topicId) {
        return success(topicService.getTopicById(topicId));
    }

    /**
     * 后台-修改选题
     */
    @PutMapping("/update")
    public AjaxResult update( @RequestBody Topic topic) {
        topicService.updateTopic(topic);
        return success();
    }

    /**
     * 后台-删除选题
     */
    @DeleteMapping("/delete/{topicId}")
    public AjaxResult delete(@PathVariable Integer topicId) {
        topicService.deleteTopic(topicId);
        return success();
    }

    /**
     * 审核
     */
    @PutMapping("/check/{topicId}")
    public AjaxResult check(@PathVariable Integer topicId) {
        topicService.checkTopic(topicId);
        return success();
    }
}
