package com.ruoyi.common.base;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class BaseRequest<T> implements Serializable {
    @NotNull(message = "渠道参数错误")
    private String channelId;
    @NotNull(message = "设备ID参数错误")
    private String deviceNo;
    @NotNull(message = "请求端设备种类参数错误")
    private String deviceType;
    @NotNull(message = "请求端版本号参数错误")
    private String version;
    @NotNull(message = "请求端版本参数错误")
    private Integer versionCode;
    private String clientType;
    private String resolution;
    private String osVersion;
    private String deviceName;
    private String adCode;
    private String networkAccess;
    @Valid
    private T data;
}
