package com.ruoyi.common.base;

import java.io.Serializable;

/**
 * 通用响应类
 */
public class BaseResponse<T> implements Serializable {
    public static final BaseResponse OK = new BaseResponse<>(0, "成功", "");
    private Integer code;
    private String message;
    private T data;

    public BaseResponse(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> BaseResponse<T> ok(T data) {
        return new BaseResponse<>(0, "成功", data);
    }

    public static <T> BaseResponse<T> OK(T data) {
        return new BaseResponse<>(0, "成功", data);
    }

    public static BaseResponse error(String message) {
        return new BaseResponse<>(500, message, null);
    }

    public static <T> BaseResponse<T> error(String message, T data) {
        return new BaseResponse<>(500, message, data);
    }

    public static BaseResponse error(Integer code, String message) {
        return new BaseResponse<>(code, message, null);
    }

    public static <T> BaseResponse<T> error(Integer code, String message, T data) {
        return new BaseResponse<>(code, message, data);
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
