package com.ruoyi.common.constant;

/**
 * 简历和待办相关常量
 * 
 * <AUTHOR>
 */
public class ResumeConstants
{
    /**
     * 简历状态常量
     */
    public static final Integer RESUME_STATUS_DRAFT = 0;    // 草稿
    public static final Integer RESUME_STATUS_APPROVED = 1;    // 审核通过
    public static final Integer RESUME_STATUS_REJECTED = 2;    // 审核未通过
    public static final Integer RESUME_STATUS_DELETED = 3;    // 已删除
    
    /**
     * 简历进度阶段常量
     */
    public static final String STAGE_CREATE_RESUME = "0";   // 创建简历
    public static final String STAGE_KNOWLEDGE_LEARNING = "1";   // 知识学习
    public static final String STAGE_HR_INTERVIEW = "2";   // hr面试
    public static final String STAGE_TECHNICAL_INTERVIEW = "3";   // 技术面试
    public static final String STAGE_FORMAL_INTERVIEW = "4";   // 正式面试
    
    /**
     * 待办类型常量
     */
    public static final Integer TODO_TYPE_CREATE_RESUME = 0;    // 创建简历
    public static final Integer TODO_TYPE_KNOWLEDGE_LEARNING = 1;    // 知识学习
    public static final Integer TODO_TYPE_HR_INTERVIEW = 2;    // hr面试
    public static final Integer TODO_TYPE_TECHNICAL_INTERVIEW = 3;    // 技术面试
    public static final Integer TODO_TYPE_FORMAL_INTERVIEW = 4;    // 正式面试
    
    /**
     * 待办状态常量
     */
    public static final Integer TODO_STATUS_INCOMPLETE = 0;    // 未完成
    public static final Integer TODO_STATUS_COMPLETED = 1;    // 已完成
} 