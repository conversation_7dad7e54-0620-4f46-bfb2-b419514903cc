package com.ruoyi.common.utils;

public class TimeUtil {
    public static String formatTimeToHourMinSec(String timeStr) {
        if (timeStr == null || !timeStr.matches("\\d{2}:\\d{2}:\\d{2}")) {
            return "0秒";
        }
        String[] parts = timeStr.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        int seconds = Integer.parseInt(parts[2]);

        StringBuilder sb = new StringBuilder();
        if (hours > 0) {
            sb.append(hours).append("时");
        }
        if (minutes > 0 || hours > 0) {
            sb.append(minutes).append("分");
        }
        sb.append(seconds).append("秒");
        return sb.toString();
    }
}
