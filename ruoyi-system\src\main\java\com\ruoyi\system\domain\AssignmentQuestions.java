package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("assignment_questions")
public class AssignmentQuestions {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    private String assignmentId;
    private String questionId;
    private Integer score;
    private Integer orderNum;
    private Date createdAt;
    private Integer isDelete;
}