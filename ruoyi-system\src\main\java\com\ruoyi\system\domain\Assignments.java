package com.ruoyi.system.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Assignments {
    @TableId(value = "assignment_id", type = IdType.ASSIGN_ID)
    private String assignmentId;
    private String name;
    private String description;
    private Integer labelId;
    private Integer totalScore;
    private Integer classId;
    private String createdBy;
    private Date createdAt;
    private Date updatedAt;
    private Integer isDelete;
}