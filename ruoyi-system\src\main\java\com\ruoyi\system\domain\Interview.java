package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 面试实体类
 */
@Data
@TableName("interview")
public class Interview {

    /**
     * 面试ID
     */
    @TableId
    private String id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 面试类型(mock:模拟面试,formal:正式面试)
     */
    private String type;

    /**
     * 应聘者姓名
     */
    private String candidateName;

    /**
     * 面试公司(正式面试)
     */
    private String company;

    /**
     * 面试职位
     */
    private String position;

    /**
     * 面试阶段(hr/tech)
     */
    private String stage;

    /**
     * 工作经验(fresh/1-3/3-5/5+)
     */
    private String experience;

    /**
     * 面试状态(0:进行中,1:已完成,2:已取消)
     */
    private Integer status;

    /**
     * 总体评分(0-100)
     */
    private Integer overallScore;

    /**
     * 面试反馈
     */
    private String feedback;

    /**
     * 优势表现
     */
    private String strengths;

    /**
     * 改进建议
     */
    private String improvements;

    /**
     * 面试视频文件URL
     */
    private String videoUrl;

    /**
     * 面试结果(0:未通过,1:通过,NULL:未评定)
     */
    private Integer result;

    /**
     * 面试时间
     */
    private LocalDateTime interviewTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 