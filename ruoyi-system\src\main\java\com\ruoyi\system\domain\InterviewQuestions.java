package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 面试问题实体类
 */
@Data
@TableName("interview_questions")
public class InterviewQuestions {

    /**
     * 面试ID
     */
    @TableId
    private String interviewId;

    /**
     * 问题列表JSON
     */
    private String questions;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 