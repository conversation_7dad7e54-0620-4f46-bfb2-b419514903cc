package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class InterviewSpeechRecord {
    /**
     * 记录ID
     */
    private Long id;

    /**
     * 面试ID
     */
    private String interviewId;

    /**
     * 发言人ID
     */
    private Integer speakerId;

    /**
     * 开始时间(秒)
     */
    private Integer startTime;

    /**
     * 结束时间(秒)
     */
    private Integer endTime;

    /**
     * 发言内容
     */
    private String content;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}