package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 面试转写记录实体类
 */
@Data
@TableName("interview_transcription")
public class InterviewTranscription {

    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 面试ID
     */
    private String interviewId;

    /**
     * 问题索引
     */
    private Integer questionIndex;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 转写的回答内容
     */
    private String transcription;

    /**
     * 音频文件URL
     */
    private String audioUrl;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
} 