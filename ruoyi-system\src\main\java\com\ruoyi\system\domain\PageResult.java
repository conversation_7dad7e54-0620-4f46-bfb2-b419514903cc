package com.ruoyi.system.domain;

import lombok.Data;

import java.util.List;

@Data
public class PageResult<TopicItemVO> {
    private Integer pageNum;
    private Integer pageSize;
    private Long total;
    private List<TopicItemVO> topics;
    private Long availableTotal;
    public PageResult(Integer pageNum, Integer pageSize, Long total, List<TopicItemVO> topics) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.topics = topics;
    }
}
