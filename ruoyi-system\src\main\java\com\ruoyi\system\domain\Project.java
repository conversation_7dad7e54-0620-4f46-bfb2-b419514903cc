package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 项目实体类
 */
@Data
@TableName("resume_project")
public class Project {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long proId;

    /**
     * 项目名称
     */
    private String name;

    private Long catId;
    /**
     * 项目内容（字符串形式，用于兼容）
     * 项目内容（字符串形式，用于兼容）
     */
    private String content;

    private String createAt;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 