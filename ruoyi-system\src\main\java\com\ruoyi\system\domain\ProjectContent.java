package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 项目内容实体类
 */
@Data
@TableName("resume_project_content")
public class ProjectContent {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long conId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 内容文本
     */
    private String text;

    /**
     * 内容顺序
     */
    private Integer contentOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 