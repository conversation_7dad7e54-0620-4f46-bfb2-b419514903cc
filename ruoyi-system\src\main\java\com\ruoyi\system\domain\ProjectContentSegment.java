package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 项目内容段落实体类
 */
@Data
@TableName("resume_project_content_segment")
public class ProjectContentSegment {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long segId;

    /**
     * 项目内容ID
     */
    private Long contentId;

    /**
     * 段落文本
     */
    private String text;

    /**
     * 是否加粗
     */
    private Boolean isBold;

    /**
     * 段落顺序
     */
    private Integer segmentOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 