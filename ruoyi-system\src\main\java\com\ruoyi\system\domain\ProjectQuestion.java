package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 项目问题实体类
 */
@Data
@TableName("resume_project_question")
public class ProjectQuestion {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long queId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目内容ID
     */
    private Long conId;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 答案内容
     */
    private String answer;

    /**
     * 问题顺序
     */
    private Integer questionOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 