package com.ruoyi.system.domain;


import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QuestionnaireAnswer {
    @TableId
    private String answerId;
    private String questionnaireId;
    private String userId;
    private String questionsId;
    private String answer;
    private String status; // 状态：进行中、已完成
    private LocalDateTime submitTime;
    private LocalDateTime createdAt;
    private Integer isDelete;

}