package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Date;

@Data
@TableName("questionnaire_scores")
public class QuestionnaireScore {
    @TableId(value = "score_id", type = IdType.ASSIGN_ID)
    private String scoreId;
    private String questionnaireId;
    private String studentId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;
    private String timeSpent;
    private String status;
    private String comment;
    private Date createdAt;
    private Date updatedAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

}