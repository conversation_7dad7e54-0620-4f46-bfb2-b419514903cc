package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QuestionnaireVo {
    private String questionnaireId;
    private String title;
    private String description;
    private Integer lableId;
    private String lableName;
    private String className;
    private Integer classId;
    private String status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    private Integer duration;
    private List<WjQuestions> questionsList;
}