package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 问卷管理对象 questionnaires
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Questionnaires {
    private static final long serialVersionUID = 1L;

    /** 问卷ID */
    @TableId(value = "questionnaire_id", type = IdType.ASSIGN_ID)
    private String questionnaireId;

    /** 问卷标题 */
    @Excel(name = "问卷标题")
    private String title;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer lableId;

    /** 问卷描述 */
    @Excel(name = "问卷描述")
    private String description;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer classId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 填写时长(分钟) */
    @Excel(name = "填写时长(分钟)")
    private Long duration;

    /** 问卷状态 */
    @Excel(name = "问卷状态")
    private String status;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createdBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 0存在1删除 */
    @Excel(name = "0存在1删除")
    private Long isDelete;


}
