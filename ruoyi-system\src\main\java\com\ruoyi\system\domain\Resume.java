package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 简历主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("resume")
public class Resume implements Serializable {

    private static final long serialVersionUID=1L;
    
    /**
     * 简历ID
     */
    @TableId(value = "resume_id", type = IdType.AUTO)
    private Long resumeId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 简历标题
     */
    private String title;

    /**
     * 简历状态(0-草稿,1-已发布,2-已下线)
     */
    private Integer status;

    /**
     * 是否默认简历(0-否,1-是)
     */
    private Integer isDefault;

    /**
     * 简历模板ID
     */
    private Long templateId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建人
     */
    private String createAt;

    /**
     * 删除标识(0-未删除,1-已删除)
     */
    private Integer isDelete;

    /**
     * 审核意见
     */
    private String auditOpinion;
}
