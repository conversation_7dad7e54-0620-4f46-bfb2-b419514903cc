package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * HR问题实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("resume_hr_questions")
public class ResumeHrQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "que_id", type = IdType.AUTO)
    private Long queId;

    /**
     * 问题类型
     */
    private String questionType;

    /**
     * 题目内容
     */
    private String question;

    /**
     * 题目答案
     */
    private String answer;

    /**
     * 创建人
     */
    private String createAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标识(0-未删除,1-已删除)
     */
//    private Integer isDelete;
} 