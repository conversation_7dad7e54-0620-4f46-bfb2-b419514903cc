package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 项目内容段落表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("resume_project_content_segment")
public class ResumeProjectContentSegment implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "seg_id", type = IdType.AUTO)
    private Long segId;

    /**
     * 项目内容ID
     */
    private Long contentId;

    /**
     * 段落文本
     */
    private String text;

    /**
     * 是否加粗：0-否，1-是
     */
    private Boolean isBold;

    /**
     * 段落顺序
     */
    private Integer segmentOrder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


}
