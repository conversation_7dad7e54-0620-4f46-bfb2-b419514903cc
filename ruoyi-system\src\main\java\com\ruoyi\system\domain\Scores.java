package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Scores {
    
    /**
     * 成绩状态常量
     */
    public static final String STATUS_ONGOING = "考试中";    // 考试进行中
    public static final String STATUS_UNGRADED = "待批改";   // 已提交待批改
    public static final String STATUS_GRADED = "已完成";     // 已完成批改

    @TableId
    private String scoreId;
    private String examId;
    private String studentId;
    private Double totalScore;
    private String timeSpent;
    private String status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime submitTime;
    private String comment;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

}
