package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 技能段落实体类
 */
@Data
@TableName("resume_skill_segment")
public class SkillSegment {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long segId;

    /**
     * 技能ID
     */
    private Long skillId;
    
    /**
     * 技能点名称
     */
    private String segName;
    
    /**
     * 熟练度（一般、良好、熟练、擅长、精通）
     */
    private String proficiency;

    /**
     * 段落文本
     */
    private String text;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 