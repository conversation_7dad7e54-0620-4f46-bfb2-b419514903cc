package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 班级对象 student_class
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StudentClass {
    private static final long serialVersionUID = 1L;

    /** 班级ID */
    @TableId
    private Long classId;

    /** 班级名称 */
    @Excel(name = "班级名称")
    private String className;

    /** 班级人数 */
    private Long classNum;

    /** 创建者 */
    @Excel(name = "创建者")
    private String createdBy;

    /** 删除标志（0代表存在 2代表删除） */
    private Long isDelete;

}
