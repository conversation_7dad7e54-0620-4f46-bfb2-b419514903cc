package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @program: mozai-topic-system
 * @description: 导师表
 * @author: coke
 * @create: 2025-05-13 13:07
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Supervisor {

    /**
     * 主键
     */
    @TableId(value = "supervisor_id", type = IdType.AUTO)
    private Integer supervisorId;

    /**
     * 姓名
     */
    private String supervisorName;

    /**
     * 所属院系
     */
    private String department;

    /**
     * 职称
     */
    private String title;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String editTime;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
