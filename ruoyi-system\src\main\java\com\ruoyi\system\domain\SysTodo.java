package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 待办对象 sys_todo
 *
 * <AUTHOR>
 */
public class SysTodo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 待办ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 简历ID */
    @Excel(name = "简历ID")
    private Long resumeId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 链接URL */
    @Excel(name = "链接URL")
    private String url;

    /** 待办类型(0：创建简历；1：知识学习；2：hr面试；3：技术面试；4:正式面试) */
    @Excel(name = "待办类型", readConverterExp = "0=创建简历,1=知识学习,2=hr面试,3=技术面试,4=正式面试")
    private Integer todoType;

    /** 状态：0-未完成，1-已完成 */
    @Excel(name = "状态", readConverterExp = "0=未完成,1=已完成")
    private Integer status;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setResumeId(Long resumeId)
    {
        this.resumeId = resumeId;
    }

    public Long getResumeId()
    {
        return resumeId;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setUrl(String url)
    {
        this.url = url;
    }

    public String getUrl()
    {
        return url;
    }
    public void setTodoType(Integer todoType)
    {
        this.todoType = todoType;
    }

    public Integer getTodoType()
    {
        return todoType;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("userId", getUserId())
                .append("resumeId", getResumeId())
                .append("title", getTitle())
                .append("content", getContent())
                .append("url", getUrl())
                .append("todoType", getTodoType())
                .append("status", getStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
} 