package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("sys_wechat_auth")
public class SysWechatAuth {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Long userId;
    private String sceneStr;
    private String qrcodeUrl;
    private String openid;
    private String unionid;
    private String nickname;
    private Integer subscribe;
    private Integer status;
    private Date expireTime;
    private Date createTime;
    private Date updateTime;
} 