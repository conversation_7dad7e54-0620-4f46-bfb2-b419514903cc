package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @program: mozai-topic-system
 * @description: 选题信息表
 * @author: coke
 * @create: 2025-05-13 13:11
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class Topic {

    /**
     * 主键：选题id
     */
    @TableId(value = "topic_id", type = IdType.AUTO)
    private Integer topicId;

    /**
     * 选题名称
     */
    private String title;

    /**
     * 选题描述
     */
    private String description;

    /**
     * 选题负责人ID
     */
    private Integer supervisorId;

    /**
     * 选题类别ID
     */
    private String categoryId;

    /**
     * 分类名称（非数据库字段）
     */
    private String categoryName;

    /**
     * 导师姓名（非数据库字段）
     */
    private String supervisorName;

    /**
     * 标签
     */
    private String tag;


    /**
     * 选题发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishDate;

    /**
     * 选题截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deadline;

    /**
     * 可选人数（名额）
     */
    private Integer quota;

    /**
     * 选题状态
     */
    private Integer status;

    /**
     * 选题创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 选题更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date editTime;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
