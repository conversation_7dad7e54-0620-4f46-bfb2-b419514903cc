package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @program: mozai-topic-system
 * @description: 选题报名表
 * @author: coke
 * @create: 2025-05-13 13:22
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TopicApplication {
    /**
     * 主键: 选题报名id
     */
    @TableId(value = "application_id", type = IdType.AUTO)
    private Integer applicationId;

    /**
     * 选题id
     */
    private Integer topicId;

    /**
     * 学生id
     */
    private Integer uId;

    /**
     * 报名时间
     */
    private Date applicationDate;

    /**
     * 选题报名状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date editTime;

    /**
     * 是否删除
     */
    private Integer isDelete;


    public void setApplicationDate(Date applicationDate) {
        this.applicationDate = applicationDate;
    }
}
