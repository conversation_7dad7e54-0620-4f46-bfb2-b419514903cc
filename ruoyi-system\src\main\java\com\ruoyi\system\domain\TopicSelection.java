package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @program: mozai-topic-system
 * @description: 学生选题记录表
 * @author: coke
 * @create: 2025-05-13 13:35
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TopicSelection {

    /**
     * 主键: 学生选题记录id
     */
    @TableId(value = "selection_id", type = IdType.AUTO)
    private Integer SelectionId;

    /**
     * 学生id
     */
    private Integer uId;

    /**
     * 选题id
     */
    private Integer topicId;

    /**
     * 选题状态
     */
    private Integer status;

    /**
     * 选题时间
     */
    private Date selectionDate;

    public void setSelectionDate(Date selectionDate) {
        this.selectionDate = selectionDate;
    }

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date editTime;

    /**
     * 是否删除
     */
    private int isDelete;
}
