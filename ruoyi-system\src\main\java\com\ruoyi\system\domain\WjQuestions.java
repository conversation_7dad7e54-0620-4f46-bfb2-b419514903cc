package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 问卷题库对象 wj_questions
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("wj_questions")
public class WjQuestions {
    private static final long serialVersionUID = 1L;

    /** 问题ID */
    @TableId(value = "questions_id", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long questionsId;

    /** 问题内容 */
    @Excel(name = "问题内容")
    private String content;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String image;

    /** 问题提示词 */
    @Excel(name = "问题提示词")
    private String cueword;

    /** 问题类型 */
    @Excel(name = "问题类型")
    private String type;

    /** 所属类型 */
    @Excel(name = "所属类型")
    private Long labelId;

    /** 分值 */
    @Excel(name = "分值")
    private Long points;

    /** 选项(JSON格式) */
    @Excel(name = "选项(JSON格式)")
    private String options;

    /** 参考答案 */
    @Excel(name = "参考答案")
    private String answer;

    /** 解析 */
    @Excel(name = "解析")
    private String analysis;

    /** 是否随机选项 */
    @Excel(name = "是否随机选项")
    private Integer randomly;

    /** 是否必答 */
    @Excel(name = "是否必答")
    private Integer required;

    private String questionnaireId;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createdBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 0存在，1删除 */
    @Excel(name = "0存在，1删除")
    private Long isDelete;
}
