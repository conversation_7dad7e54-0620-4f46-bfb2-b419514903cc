package com.ruoyi.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.WjQuestions;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QuestionnaireCreateDTO {
    private String title;
    private Integer lableId;
    private String description;
    private Integer classId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    private Integer duration;
    private List<WjQuestions> questions;
}