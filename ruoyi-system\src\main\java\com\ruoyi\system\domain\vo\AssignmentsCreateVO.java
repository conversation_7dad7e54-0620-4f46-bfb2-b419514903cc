package com.ruoyi.system.domain.vo;

import lombok.Data;
import java.util.List;

@Data
public class AssignmentsCreateVO {
    private String assignmentId;
    private String title;
    private String description;
    private Integer labelId;
    private String labelName;
    private Integer totalScore;
    private Integer classId;
    private String className;
    private String createdBy;
    private List<AssignmentQuestionVO> questionsList;
    
    @Data
    public static class AssignmentQuestionVO {
        private String questionId;
        private String content;
        private String questionType;
        private String options;
        private String answer;
        private Integer score;
        private Integer orderNum;
        private String analysis;
    }
} 