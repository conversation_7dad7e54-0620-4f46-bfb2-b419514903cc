package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class AssignmentsPageVo {
    private String assignmentId;
    private String name;
    private String description;
    private Integer labelId;
    private String labelName;
    private Integer totalScore;
    private Integer classId;
    private String className;
    private String createdBy;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;
    private Integer isDelete;
}
