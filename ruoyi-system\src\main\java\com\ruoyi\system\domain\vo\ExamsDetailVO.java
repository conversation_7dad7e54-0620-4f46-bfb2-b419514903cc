package com.ruoyi.system.domain.vo;

import java.util.List;

public class ExamsDetailVO {
    private String examId;
    private String className;
    private Long totalCount;
    private Long submittedCount;
    private Long passCount;
    private double averageScore;
    private List<StudentExamVO> students;

    // getter/setter
    public String getExamId() { return examId; }
    public void setExamId(String examId) { this.examId = examId; }
    public String getClassName() { return className; }
    public void setClassName(String className) { this.className = className; }
    public Long getTotalCount() { return totalCount; }
    public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }
    public Long getSubmittedCount() { return submittedCount; }
    public void setSubmittedCount(Long submittedCount) { this.submittedCount = submittedCount; }
    public Long getPassCount() { return passCount; }
    public void setPassCount(Long passCount) { this.passCount = passCount; }
    public double getAverageScore() { return averageScore; }
    public void setAverageScore(double averageScore) { this.averageScore = averageScore; }
    public List<StudentExamVO> getStudents() { return students; }
    public void setStudents(List<StudentExamVO> students) { this.students = students; }

    @Override
    public String toString() {
        return "ExamsDetailVO{" +
                "examId='" + examId + '\'' +
                ", className='" + className + '\'' +
                ", totalCount=" + totalCount +
                ", submittedCount=" + submittedCount +
                ", passCount=" + passCount +
                ", averageScore=" + averageScore +
                ", students=" + students +
                '}';
    }
}