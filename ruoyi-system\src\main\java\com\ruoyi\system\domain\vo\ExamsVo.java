package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.Questions;
import lombok.Data;

import java.util.Date;
import java.util.List;

/*
* 编辑响应
* */
@Data
public class ExamsVo {
    private String examId; // 考试ID
    private String description; // 考试ID
    private String title;  // 考试标题
    private Integer labelId;  // 标签id
    private String labelName;  // 标签名称
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime; // 开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime; // 结束时间
    private String status; // 状态
    private Integer totalScore; // 总分
    private Long questionCount; // 题目数量
    private Integer classId;
    private String className; // 班级名称
    private Long participantCount; //
    private Long totalCount; //
    //题目列表
    private List<Questions>  questionsList;
}
