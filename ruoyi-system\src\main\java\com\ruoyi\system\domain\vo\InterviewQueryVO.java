package com.ruoyi.system.domain.vo;

import lombok.Data;

/**
 * 面试查询参数VO对象
 */
@Data
public class InterviewQueryVO {
    
    /**
     * 当前页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 应聘者姓名
     */
    private String candidateName;
    
    /**
     * 面试类型
     */
    private String type;
    
    /**
     * 面试职位
     */
    private String position;
    
    /**
     * 面试阶段
     */
    private String stage;
    
    /**
     * 面试状态
     */
    private Integer status;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
} 