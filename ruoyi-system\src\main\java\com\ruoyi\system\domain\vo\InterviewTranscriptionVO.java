package com.ruoyi.system.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 面试转写记录VO对象
 */
@Data
public class InterviewTranscriptionVO {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 面试ID
     */
    private String interviewId;
    
    /**
     * 问题索引
     */
    private Integer questionIndex;
    
    /**
     * 问题内容
     */
    private String question;
    
    /**
     * 转写的回答内容
     */
    private String transcription;
    
    /**
     * 音频文件URL
     */
    private String audioUrl;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 