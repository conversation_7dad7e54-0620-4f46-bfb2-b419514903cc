package com.ruoyi.system.domain.vo;

import lombok.Data;
import java.util.List;

@Data
public class QuestionnaireCreateVO {
    private String questionnaireId; // 修改时需要
    private String title;
    private Integer lableId;
    private String description;
    private Integer classId;
    private String startTime; // 建议用String，后端手动转Date
    private String endTime;
    private Integer duration;
    private List<QuestionVO> questions;

    @Data
    public static class QuestionVO {
        private String content;
        private String image;
        private String cueword;
        private String type;
        private Long labelId;
        private Long points;
        private String options;
        private String answer;
        private String analysis;
        private Integer randomly;
        private Integer required;
    }
}