package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QuestionnaireDetailVO {
    private Long totalCount; //总人数
    private Long submittedCount; //提交人数
    private Double completionRate; // 完成率
    private String averageTime; //平均用时

    private String questionnaireId; //问卷id
    private String title;  //问卷名称
    private String className; //班级名称
    private Integer lableId;
    private String lableName;
    private String status;

    private List<QuestionVO> questions;
    private List<ScoresVo> scoresVos;
    private List<AnswerRowVO> answerRows;

    @Data
    public static class ScoresVo{
        private String scoreId;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date startTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date submitTime;
        private String duration;
    }
    @Data
    public static class QuestionVO {
        private String questionId;
        private String content;
        private Integer orderNum;
    }

    @Data
    public static class AnswerRowVO {
        private String studentName;
        private String phone;
        private Integer index;
        private List<String> answers; // 每道题的答案，顺序与questions一致
    }
}