package com.ruoyi.system.domain.vo;

import lombok.Data;

import java.util.List;

/**保存或更新简历*/
@Data
public class ResumeFullSaveDto {
    private ResumeVo resumeVo;
    private ResumeInformationVo information; // 基本信息
    private List<ResumeEducationalVo> educationList; // 教育经历
    private List<ResumeWorkVo> workList; // 工作经验
    private List<ResumeProjectExperienceVo> projectList; // 项目经验
    private List<ResumePracticeVo> practiceList; // 练手项目
    private List<ResumeTalentVo> talentList; // 技能特长
    private List<ResumeCertificateVo> certificateList; // 证书奖项
    private List<ResumeCampusVo> campusList; // 校园经历
    private List<ResumeInterestVo> interestList; // 兴趣爱好
    private List<ResumeEvaluateVo> evaluateList; // 自我评价
    // 你可以根据实际表结构和字段名调整Vo类名
}