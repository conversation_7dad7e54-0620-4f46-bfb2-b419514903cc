package com.ruoyi.system.domain.vo;

import com.ruoyi.system.domain.ResumeSkillSegment;
import lombok.Data;

import java.util.List;
/**个人技能*/
@Data
public class ResumeSkillVo {
    private Long skiId;

    /**
     * 技能名称
     */
    private String name;

    /**
     * 熟练度（一般、良好、熟练、擅长、精通）
     */
    private String proficiency;

    /**
     * 技能描述（生成话语）
     */
    private String description;

    /**
     * 技能段落列表
     */
    private List<ResumeSkillSegment> segments;

}
