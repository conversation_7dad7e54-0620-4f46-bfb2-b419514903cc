package com.ruoyi.system.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
@Data
public class StudentAnswerRowVO {
    private int index; // 序号
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String duration; // 答题时长（秒）
    private List<String> answers; // 每题答案，顺序与题目一致
    private String studentName;
    private String studentNo;
    private String gender; // 性别
    private String phone;  // 手机号
    private String action; // 操作（如"预览"按钮等）
}