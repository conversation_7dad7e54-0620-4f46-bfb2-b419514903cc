package com.ruoyi.system.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TopicItemVO {
    // 课题ID
    private Integer topicId;
    // 课题标题
    private String title;
    // 课题导师姓名
    private String supervisorName;
    // 课题分类名称
    private String categoryName;
    // 课题标签
    private String tag;
    // 课题截止日期
    private LocalDateTime deadline;
    // 课题名额
    private Integer quota;
    // 课题状态
    private String status;
    // 课题可选个数
    private Integer optionalCount;
}
