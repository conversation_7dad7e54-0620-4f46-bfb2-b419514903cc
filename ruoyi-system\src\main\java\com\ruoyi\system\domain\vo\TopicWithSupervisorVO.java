package com.ruoyi.system.domain.vo;

import com.ruoyi.system.domain.Supervisor;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @program: mozai-back-stage
 * @description:
 * @author: coke
 * @create: 2025-05-24 17:47
 **/
@Data
public class TopicWithSupervisorVO {
    // 课题ID
    private Integer topicId;
    // 课题标题
    private String title;
    // 课题导师姓名
    private Supervisor supervisor;
    // 课题分类名称
    private String categoryName;
    // 课题标签
    private String tag;
    // 课题状态
    private Integer status;

    private String description;

    //时间注解
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishDate;

    /** 手机号 */
    private String phone;

    /** 昵称 */
    private String nickname;
}
