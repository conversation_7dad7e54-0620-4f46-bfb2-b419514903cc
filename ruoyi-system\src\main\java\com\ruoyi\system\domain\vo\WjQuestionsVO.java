package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
@Data
public class WjQuestionsVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long questionsId;

    /** 问题内容 */
    @Excel(name = "问题内容")
    private String content;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String image;

    /** 问题提示词 */
    @Excel(name = "问题提示词")
    private String cueword;

    /** 问题类型 */
    @Excel(name = "问题类型")
    private String type;

    /** 所属类型 */
    @Excel(name = "所属类型")
    private Long labelId;

    private String labelName;

    /** 分值 */
    @Excel(name = "分值")
    private Long points;

    /** 选项(JSON格式) */
    @Excel(name = "选项(JSON格式)")
    private String options;

    /** 参考答案 */
    @Excel(name = "参考答案")
    private String answer;

    /** 解析 */
    @Excel(name = "解析")
    private String analysis;

    /** 是否随机选项 */
    @Excel(name = "是否随机选项")
    private Integer randomly;

    /** 是否必答 */
    @Excel(name = "是否必答")
    private Integer required;

    private String questionnaireId;

    /** 创建人ID */
    @Excel(name = "创建人ID")
    private String createdBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updatedAt;

    /** 0存在，1删除 */
    @Excel(name = "0存在，1删除")
    private Long isDelete;
}
