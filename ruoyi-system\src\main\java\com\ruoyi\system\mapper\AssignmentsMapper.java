package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.Assignments;
import com.ruoyi.system.domain.dto.AssignmentsDTO;
import com.ruoyi.system.domain.vo.AssignmentsPageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AssignmentsMapper extends BaseMapper<Assignments> {
    
    /**
     * 查询作业列表
     */
    List<AssignmentsPageVo> selectAssignmentsList(AssignmentsDTO assignmentsDTO);

    /**
     * 查询作业详细信息
     */
    AssignmentsPageVo selectAssignmentsByAssignmentId(@Param("assignmentId") String assignmentId);

    /**
     * 批量删除作业
     */
    int deleteAssignmentsByAssignmentIds(@Param("list") List<Assignments> assignments);
}
