package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.Category;
import com.ruoyi.system.domain.dto.QueryDTO;

import java.util.List;

public interface CategoryMapper extends BaseMapper<Category> {
    List<Category> selectByCondition(QueryDTO queryDTO);
    int insertCate(Category category);
    int deleteCate(Integer categoryId);
    int updateCate(Category category);

    Category selectById(Integer categoryId);
    }
