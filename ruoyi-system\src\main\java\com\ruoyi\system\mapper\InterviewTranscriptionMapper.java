package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.InterviewTranscription;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 面试转写记录Mapper接口
 */
@Mapper
public interface InterviewTranscriptionMapper extends BaseMapper<InterviewTranscription> {

    List<InterviewTranscription> selectByInterviewId(@Param("interviewId") String id);
}