package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.ProjectContent;
import com.ruoyi.system.domain.vo.ProjectContentVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 项目内容Mapper接口
 */
@Mapper
public interface ProjectContentMapper extends BaseMapper<ProjectContent> {
    
    @Select("SELECT con_id as conId, project_id, text, content_order FROM resume_project_content WHERE project_id = #{projectId} ORDER BY content_order")
    List<ProjectContentVO> selectByProjectId(Long projectId);

    @Delete("DELETE FROM resume_project_content WHERE project_id = #{projectId} ")
    void deleteByProjectId(Long projectId);
}