package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.ProjectQuestion;
import com.ruoyi.system.domain.vo.ProjectQuestionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 项目问题Mapper接口
 */
@Mapper
public interface ProjectQuestionMapper extends BaseMapper<ProjectQuestion> {

    @Select("SELECT * FROM resume_project_question WHERE con_id = #{conId} ORDER BY question_order")
    List<ProjectQuestionVO> selectByContentId(Long conId);
}