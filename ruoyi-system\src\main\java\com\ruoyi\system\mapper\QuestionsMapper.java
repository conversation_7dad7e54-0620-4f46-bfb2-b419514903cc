package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.Questions;

import java.util.List;

public interface QuestionsMapper extends BaseMapper<Questions> {
    List<Questions> selectQuestionsList(Questions questions);

    /**
     * 批量新增题目
     *
     * @param questionsList 题目列表
     * @return 结果
     */
    public int batchInsertQuestions(List<Questions> questionsList);
}
