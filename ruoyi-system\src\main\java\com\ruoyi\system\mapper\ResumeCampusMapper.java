package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.ResumeCampus;
import com.ruoyi.system.domain.vo.ResumeCampusVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 校园经历表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeCampusMapper extends BaseMapper<ResumeCampus> {

    List<ResumeCampusVo> selectByResumeId(@Param("resumeId") Long resumeId);
}
