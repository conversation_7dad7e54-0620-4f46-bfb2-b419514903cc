package com.ruoyi.system.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.ResumeEvaluate;
import com.ruoyi.system.domain.vo.ResumeEvaluateVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 自我评价表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeEvaluateMapper extends BaseMapper<ResumeEvaluate> {

    List<ResumeEvaluateVo> selectByResumeId(@Param("resumeId") Long resumeId);
}
