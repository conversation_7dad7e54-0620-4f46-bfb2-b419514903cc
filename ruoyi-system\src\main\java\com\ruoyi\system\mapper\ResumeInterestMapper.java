package com.ruoyi.system.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.ResumeInterest;
import com.ruoyi.system.domain.vo.ResumeInterestVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 兴趣爱好表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeInterestMapper extends BaseMapper<ResumeInterest> {

    List<ResumeInterestVo> selectByResumeId(@Param("resumeId") Long resumeId);
}
