package com.ruoyi.system.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.ResumeProjectExperience;
import com.ruoyi.system.domain.vo.ResumeProjectExperienceVo;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <p>
 * 项目经验表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
public interface ResumeProjectExperienceMapper extends BaseMapper<ResumeProjectExperience> {

    List<ResumeProjectExperienceVo> selectByResumeId(@Param("resumeId") Long resumeId);
}
