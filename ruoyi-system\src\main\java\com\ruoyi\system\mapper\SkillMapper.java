package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.Skill;
import com.ruoyi.system.domain.vo.SkillSegmentVO;
import com.ruoyi.system.domain.vo.SkillVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 技能Mapper接口
 */
@Mapper
public interface SkillMapper extends BaseMapper<Skill> {
    List<SkillVO> selectSkills(String keyword);
    
    @Select("SELECT seg_id as segId, skill_id as skillId, seg_name as segName, proficiency, text " +
            "FROM resume_skill_segment WHERE skill_id = #{skillId}")
    List<SkillSegmentVO> selectSegmentsBySkillId(Long skillId);
}