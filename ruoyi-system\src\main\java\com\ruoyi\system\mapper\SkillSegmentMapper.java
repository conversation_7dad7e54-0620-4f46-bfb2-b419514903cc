package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.system.domain.SkillSegment;
import com.ruoyi.system.domain.vo.SkillSegmentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 技能段落Mapper接口
 */
@Mapper
public interface SkillSegmentMapper extends BaseMapper<SkillSegment> {
    
    /**
     * 分页查询技能段落列表
     * @param page 分页参数
     * @param name 技能名称
     * @param proficiency 熟练度
     * @return 分页结果
     */
    Page<SkillSegmentVO> selectSkillSegmentPage(Page<SkillSegmentVO> page, @Param("name") String name, @Param("proficiency") String proficiency);
    
    /**
     * 根据ID查询技能段落详情
     * @param segId 段落ID
     * @return 技能段落详情
     */
    SkillSegmentVO selectSkillSegmentById(@Param("segId") Long segId);
} 