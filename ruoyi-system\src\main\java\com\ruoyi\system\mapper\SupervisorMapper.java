package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.Supervisor;
import com.ruoyi.system.domain.dto.QueryDTO;

import java.util.List;

/**
 * @program: mozai-back-stage
 * @description:
 * @author: coke
 * @create: 2025-05-16 16:29
 **/
public interface SupervisorMapper extends BaseMapper<Supervisor> {
    List<Supervisor> selectByCondition(QueryDTO queryDTO);

    int deleteSuper(Integer superId);

    int updateSuper(Supervisor supervisor);

    Supervisor selectById(Integer superId);
}
