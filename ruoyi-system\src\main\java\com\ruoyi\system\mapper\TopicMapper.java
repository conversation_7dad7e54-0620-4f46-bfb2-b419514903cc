package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.Topic;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.domain.vo.TopicItemVO;
import com.ruoyi.system.domain.vo.TopicWithSupervisorVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TopicMapper extends BaseMapper<Topic> {
    List<TopicItemVO> selectByCondition(QueryDTO queryDTO);

    Long countByCondition(QueryDTO queryDTO);

    @Select("SELECT COUNT(*) FROM topic WHERE status = 1")
    Long countAvailableTopics();

    void updateStatus(int topicId);

    void insertTopic(Topic topic);

    void updateTopic(Topic topic);

    Topic selectDetailById(int id);

    Topic selectTopicById(int topicId);

    void checkTopic(Integer topicId);

    /**
     * 根据导师ID查询题目列表
     * @param supervisorId 导师ID
     * @return 题目列表
     */
    List<TopicWithSupervisorVO> selectBySupervisorId(@Param("supervisorId") Integer supervisorId);
}
