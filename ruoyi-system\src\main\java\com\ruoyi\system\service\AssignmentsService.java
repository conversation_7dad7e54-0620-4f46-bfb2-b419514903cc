package com.ruoyi.system.service;

import com.ruoyi.system.domain.Assignments;
import com.ruoyi.system.domain.dto.AssignmentsDTO;
import com.ruoyi.system.domain.vo.AssignmentsCreateVO;
import com.ruoyi.system.domain.vo.AssignmentsDetailVO;
import com.ruoyi.system.domain.vo.AssignmentsPageVo;
import java.util.List;

public interface AssignmentsService {
    /**
     * 查询作业列表
     */
    List<AssignmentsPageVo> selectAssignmentsLists(AssignmentsDTO assignmentsDTO);

    /**
     * 查询作业详细信息
     */
    AssignmentsDetailVO selectAssignmentsByAssignmentId(String assignmentId);

    /**
     * 查询作业编辑信息
     */
    AssignmentsCreateVO selectByAssignmentId(String assignmentId);

    /**
     * 新增作业
     */
//    int insertAssignmentsWithQuestions(AssignmentsCreateVO assignmentsCreateVO);

    /**
     * 修改作业
     */
    int updateAssignmentsWithQuestions(AssignmentsCreateVO assignmentsCreateVO);

    /**
     * 批量删除作业
     */
    int deleteAssignmentsByAssignmentIds(List<Assignments> assignments);
}
