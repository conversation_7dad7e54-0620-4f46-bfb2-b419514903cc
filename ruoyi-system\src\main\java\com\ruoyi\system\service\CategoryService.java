package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.Category;
import com.ruoyi.system.domain.PageResult;
import com.ruoyi.system.domain.dto.QueryDTO;

public interface CategoryService extends IService<Category> {

    PageResult<Category> searchCate(QueryDTO queryDTO);

    int addCate(Category category);

    int updateCate(Category category);

    int removeCate(Integer categoryId);

    Category selectById(Integer categoryId);
}
