package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.ResumeHrQuestion;
import com.ruoyi.system.domain.dto.ResumeHrQueDTO;

import java.util.List;

/**
 * HR问题服务接口
 */
public interface HrQuestionsService extends IService<ResumeHrQuestion> {

    /**
     * 查询HR问题列表
     * @param resumeHrQueDTO 查询条件
     * @return HR问题列表
     */
    List<ResumeHrQuestion> hrQuestionList(ResumeHrQueDTO resumeHrQueDTO);

    /**
     * 添加HR问题
     * @param question HR问题信息
     * @return 添加结果
     */
    boolean addQuestion(ResumeHrQuestion question);

    /**
     * 修改HR问题
     * @param question HR问题信息
     * @return 修改结果
     */
    boolean updateQuestion(ResumeHrQuestion question);

    /**
     * 删除HR问题
     * @param queId 问题ID
     * @return 删除结果
     */
    boolean deleteQuestion(Long queId);

    ResumeHrQuestion getQuestion(Long queId);
}
