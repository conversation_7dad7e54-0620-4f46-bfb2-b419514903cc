package com.ruoyi.system.service;

import com.ruoyi.system.domain.Exams;
import com.ruoyi.system.domain.dto.ExamsDTO;
import com.ruoyi.system.domain.dto.ExamsSelectDTO;
import com.ruoyi.system.domain.vo.*;

import java.util.List;

/**
 * 考试管理Service接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface IExamsService {
    /**
     * 查询考试管理
     *
     * @param examId 考试管理主键
     * @return 考试管理
     */
    public ExamsDetailVO selectExamsByExamId(String examId);

    /**
     * 查询考试管理列表
     *
     * @param exams 考试管理
     * @return 考试管理集合
     */
    public List<ExamsPageVo> selectExamsLists(ExamsDTO exams);


    /**
     * 批量删除考试管理
     *
     * @param examIds 需要删除的考试管理主键集合
     * @return 结果
     */
    public int deleteExamsByExamIds(List<Exams> examIds);


    /**
     * 新增考试信息（包含题目）
     *
     * @param examsCreateVO 考试信息（包含题目）
     * @return 结果
     */
    public int insertExamsWithQuestions(ExamsCreateVO examsCreateVO);

    /**
     * 修改考试信息（包含题目）
     *
     * @param examsCreateVO 考试信息（包含题目）
     * @return 结果
     */
    public int updateExamsWithQuestions(ExamsCreateVO examsCreateVO);

    List<ExamsVo> selectByExamId(String examId);

    StudentExamVO selectListByDto(ExamsSelectDTO examsSelectDTO);
}
