package com.ruoyi.system.service;

import com.ruoyi.system.domain.QuestionnaireVo;
import com.ruoyi.system.domain.Questionnaires;
import com.ruoyi.system.domain.dto.QuestionnaireCreateDTO;
import com.ruoyi.system.domain.vo.QuestionnaireCreateVO;
import com.ruoyi.system.domain.vo.QuestionnaireDetailVO;
import com.ruoyi.system.domain.vo.QuestionnairesVo;

import java.util.List;

/**
 * 问卷管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface IQuestionnairesService {
    /**
     * 查询问卷管理
     * 
     * @param questionnaireId 问卷管理主键
     * @return 问卷管理
     */
    public QuestionnaireDetailVO selectQuestionnaireDetail(String questionnaireId);

    /**
     * 查询问卷管理列表
     * 
     * @param questionnaires 问卷管理
     * @return 问卷管理集合
     */
    public List<QuestionnairesVo> selectQuestionnairesList(Questionnaires questionnaires);

    /**
     * 新增问卷管理
     * 
     * @pa
     * @return 结果
     */
    public int insertQuestionnaires(QuestionnaireCreateDTO questionnaireCreateDTO);

    /**
     * 修改问卷管理
     * @return 结果
     */
    public int updateQuestionnaires(QuestionnaireCreateVO vo);

    /**
     * 批量删除问卷管理
     *
     * @return 结果
     */
    public int deleteQuestionnairesByQuestionnaireIds(List<Questionnaires> questionnairesList);

    QuestionnaireVo selectByQuestionnaireId(String questionnaireId);
}
