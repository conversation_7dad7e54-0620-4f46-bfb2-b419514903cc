package com.ruoyi.system.service;

import com.ruoyi.system.domain.Questions;

import java.util.List;

/**
 * 题库管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface IQuestionsService 
{
    /**
     * 查询题库管理
     * 
     * @param questionId 题库管理主键
     * @return 题库管理
     */
    public Questions selectQuestionsByQuestionId(String questionId);

    /**
     * 查询题库管理列表
     * 
     * @param questions 题库管理
     * @return 题库管理集合
     */
    public List<Questions> selectQuestionsList(Questions questions);

    /**
     * 新增题库管理
     * 
     * @param questions 题库管理
     * @return 结果
     */
    public int insertQuestions(Questions questions);

    /**
     * 修改题库管理
     * 
     * @param questions 题库管理
     * @return 结果
     */
    public int updateQuestions(Questions questions);

    /**
     * 批量删除题库管理
     * 
     * @param questionIds 需要删除的题库管理主键集合
     * @return 结果
     */
    public int deleteQuestionsByQuestionIds(List<Questions> questionIds);

    /**
     * 删除题库管理信息
     * 
     * @param questionId 题库管理主键
     * @return 结果
     */
    public int deleteQuestionsByQuestionId(String questionId);

    /**
     * 批量插入题目
     *
     * @param questionsList 题目列表
     * @return 结果
     */
    public int batchInsertQuestions(List<Questions> questionsList);
}
