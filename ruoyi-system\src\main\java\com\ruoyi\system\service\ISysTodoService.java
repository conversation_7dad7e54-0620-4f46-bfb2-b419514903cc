package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.SysTodo;

/**
 * 待办Service接口
 * 
 * <AUTHOR>
 */
public interface ISysTodoService 
{
    /**
     * 查询待办
     * 
     * @param id 待办ID
     * @return 待办
     */
    public SysTodo selectSysTodoById(Long id);

    /**
     * 查询待办列表
     * 
     * @param sysTodo 待办
     * @return 待办集合
     */
    public List<SysTodo> selectSysTodoList(SysTodo sysTodo);

    /**
     * 查询用户待办列表
     * 
     * @param userId 用户ID
     * @return 待办集合
     */
    public List<SysTodo> selectSysTodoByUserId(Long userId);

    /**
     * 新增待办
     * 
     * @param sysTodo 待办
     * @return 结果
     */
    public int insertSysTodo(SysTodo sysTodo);

    /**
     * 修改待办
     * 
     * @param sysTodo 待办
     * @return 结果
     */
    public int updateSysTodo(SysTodo sysTodo);

    /**
     * 批量删除待办
     * 
     * @param ids 需要删除的待办ID
     * @return 结果
     */
    public int deleteSysTodoByIds(Long[] ids);

    /**
     * 删除待办信息
     * 
     * @param id 待办ID
     * @return 结果
     */
    public int deleteSysTodoById(Long id);
} 