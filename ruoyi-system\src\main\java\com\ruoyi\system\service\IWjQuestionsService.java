package com.ruoyi.system.service;

import com.ruoyi.system.domain.WjQuestions;

import java.util.List;

/**
 * 问卷题库Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface IWjQuestionsService 
{
    /**
     * 查询问卷题库
     * 
     * @param questionsId 问卷题库主键
     * @return 问卷题库
     */
    public WjQuestions selectWjQuestionsByQuestionsId(Long questionsId);

    /**
     * 查询问卷题库列表
     *
     * @param wjQuestions 问卷题库
     * @return 问卷题库集合
     */
    public List<WjQuestions> selectWjQuestionsList(WjQuestions wjQuestions);

    /**
     * 新增问卷题库
     * 
     * @param wjQuestions 问卷题库
     * @return 结果
     */
    public int insertWjQuestions(WjQuestions wjQuestions);

    /**
     * 修改问卷题库
     * 
     * @param wjQuestions 问卷题库
     * @return 结果
     */
    public int updateWjQuestions(WjQuestions wjQuestions);

    /**
     * 批量删除问卷题库
     * 
     * @param questionsIds 需要删除的问卷题库主键集合
     * @return 结果
     */
    public int deleteWjQuestionsByQuestionsIds(Long[] questionsIds);

    long selectWjQuestionsCount(WjQuestions wjQuestions);

    /* *//**
     * 删除问卷题库信息
     * 
     * @param questionsId 问卷题库主键
     * @return 结果
     *//*
    public int deleteWjQuestionsByQuestionsId(Long questionsId);

    *//**
     * 批量获取问卷题目信息
     * 
     * @param questionIds 题目ID列表
     * @return 问卷题目列表
     *//*
    public List<WjQuestions> selectWjQuestionsByIds(List<String> questionIds);*/
}
