package com.ruoyi.system.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.Interview;
import com.ruoyi.system.domain.InterviewSpeechRecord;
import com.ruoyi.system.domain.InterviewTranscription;
import com.ruoyi.system.domain.dto.FeedbackDto;
import com.ruoyi.system.domain.dto.InterviewSpeechChapterDto;
import com.ruoyi.system.domain.vo.InterviewQueryVO;
import com.ruoyi.system.domain.vo.InterviewVO;

import java.util.List;

/**
 * 面试服务接口
 */
public interface InterviewService extends IService<Interview> {
    
    /**
     * 分页查询面试列表
     * @param queryVO 查询参数
     * @return 分页结果
     */
    Page<InterviewVO> pageInterviews(InterviewQueryVO queryVO);
    
    /**
     * 获取面试详情
     * @param id 面试ID
     * @return 面试详情
     */
    InterviewVO getInterviewDetail(String id);
    
    /**
     * 新增面试
     * @param interviewVO 面试信息
     * @return 是否成功
     */
    boolean saveInterview(InterviewVO interviewVO);
    
    /**
     * 更新面试
     * @param interviewVO 面试信息
     * @return 是否成功
     */
    boolean updateInterview(InterviewVO interviewVO);
    
    /**
     * 删除面试
     * @param id 面试ID
     * @return 是否成功
     */
    boolean deleteInterview(String id);
    
    /**
     * 更新面试状态
     * @param id 面试ID
     * @param status 面试状态
     * @return 是否成功
     */
    boolean updateInterviewStatus(String id, Integer status);
    
    /**
     * 更新面试评分和反馈
     * @param id 面试ID
     * @param score 评分
     * @param feedback 反馈
     * @param strengths 优势
     * @param improvements 改进建议
     * @return 是否成功
     */
    boolean updateInterviewFeedback(FeedbackDto feedbackDto);

    List<InterviewSpeechRecord> getSpeechRecord(String id);

    List<InterviewTranscription> getTranscriptionList(String interviewId);
}