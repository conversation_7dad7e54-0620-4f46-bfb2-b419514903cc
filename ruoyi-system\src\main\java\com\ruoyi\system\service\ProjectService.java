package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.Project;
import com.ruoyi.system.domain.vo.AIQuestionsVo;
import com.ruoyi.system.domain.vo.ProjectVO;


import java.util.List;

/**
 * 项目服务接口
 */
public interface ProjectService extends IService<Project> {
    
    /**
     * 获取所有项目列表
     */
    List<ProjectVO> listAllProjects(String keyword);
    
    /**
     * 分页查询项目列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 项目名称(可选)
     * @param role 角色(可选)
     * @return 分页结果
     */
    Page<ProjectVO> pageProjects(Integer pageNum, Integer pageSize, String name, String role);
    
    /**
     * 根据ID获取项目详情
     */
    ProjectVO getProjectById(Long id);
    
    /**
     * 保存项目信息（含内容和问题）
     */
    boolean saveProjectWithDetails(ProjectVO projectVO);
    
    /**
     * 更新项目信息（含内容和问题）
     */
    boolean updateProjectWithDetails(ProjectVO projectVO);
    
    /**
     * 删除项目信息（含内容和问题）
     */
    boolean deleteProjectWithDetails(Long id);

    List<AIQuestionsVo> selectByContent(String content) throws Exception;

}