package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.dto.ResumeDTO;
import com.ruoyi.system.domain.dto.ResumeHrQueDTO;
import com.ruoyi.system.domain.vo.ResumeFullSaveDto;

import java.util.List;

public interface ResumeService extends IService<Resume> {

    List<ResumeFullSaveDto> getList(ResumeDTO resumeDto);

    ResumeFullSaveDto getResumeDetail(Long resumeId);

    /**
     * 审核简历
     * @param resumeId 简历ID
     * @param status 审核状态
     * @param auditOpinion 审核意见
     * @return 审核结果
     */
    boolean auditResume(Long resumeId, Integer status, String auditOpinion);


}