package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.Skill;
import com.ruoyi.system.domain.SkillSegment;
import com.ruoyi.system.domain.vo.SkillSegmentVO;
import com.ruoyi.system.domain.vo.SkillVO;

import java.util.List;

/**
 * 技能服务接口
 */
public interface SkillService extends IService<Skill> {
    
    /**
     * 获取所有技能列表
     */
    List<SkillVO> listAllSkills(String keyword);
    
    /**
     * 分页查询技能段落列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param name 技能名称(可选)
     * @param proficiency 熟练度(可选)
     * @return 分页结果
     */
    Page<SkillSegmentVO> pageSkillSegments(Integer pageNum, Integer pageSize, String name, String proficiency);
    
    /**
     * 根据ID获取技能详情
     */
    SkillVO getSkillById(Long id);
    
    /**
     * 根据ID获取技能段落详情
     */
    SkillSegmentVO getSkillSegmentById(Long segId);
    
    /**
     * 保存技能段落
     */
    boolean saveSkillSegment(SkillSegmentVO segmentVO);
    
    /**
     * 更新技能段落
     */
    boolean updateSkillSegment(SkillSegmentVO segmentVO);
    
    /**
     * 删除技能段落
     */
    boolean deleteSkillSegment(Long segId);
    
    /**
     * 保存技能信息（含段落）
     * @deprecated 使用saveSkillSegment替代
     */
    @Deprecated
    boolean saveSkillWithSegments(SkillVO skillVO);
    
    /**
     * 更新技能信息（含段落）
     * @deprecated 使用updateSkillSegment替代
     */
    @Deprecated
    boolean updateSkillWithSegments(SkillVO skillVO);
    
    /**
     * 删除技能信息（含段落）
     * @deprecated 使用deleteSkillSegment替代
     */
    @Deprecated
    boolean deleteSkillWithSegments(Long id);
} 