package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.PageResult;
import com.ruoyi.system.domain.Supervisor;
import com.ruoyi.system.domain.Topic;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.domain.vo.SupervisorWithTopicsVO;

import java.util.List;

/**
 * @program: mozai-back-stage
 * @description:
 * @author: coke
 * @create: 2025-05-16 16:27
 **/
public interface SupervisorService extends IService<Supervisor> {
    /**
     * 分页查询导师列表，包含导师发布的题目信息
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<Supervisor> searchSuper(QueryDTO queryDTO);

    int insertSupervisor(Supervisor supervisor);

    int updateSupervisor(Supervisor supervisor);

    int deleteSupervisorByIds(Integer superId);

    Supervisor selectById(Integer supervisorId);

    SupervisorWithTopicsVO selectBySupervisorId(Integer supervisorId);
}
