package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.PageResult;
import com.ruoyi.system.domain.Topic;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.domain.vo.TopicItemVO;
import org.springframework.transaction.annotation.Transactional;

public interface TopicService extends IService<Topic> {
    PageResult<TopicItemVO> searchTopics(QueryDTO topicQueryDTO);
    Topic getTopicById(int id);

    void addTopic(Topic topic);

    @Transactional
    void updateTopic(Topic topic);

    @Transactional
    void deleteTopic(int topicId);

    void checkTopic(Integer topicId);
}
