package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.TimeUtil;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.dto.AssignmentsDTO;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.AssignmentsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AssignmentsServiceImpl implements AssignmentsService {

    @Resource
    private AssignmentsMapper assignmentsMapper;

    @Resource
    private AssignmentQuestionsMapper assignmentQuestionsMapper;

    @Resource
    private StudentClassMapper studentClassMapper;

    @Resource
    private LabelsMapper labelsMapper;

    @Resource
    private AssignmentScoresMapper assignmentScoresMapper;

    @Resource
    private QuestionsMapper questionsMapper;

    @Override
    public List<AssignmentsPageVo> selectAssignmentsLists(AssignmentsDTO assignmentsDTO) {
        return assignmentsMapper.selectAssignmentsList(assignmentsDTO);
    }

    @Override
    public AssignmentsDetailVO selectAssignmentsByAssignmentId(String assignmentId) {
        // 1. 获取作业基本信息
        Assignments assignment = assignmentsMapper.selectById(assignmentId);
        if (assignment == null) {
            return null;
        }

        AssignmentsDetailVO vo = new AssignmentsDetailVO();
        BeanUtils.copyProperties(assignment, vo);

        // 2. 获取班级信息
        StudentClass studentClass = studentClassMapper.selectById(assignment.getClassId());
        vo.setClassName(studentClass != null ? studentClass.getClassName() : "");
        vo.setTotalCount(studentClass != null ? studentClass.getClassNum() : 0L);

        // 3. 获取标签信息
        Labels labels = labelsMapper.selectById(assignment.getLabelId());
        vo.setLabelName(labels != null ? labels.getLabelName() : "");

        // 4. 获取所有学生
        List<KsUser> students = studentClassMapper.selectByClassId(assignment.getClassId());
        List<StudentAssignmentVO> studentList = new ArrayList<>();
        Long submittedCount = 0L, passCount = 0L;
        double totalScore = 0;

        for (KsUser student : students) {
            StudentAssignmentVO studentVO = new StudentAssignmentVO();
            studentVO.setStudentNo(student.getPhone());
            studentVO.setStudentName(student.getNickname());

            AssignmentScores score = assignmentScoresMapper.selectOne(
                new LambdaQueryWrapper<AssignmentScores>()
                    .eq(AssignmentScores::getAssignmentId, assignmentId)
                    .eq(AssignmentScores::getStudentId, student.getUId().toString())
            );

            if (score != null) {
                studentVO.setScore(score.getTotalScore());
                studentVO.setStatus(score.getStatus());
                studentVO.setSubmitTime(score.getSubmitTime());
                studentVO.setDuration(TimeUtil.formatTimeToHourMinSec(score.getTimeSpent()));
                studentVO.setComment(score.getComment());

                submittedCount++;
                totalScore += score.getTotalScore();
                if ("已完成".equals(score.getStatus())) {
                    passCount++;
                }
            } else {
                studentVO.setStatus("未开始");
            }

            studentList.add(studentVO);
        }

        vo.setStudents(studentList);
        vo.setSubmittedCount(submittedCount);
        vo.setPassCount(passCount);
        vo.setAverageScore(submittedCount > 0 ? totalScore / submittedCount : 0);

        return vo;
    }

    @Override
    public AssignmentsCreateVO selectByAssignmentId(String assignmentId) {
        // 1. 获取作业基本信息
        Assignments assignment = assignmentsMapper.selectById(assignmentId);
        if (assignment == null) {
            return null;
        }

        // 2. 获取作业题目信息
        LambdaQueryWrapper<AssignmentQuestions> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AssignmentQuestions::getAssignmentId, assignmentId)
               .orderByAsc(AssignmentQuestions::getOrderNum);
        List<AssignmentQuestions> questions = assignmentQuestionsMapper.selectList(wrapper);

        // 3. 获取班级信息
        StudentClass studentClass = studentClassMapper.selectById(assignment.getClassId());
        
        // 4. 获取标签信息
        Labels labels = labelsMapper.selectById(assignment.getLabelId());

        // 5. 组装返回数据
        AssignmentsCreateVO vo = new AssignmentsCreateVO();
        BeanUtils.copyProperties(assignment, vo);
        vo.setTitle(assignment.getName());
        vo.setClassName(studentClass != null ? studentClass.getClassName() : "");
        vo.setLabelName(labels != null ? labels.getLabelName() : "");
        
        // 6. 获取题目详细信息
        List<AssignmentsCreateVO.AssignmentQuestionVO> questionVOs = questions.stream()
            .map(q -> {
                AssignmentsCreateVO.AssignmentQuestionVO questionVO = new AssignmentsCreateVO.AssignmentQuestionVO();
                questionVO.setQuestionId(q.getQuestionId());
                questionVO.setScore(q.getScore());
                questionVO.setOrderNum(q.getOrderNum());
                
                // 获取题目详细信息
                Questions question = questionsMapper.selectById(q.getQuestionId());
                if (question != null) {
                    questionVO.setContent(question.getContent());
                    questionVO.setQuestionType(question.getType());
                    questionVO.setOptions(question.getOptions());
                    questionVO.setAnswer(question.getAnswer());
                    questionVO.setAnalysis(question.getAnalysis());
                }
                
                return questionVO;
            })
            .collect(Collectors.toList());
        vo.setQuestionsList(questionVOs);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAssignmentsWithQuestions(AssignmentsCreateVO assignmentsCreateVO) {
        // 1. 更新作业基本信息
        Assignments assignment = new Assignments();
        assignment.setName(assignmentsCreateVO.getTitle());

        BeanUtils.copyProperties(assignmentsCreateVO, assignment);
        assignment.setUpdatedAt(new Date());
        assignmentsMapper.updateById(assignment);

        // 2. 删除原有题目关联
        assignmentQuestionsMapper.delete(new LambdaQueryWrapper<AssignmentQuestions>().eq(AssignmentQuestions::getAssignmentId,assignment.getAssignmentId()));


        // 3. 保存新的题目关联
        if (StringUtils.isNotEmpty(assignmentsCreateVO.getQuestionsList())) {
            List<AssignmentQuestions> questions = assignmentsCreateVO.getQuestionsList().stream()
                .map(q -> {
                    AssignmentQuestions question = new AssignmentQuestions();
                    question.setAssignmentId(assignment.getAssignmentId());
                    question.setQuestionId(q.getQuestionId());
                    question.setScore(q.getScore());
                    question.setOrderNum(q.getOrderNum());
                    return question;
                })
                .collect(Collectors.toList());
            // 批量插入新的作业题目关联
            for (AssignmentQuestions assignmentQuestion : questions) {
                assignmentQuestionsMapper.insert(assignmentQuestion);
            }
        }

        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAssignmentsByAssignmentIds(List<Assignments> assignments) {
        List<String> assignmentIds = assignments.stream()
                .map(Assignments::getAssignmentId)
                .collect(Collectors.toList());

        // 1. 删除作业题目关联
        for (String assignmentId : assignmentIds) {
            AssignmentQuestions assignmentQuestions = new AssignmentQuestions();
            assignmentQuestions.setAssignmentId(assignmentId);
            assignmentQuestionsMapper.updateByAssignmentId(assignmentQuestions);
        }


        // 2. 删除作业基本信息
        for (Assignments assignment : assignments) {
            assignmentsMapper.updateById(assignment);
        }

        return 1;
    }
}
