package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ruoyi.system.domain.Category;
import com.ruoyi.system.domain.PageResult;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.mapper.CategoryMapper;
import com.ruoyi.system.service.CategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    @Autowired
    private CategoryMapper categoryMapper;

    @Override
    public PageResult<Category> searchCate(QueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<Category> topics = baseMapper.selectByCondition(queryDTO);
        PageResult<Category> result = new PageResult<>(queryDTO.getPageNum(), queryDTO.getPageSize(),
                ((com.github.pagehelper.Page<?>) topics).getTotal(), topics);
        return result;
    }

    @Override
    public int addCate(Category category) {
        category.setCreateTime(new Date());
        category.setIsDelete(0);
        return baseMapper.insertCate(category);
    }

    @Override
    public int updateCate(Category category) {
        category.setIsDelete(0);
        category.setEditTime(new Date());
        return baseMapper.updateCate(category);
    }

    @Override
    public int removeCate(Integer categoryId) {
        return baseMapper.deleteCate(categoryId);
    }

    @Override
    public Category selectById(Integer categoryId) {
        return categoryMapper.selectById(categoryId);
    }
}
