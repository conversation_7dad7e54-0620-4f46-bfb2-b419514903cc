package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.Classification;
import com.ruoyi.system.mapper.ClassificationMapper;
import com.ruoyi.system.service.ClassificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ClassificationServiceImpl implements ClassificationService {

    @Autowired
    private ClassificationMapper classificationMapper;


    @Override
    public List<Classification> selectList() {
        return classificationMapper.selectList(new LambdaQueryWrapper<>());
    }
}
