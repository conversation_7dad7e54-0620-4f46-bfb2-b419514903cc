package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.TimeUtil;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.dto.ExamsDTO;
import com.ruoyi.system.domain.dto.ExamsSelectDTO;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.IExamsService;
import com.ruoyi.system.service.IQuestionsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考试管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
public class ExamsServiceImpl implements IExamsService {
    @Autowired
    private ExamsMapper examsMapper;

    @Autowired
    private StudentClassMapper studentClassMapper;

    @Autowired
    private ScoreMapper scoreMapper;

    @Autowired
    private LabelsMapper labelsMapper;

    @Autowired
    private ExamQuestionsMapper examQuestionsMapper;

    @Autowired
    private AssignmentQuestionsMapper assignmentQuestionsMapper;

    @Autowired
    private AssignmentsMapper assignmentsMapper;

    @Autowired
    private IQuestionsService questionsService;

    @Autowired
    private KsUserMapper ksUserMapper;


    /**
     * 查询考试管理
     * 
     * @param examId 考试管理主键
     * @return 考试管理
     */
    @Override
    public ExamsDetailVO selectExamsByExamId(String examId) {
        Exams exam = examsMapper.selectById(examId);
        ExamsDetailVO vo = new ExamsDetailVO();
        vo.setExamId(exam.getExamId());

        // 查询班级信息
        StudentClass studentClass = studentClassMapper.selectById(exam.getClassId());
        vo.setClassName(studentClass != null ? studentClass.getClassName() : "");

        // 查询班级总人数
        vo.setTotalCount(studentClass.getClassNum());

        // 查询所有学生
        List<KsUser> students = studentClassMapper.selectByClassId(exam.getClassId());
        List<StudentExamVO> studentList = new ArrayList<>();
        Long submittedCount = 0L, passCount = 0L;
        double totalScore = 0;
        int index = 1;
        for (KsUser stu : students) {
            StudentExamVO svo = new StudentExamVO();
            svo.setIndex(index++);
            svo.setStudentName(stu.getNickname());
            svo.setStudentNo(stu.getPhone());
            // 查询答题记录
            Scores scores = scoreMapper.findByExamIdAndUid(examId, stu.getUId(),null);
            if (scores != null) {
                svo.setStatus(scores.getStatus()); // 已完成/进行中/未开始
                svo.setScore(scores.getTotalScore());
                svo.setSubmitTime(scores.getSubmitTime());
                svo.setDuration(TimeUtil.formatTimeToHourMinSec(scores.getTimeSpent()));
                submittedCount++;
                if (scores.getTotalScore() != null && scores.getTotalScore() >= 60) passCount++;
                if (scores.getTotalScore() != null) totalScore += scores.getTotalScore();
            } else {
                svo.setStatus("未开始");
                svo.setScore(null);
                svo.setSubmitTime(null);
                svo.setDuration(null);
            }
            studentList.add(svo);
        }
        vo.setStudents(studentList);
        vo.setSubmittedCount(submittedCount);
        vo.setPassCount(passCount);
        vo.setAverageScore(submittedCount == 0 ? 0 : Math.round(totalScore * 10.0 / submittedCount) / 10.0); // 保留1位小数
        return vo;
    }



    @Override
    public List<ExamsPageVo> selectExamsLists(ExamsDTO exams) {
        List<ExamsPageVo> examsPageVos = new ArrayList<>();
        List<ExamsPageVo> exams1 = examsMapper.selectExamsList(exams);
        for (ExamsPageVo exams2 : exams1) {
            // 获取标签信息
            Labels labels = labelsMapper.selectById(exams2.getLabelId());
            // 判断 lables 是否为 null
            if (labels != null) {
                exams2.setLabelName(labels.getLabelName());
            }
            // 获取班级信息
            StudentClass studentClass = studentClassMapper.selectById(exams2.getClassId());
            if (studentClass != null) {
                exams2.setTotalCount(studentClass.getClassNum());
            }
            //查询答题人数
            Long participantCount = scoreMapper.selectCount(new LambdaQueryWrapper<Scores>().eq(Scores::getExamId,exams2.getExamId()));
            // 获取题目数量
            Long count = examQuestionsMapper.selectCount(new LambdaQueryWrapper<ExamQuestions>().eq(ExamQuestions::getExamId,exams2.getExamId()));
            exams2.setQuestionCount(count != null ? count : 0L);
            exams2.setParticipantCount(participantCount);
            examsPageVos.add(exams2);
        }
        return examsPageVos;
    }


    /**
     * 批量删除考试管理
     * 
     * @param
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteExamsByExamIds(List<Exams> examsList) {
        int a = 0;
        for (Exams exams : examsList) {
            a = examsMapper.updateById(exams);
        }
        return a;
    }


    /**
     * 新增考试信息（包含题目）
     *
     * @param examsCreateVO 考试信息（包含题目）
     * @return 考试ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertExamsWithQuestions(ExamsCreateVO examsCreateVO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        Date now = new Date();

        if ("作业".equals(examsCreateVO.getDistinguish())) {
            // 处理作业
            Assignments assignments = new Assignments();
            assignments.setName(examsCreateVO.getTitle());
            assignments.setDescription(examsCreateVO.getDescription());
            assignments.setLabelId(examsCreateVO.getLabelId());
            assignments.setTotalScore(examsCreateVO.getTotalScore());
            assignments.setClassId(examsCreateVO.getClassId());
            assignments.setCreatedBy(user.getUserName());

            // 保存作业基本信息
            assignmentsMapper.insert(assignments);
            String assignmentId = assignments.getAssignmentId();

            if (assignmentId != null && examsCreateVO.getQuestions() != null && !examsCreateVO.getQuestions().isEmpty()) {
                // 保存作业题目关联
                List<AssignmentQuestions> assignmentQuestions = examsCreateVO.getQuestions().stream()
                    .map(q -> AssignmentQuestions.builder()
                        .assignmentId(assignmentId)
                        .questionId(q.getQuestionId())
                        .score(q.getScore())
                        .orderNum(q.getOrderNum())
                        .createdAt(new Date())
                        .build())
                    .collect(Collectors.toList());

                // 批量插入作业题目关联
                for (AssignmentQuestions assignmentQuestion : assignmentQuestions) {
                    assignmentQuestionsMapper.insert(assignmentQuestion);
                }
            }

            return assignmentId != null ? 1 : 0;
        } else {
            // 处理考试
            Exams exams = new Exams();
            BeanUtils.copyProperties(examsCreateVO, exams);
            exams.setCreatedBy(user.getUserName());

            // 判断开始时间和结束时间，来确定status
            if (exams.getStartTime().after(now)) {
                exams.setStatus("未开始");
            } else if (exams.getEndTime().before(now)) {
                exams.setStatus("已结束");
            } else {
                exams.setStatus("进行中");
            }

            // 保存考试基本信息
            examsMapper.insert(exams);
            String examId = exams.getExamId();

            if (examId != null && examsCreateVO.getQuestions() != null && !examsCreateVO.getQuestions().isEmpty()) {
                // 保存考试题目关联
                List<ExamQuestions> examQuestions = examsCreateVO.getQuestions().stream()
                    .map(q -> ExamQuestions.builder()
                        .examId(examId)
                        .questionId(q.getQuestionId())
                        .score(q.getScore())
                        .orderNum(q.getOrderNum())
                        .createdAt(new Date())
                        .isDelete(0)
                        .build())
                    .collect(Collectors.toList());

                // 批量插入考试题目关联
                for (ExamQuestions examQuestion : examQuestions) {
                    examQuestionsMapper.insert(examQuestion);
                }
            }

            return examId != null ? 1 : 0;
        }
    }

    /**
     * 修改考试信息（包含题目）
     *
     * @param examsCreateVO 考试信息（包含题目）
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateExamsWithQuestions(ExamsCreateVO examsCreateVO) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        Date now = new Date();

        if ("作业".equals(examsCreateVO.getDistinguish())) {
            // 处理作业更新
            Assignments assignments = new Assignments();
            assignments.setAssignmentId(examsCreateVO.getExamId());
            assignments.setName(examsCreateVO.getTitle());
            assignments.setDescription(examsCreateVO.getDescription());
            assignments.setLabelId(examsCreateVO.getLabelId());
            assignments.setTotalScore(examsCreateVO.getTotalScore());
            assignments.setClassId(examsCreateVO.getClassId());
            assignments.setCreatedBy(user.getUserName());

            // 保存作业基本信息
            int rows = assignmentsMapper.updateById(assignments);

            if (rows > 0 && examsCreateVO.getQuestions() != null && !examsCreateVO.getQuestions().isEmpty()) {
                // 删除原有的作业题目关联
                LambdaQueryWrapper<AssignmentQuestions> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AssignmentQuestions::getAssignmentId, assignments.getAssignmentId());
                assignmentQuestionsMapper.delete(wrapper);

                // 保存新的作业题目关联
                List<AssignmentQuestions> assignmentQuestions = examsCreateVO.getQuestions().stream()
                    .map(q -> AssignmentQuestions.builder()
                        .assignmentId(assignments.getAssignmentId())
                        .questionId(q.getQuestionId())
                        .score(q.getScore())
                        .orderNum(q.getOrderNum())
                        .createdAt(new Date())
                        .build())
                    .collect(Collectors.toList());

                // 批量插入新的作业题目关联
                for (AssignmentQuestions assignmentQuestion : assignmentQuestions) {
                    assignmentQuestionsMapper.insert(assignmentQuestion);
                }
            }

            return rows;
        } else {
            // 处理考试更新
        Exams exams = new Exams();
            BeanUtils.copyProperties(examsCreateVO, exams);
            exams.setCreatedBy(user.getUserName());

            // 判断开始时间和结束时间，来确定status
            if (exams.getStartTime().after(now)) {
                exams.setStatus("未开始");
            } else if (exams.getEndTime().before(now)) {
                exams.setStatus("已结束");
            } else {
                exams.setStatus("进行中");
            }

            // 保存考试基本信息
            int rows = examsMapper.updateById(exams);

            if (rows > 0 && examsCreateVO.getQuestions() != null && !examsCreateVO.getQuestions().isEmpty()) {
                // 删除原有的考试题目关联
                LambdaQueryWrapper<ExamQuestions> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ExamQuestions::getExamId, exams.getExamId());
                examQuestionsMapper.delete(wrapper);

                // 保存新的考试题目关联
                List<ExamQuestions> examQuestions = examsCreateVO.getQuestions().stream()
                    .map(q -> ExamQuestions.builder()
                        .examId(exams.getExamId())
                        .questionId(q.getQuestionId())
                        .score(q.getScore())
                        .orderNum(q.getOrderNum())
                        .createdAt(new Date())
                        .isDelete(0)
                        .build())
                    .collect(Collectors.toList());

                // 批量插入新的考试题目关联
                for (ExamQuestions examQuestion : examQuestions) {
                    examQuestionsMapper.insert(examQuestion);
                }
            }

            return rows;
        }
    }

    @Override
    public List<ExamsVo> selectByExamId(String examId) {
        List<ExamsVo> examsVoList = new ArrayList<>();
        Exams exams = examsMapper.selectById(examId);
        ExamsVo examsVo = new ExamsVo();
        List<ExamQuestions> examQuestions = examQuestionsMapper.selectList(new LambdaQueryWrapper<ExamQuestions>().eq(ExamQuestions::getExamId, examId));
        List<Questions> questionsList = new ArrayList<>();
        for (ExamQuestions examQuestion : examQuestions) {
            Questions questions = questionsService.selectQuestionsByQuestionId(examQuestion.getQuestionId());
            questionsList.add(questions);
        }
        // 获取标签信息
        Labels labels = labelsMapper.selectById(exams.getLabelId());
        // 判断 lables 是否为 null
        if (labels != null) {
            examsVo.setLabelName(labels.getLabelName());
        }
        // 获取班级信息
        StudentClass studentClass = studentClassMapper.selectById(exams.getClassId());
        if (studentClass != null) {
            examsVo.setClassName(studentClass.getClassName());
            examsVo.setTotalCount(studentClass.getClassNum());
        }
        //查询答题人数
        Long participantCount = scoreMapper.selectCount(new LambdaQueryWrapper<Scores>().eq(Scores::getExamId,examId));
        // 获取题目数量
        Long count = examQuestionsMapper.selectCount(new LambdaQueryWrapper<ExamQuestions>().eq(ExamQuestions::getExamId,examId));
        examsVo.setQuestionCount(count != null ? count : 0);
        examsVo.setParticipantCount(participantCount);
        BeanUtils.copyProperties(exams,examsVo);
        examsVo.setQuestionsList(questionsList);
        examsVoList.add(examsVo);
        return examsVoList;
    }

    @Override
    public StudentExamVO selectListByDto(ExamsSelectDTO examsSelectDTO) {
        Exams exam = examsMapper.selectById(examsSelectDTO.getExamId());
        StudentExamVO vo = new StudentExamVO();
        // 查询班级信息
        StudentClass studentClass = studentClassMapper.selectById(exam.getClassId());
        // 查询所有学生
        List<KsUser> students = studentClassMapper.selectByClassIdAndUserId(studentClass.getClassId(),examsSelectDTO.getStudentNo(),examsSelectDTO.getName());
        List<StudentExamVO> studentList = new ArrayList<>();
        int index = 1;
        for (KsUser stu : students) {
            StudentExamVO svo = new StudentExamVO();
            svo.setIndex(index++);
            svo.setStudentName(stu.getNickname());
            svo.setStudentNo(stu.getPhone());
            // 查询答题记录
            Scores scores = scoreMapper.findByExamIdAndUid(examsSelectDTO.getExamId(), stu.getUId(),examsSelectDTO.getStatus());
            if (scores != null) {
                svo.setStatus(scores.getStatus()); // 已完成/进行中/未开始
                svo.setScore(scores.getTotalScore());
                svo.setSubmitTime(scores.getSubmitTime());
                svo.setDuration(TimeUtil.formatTimeToHourMinSec(scores.getTimeSpent()));
            } else {
                svo.setStatus("未开始");
                svo.setScore(null);
                svo.setSubmitTime(null);
                svo.setDuration(null);
            }
            studentList.add(svo);
        }
        return vo;
    }

}
