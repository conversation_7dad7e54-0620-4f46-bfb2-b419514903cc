package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.ResumeHrQuestion;
import com.ruoyi.system.domain.dto.ResumeHrQueDTO;
import com.ruoyi.system.mapper.HrQuestionsMapper;
import com.ruoyi.system.service.HrQuestionsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @program: mozai-back-stage
 * @description:
 * @author: coke
 * @create: 2025-05-25 15:51
 **/
@Service
public class HrQuestionsServiceImpl extends ServiceImpl<HrQuestionsMapper, ResumeHrQuestion> implements HrQuestionsService {

    @Autowired
    private HrQuestionsMapper hrQuestionMapper;

    @Override
    public List<ResumeHrQuestion> hrQuestionList(ResumeHrQueDTO resumeHrQueDTO) {
        // 构建查询条件
        QueryWrapper<ResumeHrQuestion> queryWrapper = new QueryWrapper<>();
        // 添加查询条件
        if (resumeHrQueDTO != null) {
            // 问题类型模糊查询
            if (StringUtils.hasText(resumeHrQueDTO.getQuestionType())) {
                queryWrapper.like("question_type", resumeHrQueDTO.getQuestionType());
            }
            // 题目内容模糊查询
            if (StringUtils.hasText(resumeHrQueDTO.getQuestionContent())) {
                queryWrapper.like("question", resumeHrQueDTO.getQuestionContent());
            }
            // 题目答案模糊查询
            if (StringUtils.hasText(resumeHrQueDTO.getQuestionAnswer())) {
                queryWrapper.like("answer", resumeHrQueDTO.getQuestionAnswer());
            }
            // 创建人精确查询
            if (StringUtils.hasText(resumeHrQueDTO.getCreateAt())) {
                queryWrapper.eq("create_at", resumeHrQueDTO.getCreateAt());
            }
        }
        // 只查询未删除的记录
//        queryWrapper.eq("is_delete", 0);
        // 按更新时间倒序排序
        queryWrapper.orderByDesc("update_time");
        // 执行查询
        List<ResumeHrQuestion> questions = hrQuestionMapper.selectList(queryWrapper);
        return questions;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addQuestion(ResumeHrQuestion question) {
        if (question == null) {
            return false;
        }
        
        // 设置创建时间和更新时间
        Date now = new Date();
        question.setUpdateTime(now);
        
        // 插入数据
        return hrQuestionMapper.insert(question) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestion(ResumeHrQuestion question) {
        if (question == null || question.getQueId() == null) {
            return false;
        }
        
        // 设置更新时间
        question.setUpdateTime(new Date());
        
        // 更新数据
        return hrQuestionMapper.updateById(question) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteQuestion(Long queId) {
        if (queId == null) {
            return false;
        }
        
        // 物理删除
        return hrQuestionMapper.deleteById(queId) > 0;
    }

    @Override
    public ResumeHrQuestion getQuestion(Long queId) {
        if (queId == null) {
            return null;
        }
        
        // 根据ID查询问题
        return hrQuestionMapper.selectById(queId);
    }
}
