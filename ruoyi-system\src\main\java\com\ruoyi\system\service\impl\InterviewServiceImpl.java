package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.ResumeConstants;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.dto.FeedbackDto;
import com.ruoyi.system.domain.dto.InterviewSpeechChapterDto;
import com.ruoyi.system.domain.vo.InterviewQueryVO;
import com.ruoyi.system.domain.vo.InterviewTranscriptionVO;
import com.ruoyi.system.domain.vo.InterviewVO;
import com.ruoyi.system.mapper.InterviewMapper;
import com.ruoyi.system.mapper.InterviewQuestionsMapper;
import com.ruoyi.system.mapper.InterviewTranscriptionMapper;
import com.ruoyi.system.mapper.InterviewSpeechChapterMapper;
import com.ruoyi.system.mapper.InterviewSpeechRecordMapper;
import com.ruoyi.system.service.InterviewService;
import com.ruoyi.system.service.ISysTodoService;
import com.ruoyi.system.service.ResumeCategoryRelationService;
import com.ruoyi.system.service.ResumeProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 面试服务实现类
 */
@Service
@Slf4j
public class InterviewServiceImpl extends ServiceImpl<InterviewMapper, Interview> implements InterviewService {

    @Autowired
    private InterviewQuestionsMapper interviewQuestionsMapper;
    
    @Autowired
    private InterviewTranscriptionMapper interviewTranscriptionMapper;
    
    @Autowired
    private InterviewSpeechChapterMapper interviewSpeechChapterMapper;

    @Autowired
    private InterviewSpeechRecordMapper interviewSpeechRecordMapper;

    @Autowired
    private InterviewTranscriptionMapper transcriptionMapper;

    @Autowired
    private ISysTodoService sysTodoService;

    @Autowired
    private ResumeProgressService resumeProgressService;

    @Value("${interview.url}")
    private String interviewUrl;
    @Autowired
    ResumeCategoryRelationService resumeCategoryRelationService;
    
    /**
     * 状态文本映射
     */
    private static final Map<Integer, String> STATUS_TEXT_MAP = Collections.unmodifiableMap(new HashMap<Integer, String>() {{
        put(1, "等待结果");
        put(2, "已完成");
    }});

    @Override
    public Page<InterviewVO> pageInterviews(InterviewQueryVO queryVO) {
        // 创建分页对象
        Page<Interview> page = new Page<>(queryVO.getPageNum(), queryVO.getPageSize());
        
        // 构建查询条件
        LambdaQueryWrapper<Interview> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(queryVO.getCandidateName())) {
            queryWrapper.like(Interview::getCandidateName, queryVO.getCandidateName());
        }
        
        if (StringUtils.hasText(queryVO.getType())) {
            queryWrapper.eq(Interview::getType, queryVO.getType());
        }
        
        if (StringUtils.hasText(queryVO.getPosition())) {
            queryWrapper.like(Interview::getPosition, queryVO.getPosition());
        }
        
        if (StringUtils.hasText(queryVO.getStage())) {
            queryWrapper.eq(Interview::getStage, queryVO.getStage());
        }
        
        if (queryVO.getStatus() != null) {
            queryWrapper.eq(Interview::getStatus, queryVO.getStatus());
        }
        
        // 处理时间范围查询
        if (StringUtils.hasText(queryVO.getStartTime()) && StringUtils.hasText(queryVO.getEndTime())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startTime = LocalDateTime.parse(queryVO.getStartTime(), formatter);
            LocalDateTime endTime = LocalDateTime.parse(queryVO.getEndTime(), formatter);
            queryWrapper.between(Interview::getInterviewTime, startTime, endTime);
        } else if (StringUtils.hasText(queryVO.getStartTime())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startTime = LocalDateTime.parse(queryVO.getStartTime(), formatter);
            queryWrapper.ge(Interview::getInterviewTime, startTime);
        } else if (StringUtils.hasText(queryVO.getEndTime())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime endTime = LocalDateTime.parse(queryVO.getEndTime(), formatter);
            queryWrapper.le(Interview::getInterviewTime, endTime);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(Interview::getCreateTime);
        
        // 执行分页查询
        Page<Interview> interviewPage = this.page(page, queryWrapper);
        
        // 创建VO分页对象
        Page<InterviewVO> voPage = new Page<>();
        voPage.setTotal(interviewPage.getTotal());
        voPage.setPages(interviewPage.getPages());
        voPage.setCurrent(interviewPage.getCurrent());
        voPage.setSize(interviewPage.getSize());
        // 转换记录为VO
        List<InterviewVO> voRecords = interviewPage.getRecords().stream().map(interview -> {
            InterviewVO interviewVO = new InterviewVO();
            BeanUtils.copyProperties(interview, interviewVO);
            // 设置状态文本
            interviewVO.setStatusText(STATUS_TEXT_MAP.getOrDefault(interview.getStatus(), "未知"));
            return interviewVO;
        }).collect(Collectors.toList());
        // 设置VO记录
        voPage.setRecords(voRecords);
        return voPage;
    }

    @Override
    public InterviewVO getInterviewDetail(String id) {
        // 查询面试基本信息
        Interview interview = this.getById(id);
        if (interview == null) {
            return null;
        }
        InterviewVO interviewVO = new InterviewVO();
        BeanUtils.copyProperties(interview, interviewVO);

        // 根据type判断
        if ("formal".equals(interview.getType())) {
            // 查询章节
            List<InterviewSpeechChapter> chapters = interviewSpeechChapterMapper.selectList(
                new LambdaQueryWrapper<InterviewSpeechChapter>().eq(InterviewSpeechChapter::getInterviewId, id)
            );
            interviewVO.setChapters(chapters);
            // 查询转写记录
            List<InterviewSpeechRecord> records = interviewSpeechRecordMapper.selectList(
                new LambdaQueryWrapper<InterviewSpeechRecord>().eq(InterviewSpeechRecord::getInterviewId, id)
            );
            interviewVO.setRecords(records);
        } else {
            // 模拟面试，章节和转写记录留空
            // 获取转写记录
            log.info("获取面试的所有转写结果: interviewId={}", id);
            List<InterviewTranscription> transcriptions = transcriptionMapper.selectByInterviewId(id);
            // 转换为VO列表
            List<InterviewTranscriptionVO> transcriptionVOList = transcriptions.stream().map(t -> {
                InterviewTranscriptionVO vo = new InterviewTranscriptionVO();
                vo.setId(t.getId());
                vo.setInterviewId(t.getInterviewId());
                vo.setQuestionIndex(t.getQuestionIndex());
                vo.setQuestion(t.getQuestion());
                vo.setTranscription(t.getTranscription());
                vo.setAudioUrl(t.getAudioUrl());
                vo.setCreateTime(t.getCreateTime());
                return vo;
            }).collect(Collectors.toList());
            interviewVO.setTranscriptions(transcriptionVOList);
        }
        return interviewVO;
    }

    @Override
    @Transactional
    public boolean saveInterview(InterviewVO interviewVO) {
        // 生成面试ID
        String interviewId = UUID.randomUUID().toString().replace("-", "");
        
        // 创建面试实体
        Interview interview = new Interview();
        BeanUtils.copyProperties(interviewVO, interview);
        interview.setId(interviewId);
        interview.setStatus(0); // 默认状态为进行中
        interview.setCreateTime(LocalDateTime.now());
        interview.setUpdateTime(LocalDateTime.now());
        
        // 保存面试基本信息
        boolean success = this.save(interview);
        
        // 保存面试问题
        /*if (success && interviewVO.getQuestions() != null && !interviewVO.getQuestions().isEmpty()) {
            InterviewQuestions questions = new InterviewQuestions();
            questions.setInterviewId(interviewId);
            questions.setQuestions(JSON.toJSONString(interviewVO.getQuestions()));
            questions.setCreateTime(LocalDateTime.now());
            questions.setUpdateTime(LocalDateTime.now());
            
            interviewQuestionsMapper.insert(questions);
        }*/
        
        return success;
    }

    @Override
    @Transactional
    public boolean updateInterview(InterviewVO interviewVO) {
        // 检查面试是否存在
        if (interviewVO.getId() == null) {
            return false;
        }
        
        // 创建面试实体
        Interview interview = new Interview();
        BeanUtils.copyProperties(interviewVO, interview);
        interview.setUpdateTime(LocalDateTime.now());
        
        // 更新面试基本信息
        boolean success = this.updateById(interview);
        
        // 更新面试问题
        /*if (success && interviewVO.getQuestions() != null) {
            // 先查询是否存在问题记录
            InterviewQuestions existingQuestions = interviewQuestionsMapper.selectById(interviewVO.getId());
            
            if (existingQuestions != null) {
                // 更新问题
                existingQuestions.setQuestions(JSON.toJSONString(interviewVO.getQuestions()));
                existingQuestions.setUpdateTime(LocalDateTime.now());
                interviewQuestionsMapper.updateById(existingQuestions);
            } else {
                // 新增问题
                InterviewQuestions questions = new InterviewQuestions();
                questions.setInterviewId(interviewVO.getId());
                questions.setQuestions(JSON.toJSONString(interviewVO.getQuestions()));
                questions.setCreateTime(LocalDateTime.now());
                questions.setUpdateTime(LocalDateTime.now());
                
                interviewQuestionsMapper.insert(questions);
            }
        }*/
        
        return success;
    }

    @Override
    @Transactional
    public boolean deleteInterview(String id) {
        // 删除面试基本信息
        boolean success = this.removeById(id);
        
        // 删除面试问题
        if (success) {
            interviewQuestionsMapper.deleteById(id);
            
            // 删除面试转写记录
            LambdaQueryWrapper<InterviewTranscription> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InterviewTranscription::getInterviewId, id);
            interviewTranscriptionMapper.delete(wrapper);
        }
        
        return success;
    }

    @Override
    public boolean updateInterviewStatus(String id, Integer status) {
        Interview interview = new Interview();
        interview.setId(id);
        interview.setStatus(status);
        interview.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(interview);
    }

    @Override
    @Transactional
    public boolean updateInterviewFeedback(FeedbackDto feedbackDto) {
        // 更新面试评分反馈
        Interview interview = new Interview();
        interview.setId(feedbackDto.getId());
        interview.setOverallScore(feedbackDto.getOverallScore());
        interview.setFeedback(feedbackDto.getFeedback());
        interview.setStrengths(feedbackDto.getStrengths());
        interview.setImprovements(feedbackDto.getImprovements());
        interview.setResult(feedbackDto.getResult());
        interview.setStatus(2); // 更新状态为已完成
        interview.setUpdateTime(LocalDateTime.now());
        boolean updateResult = this.updateById(interview);

        // 如果更新成功且有面试结果，则生成待办任务和更新用户进度
        if (updateResult && feedbackDto.getResult() != null) {
            // 获取完整的面试信息以获取userId和面试类型
            Interview interviewInfo = this.getById(feedbackDto.getId());
            //正式面试不更新进度与待办
            if(interviewInfo.getType().equals("formal")) {
                return updateResult;
            }
            if (interviewInfo != null && interviewInfo.getUserId() != null) {
                Long userId = interviewInfo.getUserId().longValue();
                String stage = interviewInfo.getStage();
                boolean isHrInterview = "hr".equalsIgnoreCase(stage);
                
                // 获取resumeId
                // 根据userId和面试岗位获取resumeId
                LambdaQueryWrapper<ResumeCategoryRelation> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(ResumeCategoryRelation::getUserId, userId)
                        .eq(ResumeCategoryRelation::getCatId, Long.valueOf(interviewInfo.getPosition()));

                ResumeCategoryRelation relation = resumeCategoryRelationService.getOne(wrapper);
                if (relation == null) {
                    log.error("未找到对应的简历信息，userId={}, position={}", userId, interview.getPosition());
                    return false;
                }
                Long resumeId = relation.getResumeId();
                // 根据面试结果处理
                if (feedbackDto.getResult() == 1) {
                    // 面试通过
                    handleInterviewPass(userId, isHrInterview, resumeId);
                } else {
                    // 面试未通过
                    handleInterviewFail(userId, isHrInterview, resumeId);
                }
            }
        }
        
        return updateResult;
    }

    /**
     * 处理面试通过的情况
     * @param userId 用户ID
     * @param isHrInterview 是否为HR面试
     * @param resumeId 简历ID
     */
    private void handleInterviewPass(Long userId, boolean isHrInterview, Long resumeId) {
        SysTodo todo = new SysTodo();
        todo.setUserId(userId);
        todo.setStatus(ResumeConstants.TODO_STATUS_INCOMPLETE); // 0：未完成
        todo.setResumeId(resumeId);
        if (isHrInterview) {
            // HR面试通过，生成技术面试待办
            todo.setTitle("技术面试");
            todo.setContent("恭喜您通过了HR面试环节，请您抓紧时间完成技术面试环节的面试！");
            todo.setUrl(interviewUrl);
            todo.setTodoType(ResumeConstants.TODO_TYPE_TECHNICAL_INTERVIEW); // 3：技术面试

            // 更新用户进度为技术面试阶段
            updateUserProgress(userId.intValue(), ResumeConstants.STAGE_TECHNICAL_INTERVIEW, resumeId);
        } else {
            // 技术面试通过，生成正式面试待办
            todo.setTitle("正式面试");
            todo.setContent("恭喜您完成了全部环节，请开始投递简历并参与面试，必要时将正式面试录制的音视频上传至系统！");
            todo.setUrl("");
            todo.setTodoType(ResumeConstants.TODO_TYPE_FORMAL_INTERVIEW); // 4：正式面试
            
            // 更新用户进度为正式面试阶段
            updateUserProgress(userId.intValue(), ResumeConstants.STAGE_FORMAL_INTERVIEW, resumeId);
        }
        
        sysTodoService.insertSysTodo(todo);
    }

    /**
     * 处理面试未通过的情况
     * @param userId 用户ID
     * @param isHrInterview 是否为HR面试
     * @param resumeId 简历ID
     */
    private void handleInterviewFail(Long userId, boolean isHrInterview, Long resumeId) {
        SysTodo todo = new SysTodo();
        todo.setUserId(userId);
        todo.setStatus(ResumeConstants.TODO_STATUS_INCOMPLETE); // 0：未完成
        todo.setUrl(interviewUrl);
        todo.setResumeId(resumeId);
        if (isHrInterview) {
            // HR面试未通过，再次生成HR面试待办
            todo.setTitle("HR面试");
            todo.setContent("很遗憾，您未通过本轮HR面试，请继续参与HR面试！");
            todo.setTodoType(ResumeConstants.TODO_TYPE_HR_INTERVIEW); // 2：HR面试
        } else {
            // 技术面试未通过，再次生成技术面试待办
            todo.setTitle("技术面试");
            todo.setContent("很遗憾，您未通过本轮技术面试，请继续参与技术面试！");
            todo.setTodoType(ResumeConstants.TODO_TYPE_TECHNICAL_INTERVIEW); // 3：技术面试
        }
        
        sysTodoService.insertSysTodo(todo);
    }

    /**
     * 更新用户进度阶段
     * @param userId 用户ID
     * @param stage 阶段编码
     * @param resumeId 简历ID
     */
    private void updateUserProgress(Integer userId, String stage, Long resumeId) {
        // 添加一个用于根据userId和resumeId查询的Map
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("resumeId", resumeId);
        List<ResumeProgress> progressList = resumeProgressService.getResumeProgressByCondition(params);
        ResumeProgress progress = progressList != null && !progressList.isEmpty() ? progressList.get(0) : null;
        
        // 如果没有找到带resumeId的记录，则尝试只通过userId查询
        if (progress == null && resumeId != null) {
            progress = resumeProgressService.getResumeProgressByUserId(userId);
        }
        
        if (progress != null) {
            // 更新现有进度
            if (resumeId != null) {
                progress.setResumeId(resumeId); // 设置简历ID
            }
            resumeProgressService.updateResumeProgressStage(progress.getId(), stage, resumeId);
        } else {
            // 创建新的进度记录
            ResumeProgress newProgress = new ResumeProgress();
            newProgress.setUserId(userId);
            newProgress.setResumeId(resumeId); // 设置简历ID
            newProgress.setCurrentStage(stage);
            resumeProgressService.createResumeProgress(newProgress);
        }
    }

    @Override
    public List<InterviewSpeechRecord> getSpeechRecord(String id) {
        List<InterviewSpeechRecord> interviewSpeechRecords = new ArrayList<>();
        if (id != null){
            InterviewSpeechChapter interviewSpeechChapter = interviewSpeechChapterMapper.selectById(id);
            if (interviewSpeechChapter != null) {
                // 假设 interviewSpeechChapter 是包含开始和结束时间的对象
                interviewSpeechRecords = interviewSpeechRecordMapper.selectList(
                        new LambdaQueryWrapper<InterviewSpeechRecord>()
                                .ge(InterviewSpeechRecord::getStartTime, interviewSpeechChapter.getStartTime())  // 大于等于开始时间
                                .le(InterviewSpeechRecord::getEndTime, interviewSpeechChapter.getEndTime())        // 小于等于结束时间
                );
            }

        }
        return interviewSpeechRecords;
    }

    @Override
    public List<InterviewTranscription> getTranscriptionList(String interviewId) {
        log.info("获取面试的所有转写记录: interviewId={}", interviewId);
        return transcriptionMapper.selectByInterviewId(interviewId);
    }

    /*@Override
    public List<InterviewSpeechRecord> getSpeechRecord(String id) {
        List<InterviewSpeechRecord> interviewSpeechRecords = new ArrayList<>();
        if (id != null){
            InterviewSpeechChapter interviewSpeechChapter = interviewSpeechChapterMapper.selectById(id);
            if (interviewSpeechChapter != null) {
                // 假设 interviewSpeechChapter 是包含开始和结束时间的对象
                interviewSpeechRecords = interviewSpeechRecordMapper.selectList(
                        new LambdaQueryWrapper<InterviewSpeechRecord>()
                                .ge(InterviewSpeechRecord::getStartTime, interviewSpeechChapter.getStartTime())  // 大于等于开始时间
                                .le(InterviewSpeechRecord::getEndTime, interviewSpeechChapter.getEndTime())        // 小于等于结束时间
                );
            }

        }
        return interviewSpeechRecords;
    }*/
} 