package com.ruoyi.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.Project;
import com.ruoyi.system.domain.ProjectContent;
import com.ruoyi.system.domain.ProjectContentSegment;
import com.ruoyi.system.domain.ProjectQuestion;
import com.ruoyi.system.domain.ResumeCategory;
import com.ruoyi.system.domain.SkillSegment;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.ProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目服务实现类
 */
@Service
@Slf4j
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements ProjectService {

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private ProjectContentMapper projectContentMapper;
    
    @Autowired
    private ProjectQuestionMapper projectQuestionMapper;

    @Autowired
    private ResumeCategoryMapper resumeCategoryMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public List<ProjectVO> listAllProjects(String keyword) {
        // 查询所有项目
        List<ProjectVO> projects = projectMapper.selectProject(keyword);
        
        // 将实体转换为VO
        return projects;
    }

    @Override
    public ProjectVO getProjectById(Long id) {
        // 查询项目
        Project project = this.getById(id);
        if (project == null) {
            return null;
        }

        // 转换为VO
        ProjectVO projectVO = new ProjectVO();
        BeanUtils.copyProperties(project, projectVO);

        // 设置职位类别名称
        if (project.getCatId() != null) {
            String resumeCategory = resumeCategoryMapper.selectByCatId(project.getCatId());
            projectVO.setCatId(project.getCatId());
            projectVO.setCatName(resumeCategory);
        }

        // 查询并设置项目内容及其关联的问题
        List<ProjectContentVO> contents = projectContentMapper.selectByProjectId(id);
        for (ProjectContentVO content : contents) {
            // 查询并设置该内容关联的问题
            List<ProjectQuestionVO> questions = projectQuestionMapper.selectByContentId(content.getConId());
            content.setQuestions(questions);
        }
        projectVO.setContents(contents);

        return projectVO;
    }

    @Override
    @Transactional
    public boolean saveProjectWithDetails(ProjectVO projectVO) {
        // 保存项目
        Project project = new Project();
        BeanUtils.copyProperties(projectVO, project);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        project.setCreateAt(user.getUserName());
        boolean projectSaved = this.save(project);

        if (projectSaved) {
            Long proId = project.getProId();

            // 保存项目内容及其关联的问题
            if (projectVO.getContents() != null && !projectVO.getContents().isEmpty()) {
                for (int contentIdx = 0; contentIdx < projectVO.getContents().size(); contentIdx++) {
                    ProjectContentVO contentVO = projectVO.getContents().get(contentIdx);

                    // 保存项目内容
                    ProjectContent content = new ProjectContent();
                    BeanUtils.copyProperties(contentVO, content);
                    content.setProjectId(proId);
                    content.setContentOrder(contentIdx + 1);
                    projectContentMapper.insert(content);

                    // 保存该内容关联的问题
                    if (contentVO.getQuestions() != null && !contentVO.getQuestions().isEmpty()) {
                        Long contentId = content.getConId(); // 获取刚插入的内容ID
                        for (int questionIdx = 0; questionIdx < contentVO.getQuestions().size(); questionIdx++) {
                            ProjectQuestionVO questionVO = contentVO.getQuestions().get(questionIdx);
                            ProjectQuestion question = new ProjectQuestion();
                            BeanUtils.copyProperties(questionVO, question);
                            question.setProjectId(proId);
                            question.setConId(contentId); // 关联到对应的内容
                            question.setQuestionOrder(questionIdx + 1);
                            projectQuestionMapper.insert(question);
                        }
                    }
                }
            }
        }

        return projectSaved;
    }

    @Override
    @Transactional
    public boolean updateProjectWithDetails(ProjectVO projectVO) {
        // 更新项目
        Project project = new Project();
        BeanUtils.copyProperties(projectVO, project);
        boolean projectUpdated = this.updateById(project);

        if (projectUpdated) {
            Long proId = project.getProId();

            // 删除旧的项目内容及其关联的问题
            LambdaQueryWrapper<ProjectContent> contentWrapper = new LambdaQueryWrapper<>();
            contentWrapper.eq(ProjectContent::getProjectId, proId);
            List<ProjectContent> oldContents = projectContentMapper.selectList(contentWrapper);

            // 删除旧的项目问题
            if (!oldContents.isEmpty()) {
                List<Long> contentIds = oldContents.stream()
                        .map(ProjectContent::getConId)
                        .collect(Collectors.toList());

                LambdaQueryWrapper<ProjectQuestion> questionWrapper = new LambdaQueryWrapper<>();
                questionWrapper.in(ProjectQuestion::getConId, contentIds);
                projectQuestionMapper.delete(questionWrapper);
            }

            // 删除旧的项目内容
            projectContentMapper.delete(contentWrapper);

            // 保存新的项目内容及其关联的问题
            if (projectVO.getContents() != null && !projectVO.getContents().isEmpty()) {
                for (int contentIdx = 0; contentIdx < projectVO.getContents().size(); contentIdx++) {
                    ProjectContentVO contentVO = projectVO.getContents().get(contentIdx);

                    // 保存项目内容
                    ProjectContent content = new ProjectContent();
                    BeanUtils.copyProperties(contentVO, content);
                    content.setProjectId(proId);
                    content.setContentOrder(contentIdx + 1);
                    projectContentMapper.insert(content);

                    // 保存该内容关联的问题
                    if (contentVO.getQuestions() != null && !contentVO.getQuestions().isEmpty()) {
                        Long contentId = content.getConId(); // 获取刚插入的内容ID
                        for (int questionIdx = 0; questionIdx < contentVO.getQuestions().size(); questionIdx++) {
                            ProjectQuestionVO questionVO = contentVO.getQuestions().get(questionIdx);
                            ProjectQuestion question = new ProjectQuestion();
                            BeanUtils.copyProperties(questionVO, question);
                            question.setProjectId(proId);
                            question.setConId(contentId); // 关联到对应的内容
                            question.setQuestionOrder(questionIdx + 1);
                            projectQuestionMapper.insert(question);
                        }
                    }
                }
            }
        }

        return projectUpdated;
    }

    @Override
    @Transactional
    public boolean deleteProjectWithDetails(Long id) {
        // 删除项目问题
        deleteProjectQuestions(id);
        // 删除项目内容
        deleteProjectContents(id);
        // 删除项目
        return this.removeById(id);
    }

    /**
     * 删除项目内容
     */
    private void deleteProjectContents(Long projectId) {
        // 删除内容
        projectContentMapper.deleteByProjectId(projectId);
    }

    /**
     * 删除项目问题
     */
    private void deleteProjectQuestions(Long projectId) {
        List<ProjectContentVO> projectContentVOS = projectContentMapper.selectByProjectId(projectId);
        for (ProjectContentVO projectContentVO : projectContentVOS) {
            LambdaQueryWrapper<ProjectQuestion> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ProjectQuestion::getConId, projectContentVO.getConId());
            projectQuestionMapper.delete(wrapper);
        }
    }

    @Override
    public Page<ProjectVO> pageProjects(Integer pageNum, Integer pageSize, String name, String role) {
        // 创建分页对象
        Page<Project> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StringUtils.hasText(name)) {
            queryWrapper.like(Project::getName, name);
        }

        if (StringUtils.hasText(role)) {
            queryWrapper.eq(Project::getCatId, role);
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(Project::getCreateTime);

        // 执行分页查询
        Page<Project> projectPage = this.page(page, queryWrapper);

        // 创建VO分页对象
        Page<ProjectVO> voPage = new Page<>();
        BeanUtils.copyProperties(projectPage, voPage, "records");

        // 转换记录为VO
        List<ProjectVO> voRecords = new ArrayList<>();
        for (Project project : projectPage.getRecords()) {
            ProjectVO projectVO = new ProjectVO();
            BeanUtils.copyProperties(project, projectVO);

            // 设置职位类别名称
            if (project.getCatId() != null) {
                String resumeCategory = resumeCategoryMapper.selectByCatId(project.getCatId());
                projectVO.setCatId(project.getCatId());
                projectVO.setCatName(resumeCategory);
            }

            // 查询并设置项目内容及其关联的问题
            List<ProjectContentVO> contents = projectContentMapper.selectByProjectId(project.getProId());
            for (ProjectContentVO content : contents) {
                // 查询并设置该内容关联的问题
                List<ProjectQuestionVO> questions = projectQuestionMapper.selectByContentId(content.getConId());
                content.setQuestions(questions);
            }
            projectVO.setContents(contents);

            voRecords.add(projectVO);
        }

        // 设置VO记录
        voPage.setRecords(voRecords);

        return voPage;
    }

    @Override
    public List<AIQuestionsVo> selectByContent(String content) throws Exception {
        String baseUrl = "http://127.0.0.1:7091/ai/chatAPI/contentQuestion";
        // 构建请求体，包含面试类型参数
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("question", content);

        try {
            // 发送POST请求
            ResponseEntity<String> response = restTemplate.postForEntity(baseUrl, requestBody, String.class);

            // 检查响应状态
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("HTTP请求失败，状态码: {}", response.getStatusCode());
                throw new Exception("获取面试问题失败，HTTP状态码: " + response.getStatusCode());
            }

            // 解析响应的JSON字符串
            String jsonResponse = response.getBody();
            if (jsonResponse == null || jsonResponse.isEmpty()) {
                log.error("HTTP响应内容为空");
                throw new Exception("获取面试问题失败，响应为空");
            }

            // 清理Markdown代码块标记（如果有）
            jsonResponse = jsonResponse.replaceAll("^```json\\s*|\\s*```$", "");

            try {
                // 直接解析JSON数组
                List<AIQuestionsVo> questions = JSON.parseArray(jsonResponse, AIQuestionsVo.class);
                if (questions.isEmpty()) {
                    log.warn("生成的问题列表为空");
                } else {
                    log.info("成功生成{}个面试问题", questions.size());
                }
                return questions;

            } catch (Exception e) {
                log.error("解析JSON数组失败: {}", e.getMessage());
                throw new Exception("解析面试问题失败", e);
            }
        } catch (Exception e) {
            log.error("获取面试问题失败", e);
            throw new Exception("获取面试问题失败: " + e.getMessage(), e);
        }
    }
} 