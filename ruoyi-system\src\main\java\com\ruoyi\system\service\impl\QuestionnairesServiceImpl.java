package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.TimeUtil;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.dto.QuestionnaireCreateDTO;
import com.ruoyi.system.domain.vo.QuestionnaireCreateVO;
import com.ruoyi.system.domain.vo.QuestionnaireDetailVO;
import com.ruoyi.system.domain.vo.QuestionnairesVo;
import com.ruoyi.system.domain.vo.StudentAnswerRowVO;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.IQuestionnairesService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问卷管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
public class QuestionnairesServiceImpl implements IQuestionnairesService {

    @Autowired
    private QuestionnairesMapper questionnairesMapper;

    @Autowired
    private StudentClassMapper studentClassMapper;

    @Autowired
    private QuestionnaireAnswerMapper questionnaireAnswerMapper;

    @Autowired
    private QuestionnaireScoresMapper questionnaireScoresMapper;

    @Autowired
    private WjQuestionsMapper wjQuestionsMapper;

    @Autowired
    private LabelsMapper labelsMapper;


    @Override
    public QuestionnaireDetailVO selectQuestionnaireDetail(String questionnaireId) {
        // 1. 查询问卷主信息
        Questionnaires questionnaire = questionnairesMapper.selectById(questionnaireId);
        // 2. 查询班级信息
        StudentClass studentClass = studentClassMapper.selectById(questionnaire.getClassId());
        // 3. 查询标签信息
        Labels label = labelsMapper.selectById(questionnaire.getLableId());
        // 4. 查询题目列表
        List<WjQuestions> wjQuestions = wjQuestionsMapper.selectList(new LambdaQueryWrapper<WjQuestions>().eq(WjQuestions::getQuestionnaireId, questionnaireId));
        List<QuestionnaireDetailVO.QuestionVO> questionVOList = new ArrayList<>();
        int order = 1;
        for (WjQuestions q : wjQuestions) {
            QuestionnaireDetailVO.QuestionVO vo = new QuestionnaireDetailVO.QuestionVO();
            vo.setQuestionId(String.valueOf(q.getQuestionsId()));
            vo.setContent(q.getContent());
            vo.setOrderNum(order++);
            questionVOList.add(vo);
        }
        // 5. 查询所有学生
        List<KsUser> studentList = studentClassMapper.selectByClassId(questionnaire.getClassId());
        // 6. 查询所有答卷
        List<QuestionnaireAnswer> answerList = questionnaireAnswerMapper.selectByQuestionnaireId(questionnaireId);
        // 7. 查询所有分数记录
        List<QuestionnaireScore> scoreList = questionnaireScoresMapper.selectList(new LambdaQueryWrapper<QuestionnaireScore>().eq(QuestionnaireScore::getQuestionnaireId, questionnaireId));
        // 8. 构建答卷Map：Map<userId, Map<questionId, QuestionnaireAnswer>>
        Map<String, Map<String, QuestionnaireAnswer>> studentAnswerMap = new HashMap<>();
        for (QuestionnaireAnswer ans : answerList) {
            String userId = String.valueOf(ans.getUserId());
            String questionIdStr = String.valueOf(ans.getQuestionsId());
            studentAnswerMap.computeIfAbsent(userId, k -> new HashMap<>()).put(questionIdStr, ans);
        }
        // 9. 构建分数Map：Map<studentId, QuestionnaireScore>
        Map<String, QuestionnaireScore> scoreMap = new HashMap<>();
        for (QuestionnaireScore score : scoreList) {
            String studentId = String.valueOf(score.getStudentId());
            scoreMap.put(studentId, score);
        }
        // 10. 组装答题明细和分数明细
        List<QuestionnaireDetailVO.AnswerRowVO> answerRows = new ArrayList<>();
        List<QuestionnaireDetailVO.ScoresVo> scoresVos = new ArrayList<>();
        int idx = 1;
        List<String> timeSpentList = new ArrayList<>();
        for (KsUser user : studentList) {
            String userKey = String.valueOf(user.getUId());
            // 答案
            QuestionnaireDetailVO.AnswerRowVO row = new QuestionnaireDetailVO.AnswerRowVO();
            row.setIndex(idx);
            row.setStudentName(user.getNickname());
            row.setPhone(user.getPhone());
            List<String> answers = new ArrayList<>();
            Map<String, QuestionnaireAnswer> ansMap = studentAnswerMap.get(userKey);
            for (QuestionnaireDetailVO.QuestionVO qvo : questionVOList) {
                QuestionnaireAnswer ans = (ansMap != null) ? ansMap.get(qvo.getQuestionId()) : null;
                answers.add(ans != null ? ans.getAnswer() : "");
            }
            row.setAnswers(answers);
            answerRows.add(row);

            // 分数
            QuestionnaireDetailVO.ScoresVo scoresVo = new QuestionnaireDetailVO.ScoresVo();
            QuestionnaireScore score = scoreMap.get(userKey);
            if (score != null) {
                scoresVo.setScoreId(score.getScoreId());
                scoresVo.setStartTime(score.getStartTime());
                scoresVo.setSubmitTime(score.getSubmitTime());
                //累加所有学生的用时时长
                timeSpentList.add(score.getTimeSpent());
                scoresVo.setDuration(TimeUtil.formatTimeToHourMinSec(score.getTimeSpent()));
            }
            scoresVos.add(scoresVo);
            idx++;
        }
        // 11. 统计
        Long totalCount = (long) studentList.size();
        Long submittedCount = scoreList.stream().filter(s -> s.getSubmitTime() != null).count();
        double completionRate = totalCount == 0 ? 0 : (double) submittedCount * 100 / totalCount;
        // 四舍五入并保留两位小数
        DecimalFormat df = new DecimalFormat("#.00");
        double roundedRate = Double.parseDouble(df.format(completionRate));

        //平均用时
        String averageTime = calcAverageTimeFromList(timeSpentList);
        // 12. 组装VO
        QuestionnaireDetailVO vo = new QuestionnaireDetailVO();
        vo.setQuestionnaireId(questionnaireId);
        vo.setTitle(questionnaire.getTitle());
        vo.setClassName(studentClass != null ? studentClass.getClassName() : "");
        vo.setLableId(questionnaire.getLableId());
        vo.setLableName(label != null ? label.getLabelName() : "");
        vo.setStatus(questionnaire.getStatus());
        vo.setTotalCount(totalCount);
        vo.setSubmittedCount(submittedCount);
        vo.setCompletionRate(roundedRate);
        vo.setAverageTime(averageTime);
        vo.setQuestions(questionVOList);
        vo.setAnswerRows(answerRows);
        vo.setScoresVos(scoresVos);
        return vo;
    }



    public static String calcAverageTimeFromList(List<String> timeSpentList) {
        int totalSeconds = 0;
        int validCount = 0;
        for (String timeStr : timeSpentList) {
            if (timeStr != null && timeStr.matches("\\d{2}:\\d{2}:\\d{2}")) {
                String[] parts = timeStr.split(":");
                int hours = Integer.parseInt(parts[0]);
                int minutes = Integer.parseInt(parts[1]);
                int seconds = Integer.parseInt(parts[2]);
                totalSeconds += hours * 3600 + minutes * 60 + seconds;
                validCount++;
            }
        }
        if (validCount == 0) return "0秒";
        int avgSeconds = totalSeconds / validCount;
        int hours = avgSeconds / 3600;
        int minutes = (avgSeconds % 3600) / 60;
        int seconds = avgSeconds % 60;
        StringBuilder sb = new StringBuilder();
        if (hours > 0) sb.append(hours).append("时");
        if (minutes > 0 || hours > 0) sb.append(minutes).append("分");
        sb.append(seconds).append("秒");
        return sb.toString();
    }

    /**
     * 查询问卷管理列表
     *
   */
    @Override
    public List<QuestionnairesVo> selectQuestionnairesList(Questionnaires questionnaires) {
        List<QuestionnairesVo> list = new ArrayList<>();

        List<Questionnaires> questionnairesList = questionnairesMapper.selectQuestionnairesList(questionnaires);
        for (Questionnaires questionnaires1 : questionnairesList) {
            QuestionnairesVo questionnairesVo = new QuestionnairesVo();
            BeanUtils.copyProperties(questionnaires1,questionnairesVo);
            Labels labels = labelsMapper.selectById(questionnaires1.getLableId());
            questionnairesVo.setLableName(labels.getLabelName());
            list.add(questionnairesVo);
        }
        return list;
    }
    /**
     * 新增问卷管理
     *
     * @return 结果
     */
    @Transactional
    @Override
    public int insertQuestionnaires(QuestionnaireCreateDTO vo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 1. 新建问卷对象并赋值
        Questionnaires questionnaire = new Questionnaires();
        questionnaire.setTitle(vo.getTitle());
        questionnaire.setLableId(vo.getLableId());
        questionnaire.setDescription(vo.getDescription());
        questionnaire.setClassId(vo.getClassId());
        questionnaire.setStartTime(vo.getStartTime());
        questionnaire.setEndTime(vo.getEndTime());
        questionnaire.setDuration(Long.valueOf(vo.getDuration()));
        questionnaire.setCreatedBy(user.getUserName());
        // 判断开始时间和结束时间，来确定status
        if (vo.getStartTime().after(new Date())) {
            questionnaire.setStatus("未开始");
        } else if (vo.getEndTime().before(new Date())) {
            questionnaire.setStatus("已结束");
        } else {
            questionnaire.setStatus("进行中");
        }

        // 2. 插入问卷，获取主键
        questionnairesMapper.insert(questionnaire);
        String questionnaireId = questionnaire.getQuestionnaireId(); // MyBatis-Plus会自动回填

        // 3. 批量插入题目
        for (WjQuestions q : vo.getQuestions()) {
            WjQuestions wj = new WjQuestions();
            wj.setContent(q.getContent());
            wj.setImage(q.getImage());
            wj.setCueword(q.getCueword());
            wj.setType(q.getType());
            wj.setLabelId(q.getLabelId());
            wj.setPoints(q.getPoints());
            wj.setOptions(q.getOptions());
            wj.setAnswer(q.getAnswer());
            wj.setAnalysis(q.getAnalysis());
            wj.setRandomly(q.getRandomly());
            wj.setRequired(q.getRequired());
            wj.setCreatedBy(user.getUserName());
            wj.setQuestionnaireId(questionnaireId); // 关键：设置问卷ID
            wjQuestionsMapper.insert(wj);
        }
        return 1;
    }

    /**
     * 修改问卷管理
     */
    @Transactional
    @Override
    public int updateQuestionnaires(QuestionnaireCreateVO vo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 1. 更新问卷主表
        Questionnaires questionnaire = new Questionnaires();
        questionnaire.setQuestionnaireId(vo.getQuestionnaireId());
        questionnaire.setTitle(vo.getTitle());
        questionnaire.setLableId(vo.getLableId());
        questionnaire.setDescription(vo.getDescription());
        questionnaire.setClassId(vo.getClassId());
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            questionnaire.setStartTime(sdf.parse(vo.getStartTime()));
            questionnaire.setEndTime(sdf.parse(vo.getEndTime()));
        } catch (Exception e) {
            throw new RuntimeException("时间格式错误", e);
        }
        questionnaire.setDuration(Long.valueOf(vo.getDuration()));
        questionnaire.setCreatedBy(user.getUserName());
        questionnairesMapper.updateById(questionnaire);

        // 2. 删除原有题目
        wjQuestionsMapper.delete(
                new LambdaQueryWrapper<WjQuestions>().eq(WjQuestions::getQuestionnaireId, vo.getQuestionnaireId())
        );

        // 3. 批量插入新题目
        for (QuestionnaireCreateVO.QuestionVO q : vo.getQuestions()) {
            WjQuestions wj = new WjQuestions();
            wj.setContent(q.getContent());
            wj.setImage(q.getImage());
            wj.setCueword(q.getCueword());
            wj.setType(q.getType());
            wj.setLabelId(q.getLabelId());
            wj.setPoints(q.getPoints());
            wj.setOptions(q.getOptions());
            wj.setAnswer(q.getAnswer());
            wj.setAnalysis(q.getAnalysis());
            wj.setRandomly(q.getRandomly());
            wj.setRequired(q.getRequired());
            wj.setQuestionnaireId(vo.getQuestionnaireId());
            wj.setCreatedBy(user.getUserName());
            wjQuestionsMapper.insert(wj);
        }
        return 1;
    }

    /**
     * 批量删除问卷管理
     *
     **/
    @Transactional
    @Override
    public int deleteQuestionnairesByQuestionnaireIds(List<Questionnaires> questionnairesList) {
        int a = 0;
        for (Questionnaires questionnaires : questionnairesList) {
            a = questionnairesMapper.updateById(questionnaires);
        }
        return a;
    }

    @Override
    public QuestionnaireVo selectByQuestionnaireId(String questionnaireId) {
        // 1. 查询问卷主表
        Questionnaires questionnaire = questionnairesMapper.selectById(questionnaireId);
        QuestionnaireVo vo = new QuestionnaireVo();
        vo.setQuestionnaireId(questionnaire.getQuestionnaireId());
        vo.setTitle(questionnaire.getTitle());
        vo.setDescription(questionnaire.getDescription());
        vo.setLableId(questionnaire.getLableId());
        vo.setStatus(questionnaire.getStatus());
        vo.setStartTime(questionnaire.getStartTime() != null ? questionnaire.getStartTime() : null);
        vo.setEndTime(questionnaire.getEndTime() != null ? questionnaire.getEndTime() : null);
        vo.setDuration(questionnaire.getDuration() != null ? questionnaire.getDuration().intValue() : null);
        vo.setClassId(questionnaire.getClassId());

        // 2. 查询标签名
        Labels label = labelsMapper.selectById(questionnaire.getLableId());
        vo.setLableName(label != null ? label.getLabelName() : null);

        // 3. 查询班级名
        StudentClass studentClass = studentClassMapper.selectById(questionnaire.getClassId());
        vo.setClassName(studentClass != null ? studentClass.getClassName() : null);

        // 4. 查询题目列表
        List<WjQuestions> questionsList = wjQuestionsMapper.selectList(
                new LambdaQueryWrapper<WjQuestions>().eq(WjQuestions::getQuestionnaireId, questionnaireId)
        );
        vo.setQuestionsList(questionsList);

        return vo;
    }


}
