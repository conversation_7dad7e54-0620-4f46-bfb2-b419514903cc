package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.Questions;
import com.ruoyi.system.mapper.QuestionsMapper;
import com.ruoyi.system.service.IQuestionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 题库管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
public class QuestionsServiceImpl implements IQuestionsService 
{
    @Autowired
    private QuestionsMapper questionsMapper;

    /**
     * 查询题库管理
     * 
     * @param questionId 题库管理主键
     * @return 题库管理
     */
    @Override
    public Questions selectQuestionsByQuestionId(String questionId)
    {
        return questionsMapper.selectById(questionId);
    }

    /**
     * 查询题库管理列表
     * 
     * @param questions 题库管理
     * @return 题库管理
     */
    @Override
    public List<Questions> selectQuestionsList(Questions questions)
    {
        return questionsMapper.selectQuestionsList(questions);
    }

    /**
     * 新增题库管理
     * 
     * @param questions 题库管理
     * @return 结果
     */
    @Override
    public int insertQuestions(Questions questions)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        questions.setCreatedBy(user.getUserName());
        return questionsMapper.insert(questions);
    }

    /**
     * 修改题库管理
     * 
     * @param questions 题库管理
     * @return 结果
     */
    @Override
    public int updateQuestions(Questions questions)
    {
        return questionsMapper.updateById(questions);
    }

    /**
     * 批量删除题库管理
     * 
     * @param questionIds 需要删除的题库管理主键
     * @return 结果
     */
    @Override
    public int deleteQuestionsByQuestionIds(List<Questions> questionIds)
    {
        int a = 0;
        for (Questions questions : questionIds) {
            a = questionsMapper.updateById(questions);
        }
        return a;
    }

    /**
     * 删除题库管理信息
     * 
     * @param questionId 题库管理主键
     * @return 结果
     */
    @Override
    public int deleteQuestionsByQuestionId(String questionId)
    {
        Questions questions = new Questions();
        questions.setQuestionId(questionId);
        questions.setIsDelete(1L);
        return questionsMapper.updateById(questions);
    }

    @Override
    public int batchInsertQuestions(List<Questions> questionsList) {
        return questionsMapper.batchInsertQuestions(questionsList);
    }
}
