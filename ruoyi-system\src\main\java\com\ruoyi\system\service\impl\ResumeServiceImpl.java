package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.ResumeConstants;
import com.ruoyi.system.domain.Resume;
import com.ruoyi.system.domain.ResumeCategory;
import com.ruoyi.system.domain.ResumeCategoryRelation;
import com.ruoyi.system.domain.ResumeHrQuestion;
import com.ruoyi.system.domain.ResumeProgress;
import com.ruoyi.system.domain.SysTodo;
import com.ruoyi.system.domain.SysWechatAuth;
import com.ruoyi.system.domain.dto.ResumeDTO;
import com.ruoyi.system.domain.dto.ResumeHrQueDTO;
import com.ruoyi.system.domain.vo.ResumeFullSaveDto;
import com.ruoyi.system.domain.vo.ResumeInformationVo;
import com.ruoyi.system.domain.vo.ResumeVo;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Service
public class ResumeServiceImpl extends ServiceImpl<ResumeMapper, Resume> implements ResumeService {

    @Autowired
    private ResumeMapper resumeMapper;
    @Autowired
    private ResumeInformationMapper informationMapper;
    @Autowired
    private ResumeEducationalMapper educationalMapper;
    @Autowired
    private ResumeWorkMapper workMapper;
    @Autowired
    private ResumeProjectExperienceMapper projectMapper;
    @Autowired
    private ResumePracticeMapper practiceMapper;
    @Autowired
    private ResumeTalentMapper talentMapper;
    @Autowired
    private ResumeCertificateMapper certificateMapper;
    @Autowired
    private ResumeCampusMapper campusMapper;
    @Autowired
    private ResumeInterestMapper interestMapper;
    @Autowired
    private ResumeEvaluateMapper evaluateMapper;
    @Autowired
    private ResumeHrQuestionMapper hrQuestionMapper;
    @Autowired
    private ResumeProgressService resumeProgressService;
    @Autowired
    private ISysTodoService sysTodoService;
    @Autowired
    private SysWechatAuthService sysWechatAuthService;
    @Autowired
    private WeChatTemplateMsgService weChatTemplateMsgService;
    @Autowired
    private ResumeCategoryMapper resumeCategoryMapper;
    @Autowired
    private ResumeCategoryRelationService resumeCategoryRelationService;
    @Value("${resume.url}")
    private String resumeUrl;
    @Value("${study.url}")
    private String studyUrl;
    @Value("${wechat.resumeTemplateId}")
    private String resumeTemplateId;
    @Override
    public List<ResumeFullSaveDto> getList(ResumeDTO resumeDto) {
        List<Resume> resumes = resumeMapper.selectList(
                new QueryWrapper<Resume>().like("title",resumeDto.getTitle()).like("create_at",resumeDto.getName()).eq("is_delete", 0)
        );
        List<ResumeFullSaveDto> result = new ArrayList<>();
        for (Resume resume : resumes) {
            ResumeFullSaveDto dto = new ResumeFullSaveDto();
            ResumeVo resumeVo = new ResumeVo();
            BeanUtils.copyProperties(resume,resumeVo);
            dto.setResumeVo(resumeVo);
            // 基本信息
            ResumeInformationVo info = informationMapper.selectByResumeId(resume.getResumeId());
            dto.setInformation(info);
            // 教育经历
            dto.setEducationList(educationalMapper.selectByResumeId(resume.getResumeId()));
            // 工作经验
            dto.setWorkList(workMapper.selectByResumeId(resume.getResumeId()));
            // 项目经验
            dto.setProjectList(projectMapper.selectByResumeId(resume.getResumeId()));
            // 练手项目
            dto.setPracticeList(practiceMapper.selectByResumeId(resume.getResumeId()));
            // 技能特长
            dto.setTalentList(talentMapper.selectByResumeId(resume.getResumeId()));
            // 证书奖项
            dto.setCertificateList(certificateMapper.selectByResumeId(resume.getResumeId()));
            // 校园经历
            dto.setCampusList(campusMapper.selectByResumeId(resume.getResumeId()));
            // 兴趣爱好
            dto.setInterestList(interestMapper.selectByResumeId(resume.getResumeId()));
            // 自我评价
            dto.setEvaluateList(evaluateMapper.selectByResumeId(resume.getResumeId()));

            result.add(dto);
        }
        return result;
    }

    @Override
    public ResumeFullSaveDto getResumeDetail(Long resumeId) {
        Resume resume = resumeMapper.selectOne(new QueryWrapper<Resume>().eq("resume_id",resumeId).eq("is_delete", 0));
        ResumeFullSaveDto dto = new ResumeFullSaveDto();
        ResumeVo resumeVo = new ResumeVo();
        BeanUtils.copyProperties(resume,resumeVo);
        dto.setResumeVo(resumeVo);
        // 基本信息
        ResumeInformationVo info = informationMapper.selectByResumeId(resume.getResumeId());
        dto.setInformation(info);
        // 教育经历
        dto.setEducationList(educationalMapper.selectByResumeId(resume.getResumeId()));
        // 工作经验
        dto.setWorkList(workMapper.selectByResumeId(resume.getResumeId()));
        // 项目经验
        dto.setProjectList(projectMapper.selectByResumeId(resume.getResumeId()));
        // 练手项目
        dto.setPracticeList(practiceMapper.selectByResumeId(resume.getResumeId()));
        // 技能特长
        dto.setTalentList(talentMapper.selectByResumeId(resume.getResumeId()));
        // 证书奖项
        dto.setCertificateList(certificateMapper.selectByResumeId(resume.getResumeId()));
        // 校园经历
        dto.setCampusList(campusMapper.selectByResumeId(resume.getResumeId()));
        // 兴趣爱好
        dto.setInterestList(interestMapper.selectByResumeId(resume.getResumeId()));
        // 自我评价
        dto.setEvaluateList(evaluateMapper.selectByResumeId(resume.getResumeId()));

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditResume(Long resumeId, Integer status, String auditOpinion) {
        if (resumeId == null) {
            return false;
        }
        // 查询简历
        Resume resume = resumeMapper.selectById(resumeId);
        if (resume == null) {
            return false;
        }
        // 设置审核状态
        resume.setStatus(status);
        // 设置审核意见
        resume.setAuditOpinion(auditOpinion);
        // 设置更新时间
        resume.setUpdateTime(new Date());
        // 更新简历
        boolean result = resumeMapper.updateById(resume) > 0;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //获取简历类型（开发类，测试类、技术支持类）
        String resumeType = "";
        LambdaQueryWrapper<ResumeCategoryRelation> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.eq(ResumeCategoryRelation::getResumeId, resume.getResumeId());
        List<ResumeCategoryRelation> relations = resumeCategoryRelationService.list(relationWrapper);
        if (!relations.isEmpty()) {
            Long catId = relations.get(0).getCatId();
            ResumeCategory category = resumeCategoryMapper.selectById(catId);
            if (category != null) {
                resumeType = category.getName();
            }
        }
        if (result) {
            // 如果简历审核通过（状态为1），则更新进度并创建待办
            if (status == ResumeConstants.RESUME_STATUS_APPROVED) {
                // 1. 更新用户简历进度为"1：知识学习阶段"
                Map<String, Object> params = new HashMap<>();
                params.put("userId", resume.getUserId().intValue());
                params.put("resumeId", resumeId);
                List<ResumeProgress> progressList = resumeProgressService.getResumeProgressByCondition(params);
                ResumeProgress progress = progressList != null && !progressList.isEmpty() ? progressList.get(0) : null;
                
                if (progress != null) {
                    // 更新现有进度
                    resumeProgressService.updateResumeProgressStage(progress.getId(), ResumeConstants.STAGE_KNOWLEDGE_LEARNING, resumeId);
                } else {
                    // 创建新的进度记录
                    ResumeProgress newProgress = new ResumeProgress();
                    newProgress.setUserId(resume.getUserId().intValue());
                    newProgress.setResumeId(resumeId); // 设置简历ID
                    newProgress.setCurrentStage(ResumeConstants.STAGE_KNOWLEDGE_LEARNING); // 1：知识学习阶段
                    resumeProgressService.createResumeProgress(newProgress);
                }
                
                // 2. 创建待办：标题：知识学习；内容：您创建的简历通过审核啦，请开始学习相关知识！；待办类型：1：知识学习
                SysTodo todo = new SysTodo();
                todo.setUserId(resume.getUserId());
                todo.setResumeId(resumeId); // 设置简历ID
                todo.setTitle("知识学习");
                todo.setContent("您创建的简历（"+resume.getTitle()+"）通过审核啦，请开始学习相关知识！");
                todo.setUrl(studyUrl);
                todo.setTodoType(ResumeConstants.TODO_TYPE_KNOWLEDGE_LEARNING); // 1：知识学习
                todo.setStatus(ResumeConstants.TODO_STATUS_INCOMPLETE); // 0：未完成
                sysTodoService.insertSysTodo(todo);
                //发送服务号模板消息
                SysWechatAuth wechatAuth = sysWechatAuthService.getByUserId(resume.getUserId());
                if (wechatAuth != null && wechatAuth.getOpenid() != null) {
                    // TODO: 调用微信服务号模板消息发送接口
                    // sendWeChatTemplateMessage(wechatAuth.getOpenid(), "您的简历已通过审核，请开始学习相关知识！");
                    String data = "{{\"thing2\":{\"value\":\""+resume.getTitle()+"\"}}," +
                            "{\"const5\":{\"value\":\""+resumeType+"\"}}," +
                            "{\"const3\":{\"value\":\"您的简历审核通过啦！\"}}," +
                            "{\"time4\":{\"value\":\""+sdf.format(new Date())+"\"}}}";
                    weChatTemplateMsgService.sendTemplateMsg(wechatAuth.getOpenid(), resumeTemplateId, data);
                }
            } 
            // 如果简历审核不通过（状态为2），则创建待办
            else if (status == ResumeConstants.RESUME_STATUS_REJECTED) {
                // 创建待办：标题：修改简历；内容：您的简历审核未通过，请及时完成更改！，待办类型：0：创建简历
                SysTodo todo = new SysTodo();
                todo.setUserId(resume.getUserId());
                todo.setResumeId(resumeId); // 设置简历ID
                todo.setTitle("修改简历");
                String content = "您的简历（"+resume.getTitle()+"）审核未通过，请及时完成更改！";
                todo.setContent(content);
                todo.setUrl(resumeUrl);
                todo.setTodoType(ResumeConstants.TODO_TYPE_CREATE_RESUME); // 0：创建简历
                todo.setStatus(ResumeConstants.TODO_STATUS_INCOMPLETE); // 0：未完成
                sysTodoService.insertSysTodo(todo);
                //发送服务号模板消息
                SysWechatAuth wechatAuth = sysWechatAuthService.getByUserId(resume.getUserId());
                if (wechatAuth != null && wechatAuth.getOpenid() != null) {
                    // TODO: 调用微信服务号模板消息发送接口
                    // sendWeChatTemplateMessage(wechatAuth.getOpenid(), "您的简历审核未通过，请及时修改！");
                    String data = "{{\"thing2\":{\"value\":\""+resume.getTitle()+"\"}}," +
                            "{\"const5\":{\"value\":\""+resumeType+"\"}}," +
                            "{\"const3\":{\"value\":\"您的简历审核未通过！\"}}," +
                            "{\"time4\":{\"value\":\""+sdf.format(new Date())+"\"}}}";
                    weChatTemplateMsgService.sendTemplateMsg(wechatAuth.getOpenid(), resumeTemplateId, data);
                }
            }
        }
        
        return result;
    }
}