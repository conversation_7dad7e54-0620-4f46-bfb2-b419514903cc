package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.domain.Skill;
import com.ruoyi.system.domain.SkillSegment;
import com.ruoyi.system.domain.vo.SkillSegmentVO;
import com.ruoyi.system.domain.vo.SkillVO;
import com.ruoyi.system.mapper.SkillMapper;
import com.ruoyi.system.mapper.SkillSegmentMapper;
import com.ruoyi.system.service.SkillService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 技能服务实现类
 */
@Service
public class SkillServiceImpl extends ServiceImpl<SkillMapper, Skill> implements SkillService {

    @Autowired
    private SkillMapper skillMapper;

    @Autowired
    private SkillSegmentMapper skillSegmentMapper;

    @Override
    public List<SkillVO> listAllSkills(String keyword) {
        // 查询技能列表
        List<SkillVO> skills = skillMapper.selectSkills(keyword);
        return skills;
    }

    @Override
    public SkillVO getSkillById(Long id) {
        // 查询技能
        Skill skill = this.getById(id);
        if (skill == null) {
            return null;
        }
        
        // 转换为VO
        SkillVO skillVO = new SkillVO();
        BeanUtils.copyProperties(skill, skillVO);
        
        // 查询并设置技能段落
        List<SkillSegmentVO> segments = skillMapper.selectSegmentsBySkillId(id);
        skillVO.setSegments(segments);
        
        return skillVO;
    }
    
    @Override
    public SkillSegmentVO getSkillSegmentById(Long segId) {
        return skillSegmentMapper.selectSkillSegmentById(segId);
    }
    
    @Override
    @Transactional
    public boolean saveSkillSegment(SkillSegmentVO segmentVO) {
        // 检查技能是否存在，不存在则创建
        Long skillId = segmentVO.getSkillId();
        String skillName = segmentVO.getSegName();
        if (skillId == null && StringUtils.hasText(skillName)) {
            // 查找是否已存在同名技能
            LambdaQueryWrapper<Skill> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Skill::getName, skillName);
            Skill existingSkill = this.getOne(queryWrapper);
            
            if (existingSkill != null) {
                skillId = existingSkill.getSkiId();
            } else {
                // 创建新技能
                Skill newSkill = new Skill();
                newSkill.setName(skillName);
                this.save(newSkill);
                skillId = newSkill.getSkiId();
            }
        }
        
        // 保存技能段落
        SkillSegment segment = new SkillSegment();
        BeanUtils.copyProperties(segmentVO, segment);
        segment.setSkillId(skillId);
        
        return skillSegmentMapper.insert(segment) > 0;
    }
    
    @Override
    @Transactional
    public boolean updateSkillSegment(SkillSegmentVO segmentVO) {
        // 检查技能是否存在，不存在则创建
        Long skillId = segmentVO.getSkillId();
        String skillName = segmentVO.getSegName();
        
        // 如果skillName为空，设置一个默认值
        if (StringUtils.isEmpty(skillName)) {
            // 根据skillId查找技能名称
            if (skillId != null) {
                Skill skill = this.getById(skillId);
                if (skill != null) {
                    skillName = skill.getName() + "技能点";
                    segmentVO.setSegName(skillName);
                } else {
                    // 如果找不到技能，设置一个默认名称
                    skillName = "技能点";
                    segmentVO.setSegName(skillName);
                }
            } else {
                // 如果没有skillId，设置一个默认名称
                skillName = "技能点";
                segmentVO.setSegName(skillName);
            }
        }
        
        if (StringUtils.hasText(skillName)) {
            // 查找是否已存在同名技能
            LambdaQueryWrapper<Skill> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Skill::getName, skillName);
            Skill existingSkill = this.getOne(queryWrapper);
            
            if (existingSkill != null) {
                skillId = existingSkill.getSkiId();
            } else {
                // 创建新技能
                Skill newSkill = new Skill();
                newSkill.setName(skillName);
                this.save(newSkill);
                skillId = newSkill.getSkiId();
            }
        }
        
        // 更新技能段落
        SkillSegment segment = new SkillSegment();
        BeanUtils.copyProperties(segmentVO, segment);
        segment.setSkillId(skillId);
        
        return skillSegmentMapper.updateById(segment) > 0;
    }
    
    @Override
    @Transactional
    public boolean deleteSkillSegment(Long segId) {
        return skillSegmentMapper.deleteById(segId) > 0;
    }
    
    @Override
    public Page<SkillSegmentVO> pageSkillSegments(Integer pageNum, Integer pageSize, String name, String proficiency) {
        Page<SkillSegmentVO> page = new Page<>(pageNum, pageSize);
        return skillSegmentMapper.selectSkillSegmentPage(page, name, proficiency);
    }

    @Override
    @Transactional
    public boolean saveSkillWithSegments(SkillVO skillVO) {
        // 保存技能
        Skill skill = new Skill();
        BeanUtils.copyProperties(skillVO, skill);
        boolean skillSaved = this.save(skill);

        // 保存技能段落
        if (skillSaved && skillVO.getSegments() != null) {
            Long skillId = skill.getSkiId();
            skillVO.getSegments().forEach(segmentVO -> {
                segmentVO.setSkillId(skillId);
                SkillSegment segment = new SkillSegment();
                BeanUtils.copyProperties(segmentVO, segment);
                skillSegmentMapper.insert(segment);
            });
        }

        return skillSaved;
    }

    @Override
    @Transactional
    public boolean updateSkillWithSegments(SkillVO skillVO) {
        // 更新技能
        Skill skill = new Skill();
        BeanUtils.copyProperties(skillVO, skill);
        boolean skillUpdated = this.updateById(skill);
        
        if (skillUpdated && skillVO.getSegments() != null) {
            // 删除旧的技能段落
            LambdaQueryWrapper<SkillSegment> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SkillSegment::getSkillId, skill.getSkiId());
            skillSegmentMapper.delete(wrapper);
            
            // 保存新的技能段落
            skillVO.getSegments().forEach(segmentVO -> {
                segmentVO.setSkillId(skill.getSkiId());
                SkillSegment segment = new SkillSegment();
                BeanUtils.copyProperties(segmentVO, segment);
                skillSegmentMapper.insert(segment);
            });
        }
        
        return skillUpdated;
    }

    @Override
    @Transactional
    public boolean deleteSkillWithSegments(Long id) {
        // 删除技能段落
        LambdaQueryWrapper<SkillSegment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SkillSegment::getSkillId, id);
        skillSegmentMapper.delete(wrapper);
        
        // 删除技能
        return this.removeById(id);
    }
} 