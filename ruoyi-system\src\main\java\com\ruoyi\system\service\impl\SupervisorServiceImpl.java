package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.domain.vo.SupervisorWithTopicsVO;
import com.ruoyi.system.domain.vo.TopicItemVO;
import com.ruoyi.system.domain.vo.TopicWithSupervisorVO;
import com.ruoyi.system.mapper.CategoryMapper;
import com.ruoyi.system.mapper.KsUserMapper;
import com.ruoyi.system.mapper.SupervisorMapper;
import com.ruoyi.system.mapper.TopicMapper;
import com.ruoyi.system.service.SupervisorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: mozai-back-stage
 * @description:
 * @author: coke
 * @create: 2025-05-16 16:28
 **/
@Service
public class SupervisorServiceImpl extends ServiceImpl<SupervisorMapper, Supervisor> implements SupervisorService {

    @Autowired
    private SupervisorMapper supervisorMapper;
    
    @Autowired
    private TopicMapper topicMapper;

    @Autowired
    private KsUserMapper ksUserMapper;

    @Override
    public PageResult<Supervisor> searchSuper(QueryDTO queryDTO) {
        // 开启分页
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        // 查询导师列表
        List<Supervisor> list = baseMapper.selectByCondition(queryDTO);
        PageResult<Supervisor> result = new PageResult<>(queryDTO.getPageNum(), queryDTO.getPageSize(),
                ((com.github.pagehelper.Page<?>) list).getTotal(), list);

        return result;
    }

    @Override
    public int insertSupervisor(Supervisor supervisor) {
        return baseMapper.insert(supervisor);
    }

    @Override
    public int updateSupervisor(Supervisor supervisor) {
        return baseMapper.updateSuper(supervisor);
    }

    @Override
    public int deleteSupervisorByIds(Integer superId) {
        return baseMapper.deleteSuper(superId);
    }

    @Override
    public Supervisor selectById(Integer supervisorId) {
        return supervisorMapper.selectById(supervisorId);
    }

    @Override
    public SupervisorWithTopicsVO selectBySupervisorId(Integer supervisorId) {
        SupervisorWithTopicsVO supervisorWithTopicsVO = new SupervisorWithTopicsVO();
        Supervisor supervisor = supervisorMapper.selectById(supervisorId);
        List<TopicWithSupervisorVO> topics = topicMapper.selectBySupervisorId(supervisorId);
        for (TopicWithSupervisorVO topic : topics) {
            KsUser ksUser = ksUserMapper.selectBySupervisorId(supervisorId);
            topic.setNickname(ksUser.getNickname());
            topic.setPhone(ksUser.getPhone());
        }
        supervisorWithTopicsVO.setSupervisor(supervisor);
        supervisorWithTopicsVO.setTopics(topics);
        return supervisorWithTopicsVO;
    }
}
