package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SysTodoMapper;
import com.ruoyi.system.domain.SysTodo;
import com.ruoyi.system.service.ISysTodoService;

/**
 * 待办Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysTodoServiceImpl implements ISysTodoService 
{
    @Autowired
    private SysTodoMapper sysTodoMapper;

    /**
     * 查询待办
     * 
     * @param id 待办ID
     * @return 待办
     */
    @Override
    public SysTodo selectSysTodoById(Long id)
    {
        return sysTodoMapper.selectSysTodoById(id);
    }

    /**
     * 查询待办列表
     * 
     * @param sysTodo 待办
     * @return 待办
     */
    @Override
    public List<SysTodo> selectSysTodoList(SysTodo sysTodo)
    {
        return sysTodoMapper.selectSysTodoList(sysTodo);
    }

    /**
     * 查询用户待办列表
     * 
     * @param userId 用户ID
     * @return 待办集合
     */
    @Override
    public List<SysTodo> selectSysTodoByUserId(Long userId)
    {
        return sysTodoMapper.selectSysTodoByUserId(userId);
    }

    /**
     * 新增待办
     * 
     * @param sysTodo 待办
     * @return 结果
     */
    @Override
    public int insertSysTodo(SysTodo sysTodo)
    {
        return sysTodoMapper.insertSysTodo(sysTodo);
    }

    /**
     * 修改待办
     * 
     * @param sysTodo 待办
     * @return 结果
     */
    @Override
    public int updateSysTodo(SysTodo sysTodo)
    {
        return sysTodoMapper.updateSysTodo(sysTodo);
    }

    /**
     * 批量删除待办
     * 
     * @param ids 需要删除的待办ID
     * @return 结果
     */
    @Override
    public int deleteSysTodoByIds(Long[] ids)
    {
        return sysTodoMapper.deleteSysTodoByIds(ids);
    }

    /**
     * 删除待办信息
     * 
     * @param id 待办ID
     * @return 结果
     */
    @Override
    public int deleteSysTodoById(Long id)
    {
        return sysTodoMapper.deleteSysTodoById(id);
    }
} 