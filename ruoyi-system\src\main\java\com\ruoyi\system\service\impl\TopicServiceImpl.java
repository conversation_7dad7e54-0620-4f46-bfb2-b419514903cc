package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ruoyi.system.domain.PageResult;
import com.ruoyi.system.domain.Topic;
import com.ruoyi.system.domain.dto.QueryDTO;
import com.ruoyi.system.domain.vo.TopicItemVO;
import com.ruoyi.system.mapper.TopicMapper;
import com.ruoyi.system.service.TopicService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * @program: mozai-topic-system
 * @description:
 * @author: coke
 * @create: 2025-05-13 15:06
 **/
@Service
public class TopicServiceImpl extends ServiceImpl<TopicMapper, Topic> implements TopicService {

    @Override
    public PageResult<TopicItemVO> searchTopics(QueryDTO queryDTO) {
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        List<TopicItemVO> topics = baseMapper.selectByCondition(queryDTO);
        PageResult<TopicItemVO> result = new PageResult<>(queryDTO.getPageNum(), queryDTO.getPageSize(),
                ((com.github.pagehelper.Page<?>) topics).getTotal(), topics);
        result.setAvailableTotal(baseMapper.countAvailableTopics());
        return result;
    }

    @Override
    public Topic getTopicById(int id) {
        return baseMapper.selectDetailById(id);
    }

    @Override
    @Transactional
    public void addTopic(Topic topic) {
        // 参数校验
        if (topic.getTitle() == null || topic.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("课题标题不能为空");
        }
        if (topic.getStatus() < 0 || topic.getStatus() > 2) {
            throw new IllegalArgumentException("课题状态值不合法");
        }
        
        // 自动填充系统字段
        topic.setPublishDate(new Date());
        topic.setCreateTime(new Date());
        topic.setIsDelete(0);
        System.out.println(topic);
        
        // 插入数据库
        baseMapper.insertTopic(topic);
    }

    @Override
    public void updateTopic(Topic topic) {
        Date date = new Date();
        topic.setEditTime(date);
        topic.setStatus(1);
        baseMapper.updateTopic(topic);
    }

    @Override
    @Transactional
    public void deleteTopic(int topicId) {
        // 先查询确保记录存在
        Topic existingTopic = baseMapper.selectTopicById(topicId);
        if (existingTopic == null) {
            throw new RuntimeException("要删除的课题不存在，ID：" + topicId);
        }

        // 执行逻辑删除
        existingTopic.setIsDelete(1);
        existingTopic.setEditTime(new Date());

        int result = baseMapper.updateById(existingTopic);
        if (result <= 0) {
            throw new RuntimeException("删除课题失败，请稍后重试");
        }
    }

    @Override
    public void checkTopic(Integer topicId) {
        baseMapper.checkTopic(topicId);
    }
}
