package com.ruoyi.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.system.service.WeChatTemplateMsgService;
import com.ruoyi.common.utils.http.HttpUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import cn.hutool.http.HttpUtil;

@Service
public class WeChatTemplateMsgServiceImpl implements WeChatTemplateMsgService {
    @Value("${wechat.appsecret}")
    private String secret;
    @Value("${wechat.appid}")
    private String appid;
    @Override
    public boolean sendTemplateMsg(String openId, String templateId, String data) {
        // TODO: Replace with actual WeChat API endpoint and access_token management
        String accessToken = getAccessToken();
        String url = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
        String payload = String.format("{\"touser\":\"%s\",\"template_id\":\"%s\",\"data\":%s}", openId, templateId, data);
        String response = HttpUtils.sendPost(url, payload, "application/json");
        // TODO: Parse response and return true if success
        return response != null && response.contains("ok");
    }
    private String getAccessToken() {
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";
        url = url.replace("APPID", appid).replace("APPSECRET", secret);
        String json = HttpUtil.get(url);
        JSONObject jsonObject = JSON.parseObject(json);
        return jsonObject.getString("access_token");
    }
} 