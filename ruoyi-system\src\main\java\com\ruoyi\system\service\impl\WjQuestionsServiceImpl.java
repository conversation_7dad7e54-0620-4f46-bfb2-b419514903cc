package com.ruoyi.system.service.impl;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.WjQuestions;
import com.ruoyi.system.mapper.LabelsMapper;
import com.ruoyi.system.mapper.WjQuestionsMapper;
import com.ruoyi.system.service.IWjQuestionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 问卷题库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
public class WjQuestionsServiceImpl implements IWjQuestionsService 
{
    @Autowired
    private WjQuestionsMapper wjQuestionsMapper;

    @Autowired
    private LabelsMapper labelsMapper;

    /**
     * 查询问卷题库
     * 
     * @param questionsId 问卷题库主键
     * @return 问卷题库
     */
    @Override
    public WjQuestions selectWjQuestionsByQuestionsId(Long questionsId)
    {
        return wjQuestionsMapper.selectById(questionsId);
    }

    /**
     * 查询问卷题库列表
     *
     * @param wjQuestions 问卷题库
     * @return 问卷题库
     */
    @Override
    public List<WjQuestions> selectWjQuestionsList(WjQuestions wjQuestions)
    {
        return wjQuestionsMapper.selectWjQuestionsList(wjQuestions);
    }

    /**
     * 新增问卷题库
     * 
     * @param wjQuestions 问卷题库
     * @return 结果
     */
    @Override
    public int insertWjQuestions(WjQuestions wjQuestions)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        wjQuestions.setCreatedBy(user.getUserName());
        return wjQuestionsMapper.insert(wjQuestions);
    }

    /**
     * 修改问卷题库
     * 
     * @param wjQuestions 问卷题库
     * @return 结果
     */
    @Override
    public int updateWjQuestions(WjQuestions wjQuestions)
    {
        return wjQuestionsMapper.updateById(wjQuestions);
    }

    /**
     * 批量删除问卷题库
     * 
     * @param questionsIds 需要删除的问卷题库主键集合
     * @return 结果
     */
    @Override
    public int deleteWjQuestionsByQuestionsIds(Long[] questionsIds)
    {
        int count = 0;
        for (Long questionsId : questionsIds) {
            WjQuestions wjQuestions = new WjQuestions();
            wjQuestions.setQuestionsId(questionsId);
            wjQuestions.setIsDelete(1L);
            count += wjQuestionsMapper.updateById(wjQuestions);
        }
        return count;
    }

    @Override
    public long selectWjQuestionsCount(WjQuestions wjQuestions) {
        return wjQuestionsMapper.selectWjQuestionsCount(wjQuestions);
    }

    /*  *//**
     * 删除问卷题库信息
     * 
     * @param questionsId 问卷题库主键
     * @return 结果
     *//*
    @Override
    public int deleteWjQuestionsByQuestionsId(Long questionsId)
    {
        WjQuestions wjQuestions = new WjQuestions();
        wjQuestions.setQuestionsId(questionsId);
        wjQuestions.setIsDelete(1L);
        return wjQuestionsMapper.updateById(wjQuestions);
    }

    *//**
     * 批量获取问卷题目信息
     * 
     * @param questionIds 题目ID列表
     * @return 问卷题目列表
     *//*
    @Override
    public List<WjQuestions> selectWjQuestionsByIds(List<String> questionIds) {
        return wjQuestionsMapper.selectWjQuestionsByIds(questionIds);
    }*/
}
