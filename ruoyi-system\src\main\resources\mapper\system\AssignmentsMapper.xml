<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AssignmentsMapper">

    <resultMap type="com.ruoyi.system.domain.vo.AssignmentsPageVo" id="AssignmentsPageVoResult">
        <id     property="assignmentId"    column="assignment_id"    />
        <result property="name"            column="name"            />
        <result property="description"     column="description"     />
        <result property="labelId"         column="label_id"        />
        <result property="labelName"       column="label_name"      />
        <result property="totalScore"      column="total_score"     />
        <result property="classId"         column="class_id"        />
        <result property="className"       column="class_name"      />
        <result property="createdBy"       column="created_by"      />
        <result property="createdAt"       column="created_at"      />
        <result property="updatedAt"       column="updated_at"      />
        <result property="isDelete"        column="is_delete"       />
    </resultMap>

    <sql id="selectAssignmentsVo">
        select a.assignment_id, a.name, a.description, a.label_id, l.label_name,
               a.total_score, a.class_id, c.class_name, a.created_by, a.created_at, 
               a.updated_at, a.is_delete
        from assignments a
        left join labels l on a.label_id = l.label_id
        left join student_class c on a.class_id = c.class_id
    </sql>

    <select id="selectAssignmentsList" parameterType="com.ruoyi.system.domain.dto.AssignmentsDTO" resultMap="AssignmentsPageVoResult">
        <include refid="selectAssignmentsVo"/>
        <where>
            <if test="name != null and name != ''">
                AND a.name like concat('%', #{name}, '%')
            </if>
            <if test="labelId != null">
                AND a.label_id = #{labelId}
            </if>
            <if test="classId != null">
                AND a.class_id = #{classId}
            </if>
            <if test="createdBy != null and createdBy != ''">
                AND a.created_by = #{createdBy}
            </if>
            AND a.is_delete = 0
        </where>
        order by a.created_at desc
    </select>

    <select id="selectAssignmentsByAssignmentId" parameterType="String" resultMap="AssignmentsPageVoResult">
        <include refid="selectAssignmentsVo"/>
        where a.assignment_id = #{assignmentId} and a.is_delete = 0
    </select>

    <update id="deleteAssignmentsByAssignmentIds" parameterType="java.util.List">
        update assignments set is_delete = 1
        where assignment_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.assignmentId}
        </foreach>
    </update>
</mapper>