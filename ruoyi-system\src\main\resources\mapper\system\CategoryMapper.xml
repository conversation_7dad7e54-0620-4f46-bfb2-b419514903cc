<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.CategoryMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.Category">
        <id column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="create_time" property="createTime"/>
        <result column="edit_time" property="editTime"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>
    <insert id="insertCate">
        INSERT INTO topic_category (category_name, create_time, edit_time, is_delete)
        VALUES (#{categoryName}, #{createTime}, #{editTime}, #{isDelete})
    </insert>

    <update id="deleteCate">
        UPDATE topic_category
        SET is_delete = 1
        WHERE category_id = #{categoryId}
    </update>
    <update id="updateCate">
        UPDATE topic_category SET category_name = #{categoryName}, create_time=#{createTime},
        edit_time = #{editTime},is_delete = #{isDelete} where category_id = #{categoryId}
    </update>

    <select id="selectByCondition" resultType="com.ruoyi.system.domain.Category">
        SELECT
        category_id,
        category_name,
        create_time,
        edit_time,
        is_delete
        FROM topic_category
        <where>
            is_delete = 0
            <if test="keyword != null and keyword != ''">
                AND category_name LIKE CONCAT('%', #{keyword}, '%')
            </if>
        </where>

    </select>
    <select id="selectById" resultType="com.ruoyi.system.domain.Category">
        SELECT category_id, category_name, create_time, edit_time, is_delete FROM topic_category WHERE category_id = #{categoryId}
    </select>
</mapper>