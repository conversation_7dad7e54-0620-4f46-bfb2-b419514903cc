<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ExamsMapper">

    <sql id="selectExamsVo">
        select
            e.exam_id,
            e.title,
            e.description,
            e.start_time,
            e.label_id,
            e.end_time,
            e.duration,
            e.passing_mark,
            e.status,
            e.total_score,
            e.class_id,
            e.created_by,
            e.created_at,
            e.updated_at,
            e.is_delete,
            c.class_name
        from exams e
                 left join student_class c on e.class_id = c.class_id
    </sql>

    <select id="selectExamsList" resultType="com.ruoyi.system.domain.vo.ExamsPageVo">
        <include refid="selectExamsVo"/>
        <where>
            <if test="title != null and title != ''">
                AND e.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="description != null and description != ''">
                AND e.description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="startTime != null"> AND e.start_time = #{startTime}</if>
            <if test="labelId != null"> AND e.label_id = #{labelId}</if>
            <if test="endTime != null"> AND e.end_time = #{endTime}</if>
            <if test="duration != null"> AND e.duration = #{duration}</if>
            <if test="passingMark != null"> AND e.passing_mark = #{passingMark}</if>
            <if test="status != null and status != ''"> AND e.status = #{status}</if>
            <if test="totalScore != null"> AND e.total_score = #{totalScore}</if>
            <if test="classId != null and classId != ''"> AND e.class_id = #{classId}</if>
            <if test="createdBy != null and createdBy != ''"> AND e.created_by = #{createdBy}</if>
            <if test="createdAt != null"> AND e.created_at = #{createdAt}</if>
            <if test="updatedAt != null"> AND e.updated_at = #{updatedAt}</if>
            <if test="className != null and className != ''">
                AND c.class_name LIKE CONCAT('%', #{className}, '%')
            </if>
            AND e.is_delete = 0
            AND c.is_delete = 0
            order by e.created_at desc
        </where>
    </select>
</mapper>