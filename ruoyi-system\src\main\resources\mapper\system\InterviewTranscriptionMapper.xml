<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.system.mapper.InterviewTranscriptionMapper">
    
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.InterviewTranscription">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="interview_id" property="interviewId" jdbcType="VARCHAR"/>
        <result column="question_index" property="questionIndex" jdbcType="INTEGER"/>
        <result column="question" property="question" jdbcType="VARCHAR"/>
        <result column="transcription" property="transcription" jdbcType="VARCHAR"/>
        <result column="audio_url" property="audioUrl" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, interview_id, question_index, question, transcription, audio_url, create_time, update_time
    </sql>
    

    <!-- 根据面试ID查询所有记录 -->
    <select id="selectByInterviewId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM interview_transcription
        WHERE interview_id = #{interviewId,jdbcType=VARCHAR}
        ORDER BY question_index ASC
    </select>

</mapper> 