<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace等于mapper接口类的全限定名,这样实现对应 -->
<mapper namespace="com.ruoyi.system.mapper.ProjectMapper">

    <select id="selectProject" resultType="com.ruoyi.system.domain.vo.ProjectVO">
        select resume_project.*
            ,resume_project_content.text
            ,resume_project_question.question as questions
            ,resume_category.name as cat_name,
        from resume_project
            left join resume_project_content on resume_project.pro_id = resume_project_content.project_id
            left join resume_project_question on resume_project.pro_id = resume_project_question.project_id
            left join resume_category on resume_project.cat_id = resume_category.cat_id
        <where>
                <if test="keyword != null and keyword != ''">
                    and name like concat('%', #{keyword}, '%')
                </if>
            </where>
    </select>
</mapper>