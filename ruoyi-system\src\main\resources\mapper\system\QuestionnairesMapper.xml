<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.QuestionnairesMapper">

    <sql id="selectQuestionnairesVo">
        select questionnaire_id, title, lable_id, description, class_id, start_time, end_time, duration, status, created_by, created_at, updated_at, is_delete from questionnaires
    </sql>

    <select id="selectQuestionnairesList" resultType="com.ruoyi.system.domain.Questionnaires">
        <include refid="selectQuestionnairesVo"/>
        <where>
            <if test="title != null  and title != ''"> and title LIKE CONCAT('%', #{title}, '%')</if>
            <if test="lableId != null "> and lable_id = #{lableId}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="classId != null  and classId != ''"> and class_id = #{classId}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="duration != null "> and duration = #{duration}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
            and is_delete = 0
        </where>
    </select>
</mapper>