<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.QuestionsMapper">

    <sql id="selectQuestionsVo">
        select question_id, content, type, subject, difficulty, label, points, options, answer, analysis, answering_time,classification.name as topic_classification, randomly, questions.created_by, questions.created_at, questions.updated_at, questions.is_delete from questions
                                                                                                                                                                                                                                                                               inner join  classification on questions.topic_classification = classification.id
    </sql>

    <select id="selectQuestionsList" resultType="com.ruoyi.system.domain.Questions">
        <include refid="selectQuestionsVo"/>
        <where>
            <if test="content != null  and content != ''"> and content LIKE CONCAT('%', #{content}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="subject != null  and subject != ''"> and subject = #{subject}</if>
            <if test="difficulty != null  and difficulty != ''"> and difficulty = #{difficulty}</if>
            <if test="label != null  and label != ''"> and label = #{label}</if>
            <if test="points != null "> and points = #{points}</if>
            <if test="options != null  and options != ''"> and options = #{options}</if>
            <if test="answer != null  and answer != ''"> and answer = #{answer}</if>
            <if test="analysis != null  and analysis != ''"> and analysis = #{analysis}</if>
            <if test="answeringTime != null "> and answering_time = #{answeringTime}</if>
            <if test="topicClassification != null  and topicClassification != ''"> and topic_classification = #{topicClassification}</if>
            <if test="randomly != null "> and randomly = #{randomly}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="updatedAt != null "> and updated_at = #{updatedAt}</if>
            and questions.is_delete = 0
        </where>
    </select>

    <insert id="batchInsertQuestions" parameterType="java.util.List">
        insert into questions (
            question_id, content, type, subject, difficulty, label, points,
            options, answer, analysis, answering_time, topic_classification,
            randomly, created_by, created_at, updated_at, is_delete
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.questionId}, #{item.content}, #{item.type}, #{item.subject},
            #{item.difficulty}, #{item.label}, #{item.points}, #{item.options},
            #{item.answer}, #{item.analysis}, #{item.answeringTime},
            #{item.topicClassification}, #{item.randomly}, #{item.createdBy},
            sysdate(), sysdate(), 0
            )
        </foreach>
    </insert>
</mapper>