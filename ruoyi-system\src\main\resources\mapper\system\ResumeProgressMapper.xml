<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ruoyi.system.mapper.ResumeProgressMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.ResumeProgress">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="resume_id" property="resumeId" jdbcType="BIGINT"/>
        <result column="current_stage" property="currentStage" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, user_id, resume_id, current_stage, create_time, update_time, is_deleted
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.ruoyi.system.domain.ResumeProgress" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO resume_progress (
            user_id, resume_id, current_stage, create_time, update_time, is_deleted
        ) VALUES (
                     #{userId,jdbcType=INTEGER},
                     #{resumeId,jdbcType=BIGINT},
                     #{currentStage,jdbcType=VARCHAR},
                     NOW(),
                     NOW(),
                     0
                 )
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.ruoyi.system.domain.ResumeProgress">
        UPDATE resume_progress
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=INTEGER},
            </if>
            <if test="resumeId != null">
                resume_id = #{resumeId,jdbcType=BIGINT},
            </if>
            <if test="currentStage != null">
                current_stage = #{currentStage,jdbcType=VARCHAR},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </update>

    <!-- 更新进度阶段 -->
    <update id="updateStage">
        UPDATE resume_progress
        SET current_stage = #{currentStage,jdbcType=VARCHAR},
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </update>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM resume_progress
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </select>

    <!-- 根据用户ID查询 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM resume_progress
        WHERE user_id = #{userId,jdbcType=INTEGER} AND is_deleted = 0
        LIMIT 1
    </select>

    <!-- 根据条件查询 -->
    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT <include refid="Base_Column_List" />
        FROM resume_progress
        WHERE is_deleted = 0
        <if test="userId != null">
            AND user_id = #{userId,jdbcType=INTEGER}
        </if>
        <if test="resumeId != null">
            AND resume_id = #{resumeId,jdbcType=BIGINT}
        </if>
        <if test="currentStage != null">
            AND current_stage = #{currentStage,jdbcType=VARCHAR}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 逻辑删除 -->
    <update id="deleteById">
        UPDATE resume_progress
        SET is_deleted = 1,
            update_time = NOW()
        WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
    </update>
</mapper> 