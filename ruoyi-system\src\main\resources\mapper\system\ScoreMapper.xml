<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ScoreMapper">

    <select id="findByExamIdAndUid" resultType="com.ruoyi.system.domain.Scores">
        select * from scores
        <where>
            <if test="arg0 != null and arg0 != ''"> AND exam_id = #{arg0}</if>
            <if test="arg1 != null and arg1 != ''"> AND student_id = #{arg1}</if>
            <if test="arg2 != null and arg2 != ''"> AND status = #{arg2}</if>
        </where>
    </select>
</mapper>