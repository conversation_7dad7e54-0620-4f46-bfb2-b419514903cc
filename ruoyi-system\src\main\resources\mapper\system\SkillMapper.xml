<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace等于mapper接口类的全限定名,这样实现对应 -->
<mapper namespace="com.ruoyi.system.mapper.SkillMapper">

    <select id="selectSkills" resultType="com.ruoyi.system.domain.vo.SkillVO">
        SELECT
        resume_skill.ski_id,
        resume_skill.name
        FROM resume_skill
        <where>
            <if test="keyword != null and keyword != ''">
                AND (
                resume_skill.name LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
    </select>

</mapper>