<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SkillSegmentMapper">

    <resultMap id="SkillSegmentVOMap" type="com.ruoyi.system.domain.vo.SkillSegmentVO">
        <id property="segId" column="seg_id"/>
        <result property="skillId" column="skill_id"/>
        <result property="segName" column="seg_name"/>
        <result property="proficiency" column="proficiency"/>
        <result property="text" column="text"/>
        <result property="skillName" column="name"/>
    </resultMap>

    <select id="selectSkillSegmentPage" resultMap="SkillSegmentVOMap">
        SELECT 
            s.seg_id,
            s.skill_id,
            s.seg_name,
            s.proficiency,
            s.text
        FROM 
            resume_skill_segment s
        LEFT JOIN 
            resume_skill r ON s.skill_id = r.ski_id
        <where>
            <if test="name != null and name != ''">
                AND r.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="proficiency != null and proficiency != ''">
                AND s.proficiency = #{proficiency}
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>
    
    <select id="selectSkillSegmentById" resultMap="SkillSegmentVOMap">
        SELECT 
            s.seg_id,
            s.skill_id,
            s.seg_name,
            s.proficiency,
            s.text
        FROM 
            resume_skill_segment s
        LEFT JOIN 
            resume_skill r ON s.skill_id = r.ski_id
        WHERE 
            s.seg_id = #{segId}
    </select>
</mapper> 