<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.StudentClassMapper">

    <select id="selectByClassId" resultType="com.ruoyi.system.domain.KsUser">
        select * from ks_user inner join cs_manage cm on ks_user.u_id = cm.u_id where class_id = #{classId}
    </select>
    <select id="selectByClassIdAndUserId" resultType="com.ruoyi.system.domain.KsUser">
        select * from ks_user inner join cs_manage cm on ks_user.u_id = cm.u_id
        <where>
        <if test="classId != null and classId != ''"> AND ks_user.class_id = #{classId}</if>
        <if test="studentNo != null and studentNo != ''"> AND ks_user.phone = #{studentNo}</if>
        <if test="name != null and name != ''"> AND ks_user.nickname = #{name}</if>
        </where>
    </select>

</mapper>