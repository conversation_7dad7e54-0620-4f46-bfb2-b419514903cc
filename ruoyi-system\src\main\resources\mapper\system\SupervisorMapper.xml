<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- namespace等于mapper接口类的全限定名,这样实现对应 -->
<mapper namespace="com.ruoyi.system.mapper.SupervisorMapper">

    <select id="selectByCondition" resultType="com.ruoyi.system.domain.Supervisor">
        select * from supervisor
        <where>
             is_delete = 0
            <if test="keyword != null and keyword != ''">
                and supervisor_name like concat('%', #{keyword}, '%')
                or department like concat('%', #{keyword}, '%')
                or title like concat('%', #{keyword}, '%')
            </if>
        </where>

    </select>
    <select id="selectById" resultType="com.ruoyi.system.domain.Supervisor">
        select * from supervisor where supervisor_id = #{supervisorId}
    </select>

    <update id="deleteSuper">
        update supervisor set is_delete = 1 where supervisor_id = #{supervisorId}
    </update>
    <update id="updateSuper">
        update supervisor set supervisor_name = #{supervisorName},department = #{department},title=#{title},
           phone_number = #{phoneNumber},email = #{email},create_time = #{createTime}, edit_time=#{editTime},
        is_delete = 0 where supervisor_id = #{supervisorId}
    </update>
</mapper>