<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysTodoMapper">

    <resultMap type="com.ruoyi.system.domain.SysTodo" id="SysTodoResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="resumeId"    column="resume_id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="url"    column="url"    />
        <result property="todoType"    column="todo_type"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysTodoVo">
        select id, user_id, resume_id, title, content, url, todo_type, status, create_time, update_time from sys_todo
    </sql>

    <select id="selectSysTodoList" parameterType="com.ruoyi.system.domain.SysTodo" resultMap="SysTodoResult">
        <include refid="selectSysTodoVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="resumeId != null "> and resume_id = #{resumeId}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="todoType != null "> and todo_type = #{todoType}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始创建时间 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束创建时间 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectSysTodoById" parameterType="Long" resultMap="SysTodoResult">
        <include refid="selectSysTodoVo"/>
        where id = #{id}
    </select>

    <select id="selectSysTodoByUserId" parameterType="Long" resultMap="SysTodoResult">
        <include refid="selectSysTodoVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <insert id="insertSysTodo" parameterType="com.ruoyi.system.domain.SysTodo">
        insert into sys_todo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="resumeId != null">resume_id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="url != null">url,</if>
            <if test="todoType != null">todo_type,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="resumeId != null">#{resumeId},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="url != null">#{url},</if>
            <if test="todoType != null">#{todoType},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateSysTodo" parameterType="com.ruoyi.system.domain.SysTodo">
        update sys_todo
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="resumeId != null">resume_id = #{resumeId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="url != null">url = #{url},</if>
            <if test="todoType != null">todo_type = #{todoType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysTodoById" parameterType="Long">
        delete from sys_todo where id = #{id}
    </delete>

    <delete id="deleteSysTodoByIds" parameterType="String">
        delete from sys_todo where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 