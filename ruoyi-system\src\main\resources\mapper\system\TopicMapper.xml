<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "https://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ruoyi.system.mapper.TopicMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.Topic">
        <id column="topic_id" property="topicId"/>
        <result column="title" property="title"/>
        <result column="description" property="description"/>
        <result column="supervisor_id" property="supervisorId"/>
        <result column="category_id" property="categoryId"/>
        <result column="tag" property="tag"/>
        <result column="publish_date" property="publishDate"/>
        <result column="deadline" property="deadline"/>
        <result column="quota" property="quota"/>
        <result column="status" property="status"/>
        <result column="supervisor_name" property="supervisorName" jdbcType="VARCHAR"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="selectByCondition" resultType="com.ruoyi.system.domain.vo.TopicItemVO">
        SELECT
        topic.topic_id,
        topic.title,
        topic.description,
        topic.tag,
        supervisor.supervisor_name,
        topic_category.category_name,
        topic.publish_date as publishDate,
        topic.quota,
        topic.status
        FROM topic
        LEFT JOIN supervisor ON topic.supervisor_id = supervisor.supervisor_id
        LEFT JOIN topic_category ON topic.category_id = topic_category.category_id
        <where>
            topic.is_delete = 0
            <if test="keyword != null and keyword != ''">
                AND (topic.title LIKE CONCAT('%', #{keyword}, '%')
                OR topic.description LIKE CONCAT('%', #{keyword}, '%')
                OR supervisor_name LIKE CONCAT('%', #{keyword}, '%')
                OR tag LIKE CONCAT('%', #{keyword}, '%')
                OR status LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY topic.status DESC
    </select>

    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM topic
        <where>
            <if test="keyword != null and keyword != ''">
                AND topic.title LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="categoryId != null">
                AND topic.category_id = #{categoryId}
            </if>
            <if test="status != null">
                AND topic.status = #{status}
            </if>
        </where>
    </select>

    <insert id="insertTopic">
        INSERT INTO topic (
        title, description, supervisor_id, category_id, tag,
        publish_date, quota, status
        ) VALUES (
        #{title}, #{description}, #{supervisorId}, #{categoryId}, #{tag},
        #{publishDate}, #{quota}, #{status}
        )
    </insert>


    <update id="updateStatus">
        UPDATE topic
        SET status = 0
        WHERE topic_id = #{topicId}
    </update>

    <update id="updateTopic">
        UPDATE topic
                SET title = #{title}, description = #{description}, supervisor_id = #{supervisorId}, category_id = #{categoryId}, tag = #{tag},
                publish_date = #{publishDate}, deadline = #{deadline}, quota = #{quota}, status = #{status}
         WHERE topic_id = #{topicId};
    </update>
    <update id="checkTopic">
        UPDATE topic_application SET status = 1
    </update>

    <sql id="Base_Column_List">
        topic.topic_id,
        topic.title,
        topic.description,
        topic.supervisor_id,
        topic.category_id,
        topic.tag,
        topic.publish_date,
        topic.deadline,
        topic.quota,
        topic.status
    </sql>

    <select id="selectDetailById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>,
        supervisor.supervisor_name AS supervisor_name,
        topic_category.category_name AS category_name
        FROM topic
        LEFT JOIN supervisor ON topic.supervisor_id = supervisor.supervisor_id
        LEFT JOIN topic_category ON topic.category_id = topic_category.category_id
        WHERE topic.topic_id = #{topicId}
    </select>

    <select id="selectTopicById" resultType="com.ruoyi.system.domain.Topic">
        SELECT * FROM topic WHERE topic_id = #{topicId}
    </select>

    <select id="selectBySupervisorId" parameterType="Integer" resultType="com.ruoyi.system.domain.vo.TopicWithSupervisorVO">
        select topic.*,topic_category.category_name from topic
        left join topic_category on topic.category_id = topic_category.category_id
        where supervisor_id = #{supervisorId} 
        and topic.is_delete = 0
    </select>
</mapper>
