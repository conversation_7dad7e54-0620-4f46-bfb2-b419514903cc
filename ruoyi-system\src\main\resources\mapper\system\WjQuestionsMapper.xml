<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.WjQuestionsMapper">

    <sql id="selectWjQuestionsVo">
        SELECT *
        FROM (
                 SELECT
                     questions_id, content, image, cueword, type, label_id, points, options, answer, analysis,
                     questionnaire_id, randomly, required, created_by, created_at, updated_at, is_delete,
                     ROW_NUMBER() OVER (PARTITION BY content ORDER BY created_at DESC) as rn
                 FROM wj_questions
                 WHERE is_delete = 0
             ) AS t
        WHERE rn = 1
    </sql>

    <select id="selectWjQuestionsList" resultType="com.ruoyi.system.domain.WjQuestions">
        <include refid="selectWjQuestionsVo"/>
        <if test="content != null  and content != ''"> AND t.content LIKE CONCAT('%', #{content}, '%')</if>
        <if test="image != null  and image != ''"> AND t.image = #{image}</if>
        <if test="cueword != null  and cueword != ''"> AND t.cueword = #{cueword}</if>
        <if test="type != null  and type != ''"> AND t.type = #{type}</if>
        <if test="labelId != null "> AND t.label_id = #{labelId}</if>
        <if test="points != null "> AND t.points = #{points}</if>
        <if test="options != null  and options != ''"> AND t.options = #{options}</if>
        <if test="answer != null  and answer != ''"> AND t.answer = #{answer}</if>
        <if test="analysis != null  and analysis != ''"> AND t.analysis = #{analysis}</if>
        <if test="randomly != null "> AND t.randomly = #{randomly}</if>
        <if test="required != null "> AND t.required = #{required}</if>
        <if test="createdBy != null  and createdBy != ''"> AND t.created_by = #{createdBy}</if>
        <if test="createdAt != null "> AND t.created_at = #{createdAt}</if>
        <if test="updatedAt != null "> AND t.updated_at = #{updatedAt}</if>
        ORDER BY t.created_at DESC
    </select>
    <select id="selectWjQuestionsByIds" resultType="com.ruoyi.system.domain.WjQuestions">
        SELECT *
        FROM wj_questions
        WHERE questions_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectWjQuestionsCount" parameterType="WjQuestions" resultType="long">
        SELECT COUNT(*)
        FROM (
            SELECT
                questions_id, content, image, cueword, type, label_id, points, options, answer, analysis,
                questionnaire_id, randomly, required, created_by, created_at, updated_at, is_delete,
                ROW_NUMBER() OVER (PARTITION BY content ORDER BY created_at DESC) as rn
            FROM wj_questions
            WHERE is_delete = 0
        ) AS t
        WHERE rn = 1
        <if test="content != null  and content != ''"> AND t.content LIKE CONCAT('%', #{content}, '%')</if>
        <if test="image != null  and image != ''"> AND t.image = #{image}</if>
        <if test="cueword != null  and cueword != ''"> AND t.cueword = #{cueword}</if>
        <if test="type != null  and type != ''"> AND t.type = #{type}</if>
        <if test="labelId != null "> AND t.label_id = #{labelId}</if>
        <if test="points != null "> AND t.points = #{points}</if>
        <if test="options != null  and options != ''"> AND t.options = #{options}</if>
        <if test="answer != null  and answer != ''"> AND t.answer = #{answer}</if>
        <if test="analysis != null  and analysis != ''"> AND t.analysis = #{analysis}</if>
        <if test="randomly != null "> AND t.randomly = #{randomly}</if>
        <if test="required != null "> AND t.required = #{required}</if>
        <if test="createdBy != null  and createdBy != ''"> AND t.created_by = #{createdBy}</if>
        <if test="createdAt != null "> AND t.created_at = #{createdAt}</if>
        <if test="updatedAt != null "> AND t.updated_at = #{updatedAt}</if>
        ORDER BY t.created_at DESC
    </select>
</mapper>