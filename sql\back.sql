-- MySQL dump 10.13  Distrib 8.0.35, for Win64 (x86_64)
--
-- Host: 127.0.0.1    Database: back
-- ------------------------------------------------------
-- Server version	8.0.35

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `assignment_answer_details`
--

DROP TABLE IF EXISTS `assignment_answer_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assignment_answer_details` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `score_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `question_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `score` double DEFAULT NULL,
  `is_correct` int DEFAULT NULL COMMENT '0是错误，1是正确，2是部分正确',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `assignment_answer_details_ibfk_1` (`score_id`),
  KEY `assignment_answer_details_ibfk_2` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assignment_answer_details`
--

LOCK TABLES `assignment_answer_details` WRITE;
/*!40000 ALTER TABLE `assignment_answer_details` DISABLE KEYS */;
INSERT INTO `assignment_answer_details` VALUES ('1924288263355412481','1924288238948757506','q001','A',0,1,'2025-05-19 02:17:28'),('1924288263422521345','1924288238948757506','q002','D',0,0,'2025-05-19 02:17:28');
/*!40000 ALTER TABLE `assignment_answer_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `assignment_questions`
--

DROP TABLE IF EXISTS `assignment_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assignment_questions` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `assignment_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `question_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `score` int NOT NULL,
  `order_num` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_delete` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `assignment_questions_ibfk_1` (`assignment_id`),
  KEY `assignment_questions_ibfk_2` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assignment_questions`
--

LOCK TABLES `assignment_questions` WRITE;
/*!40000 ALTER TABLE `assignment_questions` DISABLE KEYS */;
INSERT INTO `assignment_questions` VALUES ('1','1920757249790275586','q001',0,1,'2025-05-19 01:55:41',0),('2','1920757249790275586','q002',0,2,'2025-05-19 01:55:41',0),('3','1920754426193174529','q001',0,1,'2025-05-19 01:56:54',0),('4','1920754426193174529','q002',0,2,'2025-05-19 01:56:54',0),('5','1920352882318643202','q001',0,1,'2025-05-19 01:58:54',0),('6','1920352882318643202','q002',0,2,'2025-05-19 01:58:54',0),('7','1920352882318643202','q004',0,3,'2025-05-19 02:05:15',0);
/*!40000 ALTER TABLE `assignment_questions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `assignment_scores`
--

DROP TABLE IF EXISTS `assignment_scores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assignment_scores` (
  `score_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `assignment_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `student_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `total_score` double NOT NULL,
  `submit_time` timestamp NULL DEFAULT NULL,
  `time_spent` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '答题用时(分钟)',
  `status` enum('未开始','进行中','已完成') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `start_time` timestamp NOT NULL,
  PRIMARY KEY (`score_id`),
  KEY `assignment_scores_ibfk_1` (`assignment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assignment_scores`
--

LOCK TABLES `assignment_scores` WRITE;
/*!40000 ALTER TABLE `assignment_scores` DISABLE KEYS */;
INSERT INTO `assignment_scores` VALUES ('1924288238948757506','1920757249790275586','1',0,'2025-05-19 02:17:28','00:00:06','已完成',NULL,'2025-05-19 02:17:22','2025-05-19 02:17:22','2025-05-19 02:17:22');
/*!40000 ALTER TABLE `assignment_scores` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `assignments`
--

DROP TABLE IF EXISTS `assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `assignments` (
  `assignment_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `label_id` int NOT NULL,
  `total_score` int NOT NULL,
  `class_id` int DEFAULT NULL,
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` int NOT NULL DEFAULT '0' COMMENT '0存在1删除',
  PRIMARY KEY (`assignment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assignments`
--

LOCK TABLES `assignments` WRITE;
/*!40000 ALTER TABLE `assignments` DISABLE KEYS */;
INSERT INTO `assignments` VALUES ('1920352882318643202','Java基础作业-基础版','作业描述：Java基础作业-基础版',2,50,1,'admin','2025-05-08 05:39:40','2025-05-19 01:59:25',0),('1920754426193174529','Java基础作业-升级版','作业描述：Java基础作业-升级版',2,20,1,'admin','2025-05-09 08:15:15','2025-05-19 01:59:25',0),('1920757249790275586','Java基础作业-修改版','作业描述：Java基础作业-修改版',2,20,1,'admin','2025-05-09 08:26:28','2025-05-19 01:59:25',0);
/*!40000 ALTER TABLE `assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `classification`
--

DROP TABLE IF EXISTS `classification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `classification` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` int NOT NULL DEFAULT '0' COMMENT '0存在1删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `classification`
--

LOCK TABLES `classification` WRITE;
/*!40000 ALTER TABLE `classification` DISABLE KEYS */;
INSERT INTO `classification` VALUES ('1','Java基础','admin','2025-05-08 06:48:26','2025-05-08 06:48:28',0),('2','Java高级','admin','2025-05-08 07:00:18','2025-05-08 07:00:18',0),('3','Spring框架','admin','2025-05-08 07:00:18','2025-05-08 07:00:18',0),('4','前端开发','admin','2025-05-08 07:00:18','2025-05-08 07:00:18',0),('5','数据库','admin','2025-05-08 07:00:18','2025-05-08 07:00:18',0),('6','算法与数据结构','admin','2025-05-08 07:00:18','2025-05-08 07:00:18',0),('7','系统设计','admin','2025-05-08 07:00:18','2025-05-08 07:00:18',0),('8','面试题','admin','2025-05-08 07:00:18','2025-05-08 07:00:18',0);
/*!40000 ALTER TABLE `classification` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cs_manage`
--

DROP TABLE IF EXISTS `cs_manage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cs_manage` (
  `id` int NOT NULL AUTO_INCREMENT,
  `u_id` int DEFAULT NULL,
  `class_id` int NOT NULL,
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='班级学生关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cs_manage`
--

LOCK TABLES `cs_manage` WRITE;
/*!40000 ALTER TABLE `cs_manage` DISABLE KEYS */;
INSERT INTO `cs_manage` VALUES (1,1,1,'2025-05-07 10:49:25','2025-05-07 10:49:26'),(2,2,1,'2025-05-07 10:49:31','2025-05-07 10:49:32'),(3,3,2,'2025-05-09 17:07:41','2025-05-09 17:07:42');
/*!40000 ALTER TABLE `cs_manage` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exam_questions`
--

DROP TABLE IF EXISTS `exam_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exam_questions` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `exam_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `question_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `score` int NOT NULL,
  `order_num` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_delete` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exam_questions`
--

LOCK TABLES `exam_questions` WRITE;
/*!40000 ALTER TABLE `exam_questions` DISABLE KEYS */;
INSERT INTO `exam_questions` VALUES ('1','exam002','q001',5,1,'2025-04-22 06:44:33',0),('10','exam004','q006',10,4,'2025-04-24 06:24:19',0),('11','exam004','q007',10,5,'2025-04-24 06:24:22',0),('12','exam004','q008',10,6,'2025-04-24 06:24:23',0),('13','exam004','q010',10,7,'2025-04-24 06:24:24',0),('17','exam003','q006',10,1,'2025-04-25 03:06:10',0),('18','exam003','q007',10,2,'2025-04-25 03:06:11',0),('19','exam003','q008',10,3,'2025-04-25 03:06:11',0),('2','exam002','q002',5,2,'2025-04-22 10:28:59',0),('20','exam005','q001',10,1,'2025-04-25 03:22:18',0),('21','exam005','q002',10,2,'2025-04-25 03:22:19',0),('4','exam001','q001',10,1,'2025-04-22 06:44:33',0),('5','exam001','q002',10,2,'2025-04-22 10:28:59',0),('7','exam004','q001',10,1,'2025-04-24 06:24:17',0),('8','exam004','q002',10,2,'2025-04-24 06:24:18',0),('9','exam001','q004',10,3,'2025-04-24 06:24:18',0);
/*!40000 ALTER TABLE `exam_questions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exam_reminders`
--

DROP TABLE IF EXISTS `exam_reminders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exam_reminders` (
  `reminder_id` varchar(32) NOT NULL COMMENT '提醒ID',
  `exam_id` varchar(32) NOT NULL COMMENT '考试ID',
  `student_id` varchar(32) NOT NULL COMMENT '学生ID',
  `minutes_before` int NOT NULL COMMENT '提前多少分钟提醒',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`reminder_id`),
  KEY `idx_student_exam` (`student_id`,`exam_id`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='考试提醒表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exam_reminders`
--

LOCK TABLES `exam_reminders` WRITE;
/*!40000 ALTER TABLE `exam_reminders` DISABLE KEYS */;
INSERT INTO `exam_reminders` VALUES ('1914921105747877889','exam003','student001',3,1,'2025-04-23 13:55:44','2025-04-28 17:16:48',0);
/*!40000 ALTER TABLE `exam_reminders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `exams`
--

DROP TABLE IF EXISTS `exams`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `exams` (
  `exam_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `start_time` datetime NOT NULL,
  `label_id` int NOT NULL,
  `end_time` datetime NOT NULL,
  `duration` int NOT NULL COMMENT '考试时长(分钟)',
  `passing_mark` int NOT NULL,
  `status` enum('草稿状态','未开始','进行中','已结束') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `total_score` int NOT NULL,
  `class_id` int DEFAULT NULL,
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` int NOT NULL DEFAULT '0' COMMENT '0存在1删除',
  PRIMARY KEY (`exam_id`),
  KEY `created_by` (`created_by`),
  KEY `idx_exams_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `exams`
--

LOCK TABLES `exams` WRITE;
/*!40000 ALTER TABLE `exams` DISABLE KEYS */;
INSERT INTO `exams` VALUES ('exam001','Java基础测评','测试学生对Java基础知识的掌握程度','2025-05-01 09:00:00',1,'2025-05-31 11:00:00',120,15,'进行中',30,1,'admin','2025-04-22 05:13:14','2025-05-19 02:10:05',0),('exam002','Java进阶测评','测试学生对Java高级特性的掌握程度','2025-05-01 09:00:00',1,'2025-05-31 11:00:00',120,7,'进行中',15,1,'admin','2025-04-22 05:13:14','2025-05-19 02:09:51',0),('exam003','C++进阶测评','测试学生对C++高级特性的掌握程度','2025-05-01 09:00:00',1,'2025-05-31 11:00:00',120,1,'进行中',30,1,'admin','2025-04-22 05:13:14','2025-05-19 02:07:58',0),('exam004','Python基础测评','测试学生对Python高级特性的掌握程度','2025-05-01 09:00:00',1,'2025-05-31 11:00:00',120,35,'进行中',70,1,'admin','2025-04-22 05:13:14','2025-05-19 02:07:58',0),('exam005','Python进阶测评','测试学生对Python高级特性的掌握程度','2025-05-01 09:00:00',1,'2025-05-31 11:00:00',120,15,'进行中',30,1,'admin','2025-04-22 05:13:14','2025-05-19 02:09:51',0);
/*!40000 ALTER TABLE `exams` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gen_table`
--

DROP TABLE IF EXISTS `gen_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gen_table` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='代码生成业务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gen_table`
--

LOCK TABLES `gen_table` WRITE;
/*!40000 ALTER TABLE `gen_table` DISABLE KEYS */;
INSERT INTO `gen_table` VALUES (1,'exams','考试表','exam_questions','exam_id','Exams','sub','element-plus','com.ruoyi.system','aqsystem','exams','考试管理','ruoyi','0','/','{\"parentMenuId\":2000}','admin','2025-04-29 14:35:52','','2025-04-29 14:59:48',NULL),(2,'wj_questions','问卷题库表',NULL,NULL,'WjQuestions','crud','element-plus','com.ruoyi.system','aqsystem','questions','问卷题库','ruoyi','0','/','{\"parentMenuId\":2000}','admin','2025-04-29 14:36:05','','2025-04-29 17:17:27',NULL),(3,'questionnaires','问卷主表','questionnaire_questions','questionnaire_id','Questionnaires','sub','element-plus','com.ruoyi.system','aqsystem','questionnaires','问卷管理','ruoyi','0','/','{\"parentMenuId\":2000}','admin','2025-04-29 14:36:18','','2025-04-29 17:08:16',NULL),(4,'questions','考试作业题库',NULL,NULL,'Questions','crud','element-plus','com.ruoyi.system','aqsystem','questions','题库管理','ruoyi','0','/','{\"parentMenuId\":2000}','admin','2025-04-29 14:36:47','','2025-04-29 17:16:23',NULL),(5,'questionnaire_questions','问卷题目关联表',NULL,NULL,'QuestionnaireQuestions','crud','','com.ruoyi.system','system','questions','问卷题目关联','ruoyi','0','/',NULL,'admin','2025-04-29 14:40:04','',NULL,NULL),(6,'exam_questions','',NULL,NULL,'ExamQuestions','crud','','com.ruoyi.system','system','questions',NULL,'ruoyi','0','/',NULL,'admin','2025-04-29 14:40:17','',NULL,NULL),(7,'scores','',NULL,NULL,'Scores','crud','','com.ruoyi.system','system','scores',NULL,'ruoyi','0','/',NULL,'admin','2025-04-29 14:40:21','',NULL,NULL),(8,'questionnaire_scores','',NULL,NULL,'QuestionnaireScores','crud','','com.ruoyi.system','system','scores',NULL,'ruoyi','0','/',NULL,'admin','2025-04-29 14:40:36','',NULL,NULL),(9,'student_class','班级',NULL,NULL,'StudentClass','crud','element-plus','com.ruoyi.system','system','class','班级','ruoyi','0','/','{}','admin','2025-05-06 13:24:26','','2025-05-06 13:25:04',NULL),(11,'users','学生表',NULL,NULL,'Users','crud','element-plus','com.ruoyi.system','system','users','学生管理','ruoyi','0','/','{\"parentMenuId\":2000}','admin','2025-05-06 15:17:08','','2025-05-06 15:18:50',NULL),(12,'supervisor','导师信息表，用于记录导师的基本资料',NULL,NULL,'Supervisor','crud','element-plus','com.ruoyi.system','system','supervisor','导师信息，用于记录导师的基本资料','ruoyi','0','/','{}','admin','2025-05-20 11:45:15','','2025-05-20 13:05:16',NULL),(13,'topic','选题信息表',NULL,NULL,'Topic','crud','element-plus','com.ruoyi.system','system','topic','选题信息','ruoyi','0','/','{\"parentMenuId\":0}','admin','2025-05-20 11:45:15','','2025-05-20 13:37:29',NULL),(14,'topic_application','选题报名表',NULL,NULL,'TopicApplication','crud','','com.ruoyi.system','system','application','选题报名','ruoyi','0','/',NULL,'admin','2025-05-20 11:45:15','',NULL,NULL),(15,'topic_category','选题类别表',NULL,NULL,'TopicCategory','crud','element-plus','com.ruoyi.system','system','category','选题类别','ruoyi','0','/','{}','admin','2025-05-20 11:45:15','','2025-05-20 13:05:38',NULL),(16,'topic_selection','学生选题记录表',NULL,NULL,'TopicSelection','crud','','com.ruoyi.system','system','selection','学生选题记录','ruoyi','0','/',NULL,'admin','2025-05-20 11:45:15','',NULL,NULL),(17,'resume_skill','技能表',NULL,NULL,'ResumeSkill','crud','element-plus','com.ruoyi.system','system','skill','技能','ruoyi','0','/','{}','admin','2025-05-20 18:52:04','','2025-05-20 19:05:43',NULL),(18,'resume_skill_segment','技能段落表',NULL,NULL,'ResumeSkillSegment','crud','','com.ruoyi.system','system','segment','技能段落','ruoyi','0','/',NULL,'admin','2025-05-20 18:52:04','',NULL,NULL),(19,'resume_project','项目表',NULL,NULL,'ResumeProject','crud','element-plus','com.ruoyi.system','system','project','项目','ruoyi','0','/','{}','admin','2025-05-20 22:24:11','','2025-05-20 22:25:21',NULL),(20,'resume_project_content','项目内容表',NULL,NULL,'ResumeProjectContent','crud','','com.ruoyi.system','system','content','项目内容','ruoyi','0','/',NULL,'admin','2025-05-20 22:24:11','',NULL,NULL),(21,'resume_project_question','项目问题表',NULL,NULL,'ResumeProjectQuestion','crud','','com.ruoyi.system','system','question','项目问题','ruoyi','0','/',NULL,'admin','2025-05-20 22:24:11','',NULL,NULL),(22,'resume','简历主表',NULL,NULL,'Resume','crud','element-plus','com.ruoyi.system','system','resume','简历主','ruoyi','0','/','{}','admin','2025-05-21 19:16:20','','2025-05-21 19:19:32',NULL);
/*!40000 ALTER TABLE `gen_table` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gen_table_column`
--

DROP TABLE IF EXISTS `gen_table_column`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gen_table_column` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`)
) ENGINE=InnoDB AUTO_INCREMENT=212 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='代码生成业务表字段';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gen_table_column`
--

LOCK TABLES `gen_table_column` WRITE;
/*!40000 ALTER TABLE `gen_table_column` DISABLE KEYS */;
INSERT INTO `gen_table_column` VALUES (1,1,'exam_id',NULL,'varchar(50)','String','examId','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(2,1,'title','考试名称','varchar(100)','String','title','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(3,1,'description',NULL,'text','String','description','0','0','0','1','1','1','1','EQ','textarea','',3,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(4,1,'start_time','开始时间','datetime','Date','startTime','0','0','1','1','1','1','1','EQ','datetime','',4,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(5,1,'label_id',NULL,'int','Long','labelId','0','0','1','1','1','1','1','EQ','input','',5,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(6,1,'end_time','结束时间','datetime','Date','endTime','0','0','1','1','1','1','1','EQ','datetime','',6,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(7,1,'duration','考试时长(分钟)','int','Long','duration','0','0','1','1','1','1','1','EQ','input','',7,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(8,1,'passing_mark',NULL,'int','Long','passingMark','0','0','1','1','1','1','1','EQ','input','',8,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(9,1,'status','考试状态','enum(\'草稿状态\',\'未开始\',\'进行中\',\'已结束\')','String','status','0','0','1','1','1','1','1','EQ','radio','',9,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(10,1,'total_score','总分','int','Long','totalScore','0','0','1','1','1','1','1','EQ','input','',10,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(11,1,'allowed_classes','对应班级','json','String','allowedClasses','0','0','0','1','1','1','1','EQ',NULL,'',11,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(12,1,'created_by','创建人','varchar(50)','String','createdBy','0','0','1','1','1','1','1','EQ','input','',12,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(13,1,'created_at','创建时间','timestamp','Date','createdAt','0','0','0','1','1','1','1','EQ','datetime','',13,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(14,1,'updated_at','修改时间','timestamp','Date','updatedAt','0','0','0','1','1','1','1','EQ','datetime','',14,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(15,1,'is_delete','0存在1删除','int','Long','isDelete','0','0','0','1','1','1','1','EQ','input','',15,'admin','2025-04-29 14:35:52','','2025-04-29 14:59:48'),(16,2,'questions_id','问题ID','int','Long','questionsId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(17,2,'content','问题内容','text','String','content','0','0','1','1','1','1','1','EQ','editor','',2,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(18,2,'image',NULL,'text','String','image','0','0','0','1','1','1','1','EQ','imageUpload','',3,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(19,2,'cueword','问题提示词','varchar(100)','String','cueword','0','0','0','1','1','1','1','EQ','input','',4,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(20,2,'type','问题类型','enum(\'单选题\',\'多选题\',\'填空题\',\'简答题\',\'排序题\')','String','type','0','0','1','1','1','1','1','EQ','select','',5,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(21,2,'label_id','所属类型','int','Long','labelId','0','0','1','1','1','1','1','EQ','input','',6,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(22,2,'points','分值','int','Long','points','0','0','1','1','1','1','1','EQ','input','',7,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(23,2,'options','选项(JSON格式)','json','String','options','0','0','0','1','1','1','1','EQ',NULL,'',8,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(24,2,'answer','参考答案','text','String','answer','0','0','0','1','1','1','1','EQ','textarea','',9,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(25,2,'analysis','解析','text','String','analysis','0','0','0','1','1','1','1','EQ','textarea','',10,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(26,2,'randomly','是否随机选项','tinyint(1)','Integer','randomly','0','0','0','1','1','1','1','EQ','input','',11,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(27,2,'required','是否必答','tinyint(1)','Integer','required','0','0','1','1','1','1','1','EQ','input','',12,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(28,2,'created_by','创建人ID','varchar(50)','String','createdBy','0','0','1','1','1','1','1','EQ','input','',13,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(29,2,'created_at','创建时间','timestamp','Date','createdAt','0','0','0','1','1','1','1','EQ','datetime','',14,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(30,2,'updated_at','更新时间','timestamp','Date','updatedAt','0','0','0','1','1','1','1','EQ','datetime','',15,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(31,2,'is_delete','0存在，1删除','int','Long','isDelete','0','0','1','1','1','1','1','EQ','input','',16,'admin','2025-04-29 14:36:05','','2025-04-29 17:17:27'),(32,3,'questionnaire_id','问卷ID','varchar(50)','String','questionnaireId','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(33,3,'title','问卷标题','varchar(100)','String','title','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(34,3,'lable_id',NULL,'int','Long','lableId','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(35,3,'description','问卷描述','text','String','description','0','0','0','1','1','1','1','EQ','textarea','',4,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(36,3,'class_id',NULL,'varchar(20)','String','classId','0','0','1','1','1','1','1','EQ','input','',5,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(37,3,'start_time','开始时间','datetime','Date','startTime','0','0','1','1','1','1','1','EQ','datetime','',6,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(38,3,'end_time','结束时间','datetime','Date','endTime','0','0','1','1','1','1','1','EQ','datetime','',7,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(39,3,'duration','填写时长(分钟)','int','Long','duration','0','0','1','1','1','1','1','EQ','input','',8,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(40,3,'status','问卷状态','enum(\'草稿状态\',\'未开始\',\'进行中\',\'已结束\')','String','status','0','0','1','1','1','1','1','EQ','radio','',9,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(41,3,'created_by','创建人ID','varchar(50)','String','createdBy','0','0','1','1','1','1','1','EQ','input','',10,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(42,3,'created_at','创建时间','timestamp','Date','createdAt','0','0','0','1','1','1','1','EQ','datetime','',11,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(43,3,'updated_at','更新时间','timestamp','Date','updatedAt','0','0','0','1','1','1','1','EQ','datetime','',12,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(44,3,'is_delete','0存在1删除','int','Long','isDelete','0','0','0','1','1','1','1','EQ','input','',13,'admin','2025-04-29 14:36:18','','2025-04-29 17:08:16'),(45,4,'question_id',NULL,'varchar(50)','String','questionId','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(46,4,'content',NULL,'text','String','content','0','0','1','1','1','1','1','EQ','editor','',2,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(47,4,'type',NULL,'enum(\'single\',\'不定项选择题\',\'单选题\',\'多选题\',\'填空题\',\'简答题\')','String','type','0','0','1','1','1','1','1','EQ','select','',3,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(48,4,'subject',NULL,'varchar(50)','String','subject','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(49,4,'difficulty',NULL,'enum(\'easy\',\'medium\',\'hard\')','String','difficulty','0','0','1','1','1','1','1','EQ',NULL,'',5,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(50,4,'label',NULL,'varchar(50)','String','label','0','0','0','1','1','1','1','EQ','input','',6,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(51,4,'points',NULL,'int','Long','points','0','0','1','1','1','1','1','EQ','input','',7,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(52,4,'options',NULL,'json','String','options','0','0','0','1','1','1','1','EQ',NULL,'',8,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(53,4,'answer',NULL,'text','String','answer','0','0','1','1','1','1','1','EQ','textarea','',9,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(54,4,'analysis',NULL,'text','String','analysis','0','0','0','1','1','1','1','EQ','textarea','',10,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(55,4,'answering_time',NULL,'int','Long','answeringTime','0','0','0','1','1','1','1','EQ','input','',11,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(56,4,'topic_classification',NULL,'varchar(50)','String','topicClassification','0','0','0','1','1','1','1','EQ','input','',12,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(57,4,'randomly',NULL,'tinyint(1)','Integer','randomly','0','0','0','1','1','1','1','EQ','input','',13,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(58,4,'created_by',NULL,'varchar(50)','String','createdBy','0','0','1','1','1','1','1','EQ','input','',14,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(59,4,'created_at',NULL,'timestamp','Date','createdAt','0','0','0','1','1','1','1','EQ','datetime','',15,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(60,4,'updated_at',NULL,'timestamp','Date','updatedAt','0','0','0','1','1','1','1','EQ','datetime','',16,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(61,4,'is_delete','0存在，1删除','int','Long','isDelete','0','0','1','1','1','1','1','EQ','input','',17,'admin','2025-04-29 14:36:47','','2025-04-29 17:16:23'),(62,5,'id','关联ID','varchar(50)','String','id','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-04-29 14:40:04','',NULL),(63,5,'questionnaire_id','问卷ID','varchar(50)','String','questionnaireId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-04-29 14:40:04','',NULL),(64,5,'questions_id','题目ID','varchar(50)','String','questionsId','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-04-29 14:40:04','',NULL),(65,5,'order_num','题目顺序','int','Long','orderNum','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-04-29 14:40:04','',NULL),(66,5,'created_at','创建时间','timestamp','Date','createdAt','0','0','0','1','1','1','1','EQ','datetime','',5,'admin','2025-04-29 14:40:04','',NULL),(67,5,'is_delete','0存在1删除','int','Long','isDelete','0','0','1','1','1','1','1','EQ','input','',6,'admin','2025-04-29 14:40:04','',NULL),(68,6,'id',NULL,'varchar(50)','String','id','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-04-29 14:40:17','',NULL),(69,6,'exam_id',NULL,'varchar(50)','String','examId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-04-29 14:40:17','',NULL),(70,6,'question_id',NULL,'varchar(50)','String','questionId','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-04-29 14:40:17','',NULL),(71,6,'score',NULL,'int','Long','score','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-04-29 14:40:17','',NULL),(72,6,'order_num',NULL,'int','Long','orderNum','0','0','1','1','1','1','1','EQ','input','',5,'admin','2025-04-29 14:40:17','',NULL),(73,6,'created_at',NULL,'timestamp','Date','createdAt','0','0','0','1','1','1','1','EQ','datetime','',6,'admin','2025-04-29 14:40:17','',NULL),(74,6,'is_delete',NULL,'int','Long','isDelete','0','0','1','1','1','1','1','EQ','input','',7,'admin','2025-04-29 14:40:17','',NULL),(75,7,'score_id',NULL,'varchar(50)','String','scoreId','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-04-29 14:40:21','',NULL),(76,7,'exam_id',NULL,'varchar(50)','String','examId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-04-29 14:40:21','',NULL),(77,7,'student_id',NULL,'varchar(50)','String','studentId','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-04-29 14:40:21','',NULL),(78,7,'total_score',NULL,'double','Long','totalScore','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-04-29 14:40:21','',NULL),(79,7,'submit_time',NULL,'timestamp','Date','submitTime','0','0','0','1','1','1','1','EQ','datetime','',5,'admin','2025-04-29 14:40:21','',NULL),(80,7,'time_spent','答题用时(分钟)','varchar(20)','String','timeSpent','0','0','0','1','1','1','1','EQ','input','',6,'admin','2025-04-29 14:40:21','',NULL),(81,7,'status',NULL,'enum(\'考试中\',\'待批改\',\'已完成\')','String','status','0','0','1','1','1','1','1','EQ','radio','',7,'admin','2025-04-29 14:40:21','',NULL),(82,7,'comment',NULL,'text','String','comment','0','0','0','1','1','1','1','EQ','textarea','',8,'admin','2025-04-29 14:40:21','',NULL),(83,7,'created_at',NULL,'timestamp','Date','createdAt','0','0','0','1','1','1','1','EQ','datetime','',9,'admin','2025-04-29 14:40:21','',NULL),(84,7,'updated_at',NULL,'timestamp','Date','updatedAt','0','0','0','1','1','1','1','EQ','datetime','',10,'admin','2025-04-29 14:40:21','',NULL),(85,7,'start_time',NULL,'timestamp','Date','startTime','0','0','1','1','1','1','1','EQ','datetime','',11,'admin','2025-04-29 14:40:21','',NULL),(86,8,'score_id',NULL,'varchar(50)','String','scoreId','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-04-29 14:40:36','',NULL),(87,8,'questionnaire_id',NULL,'varchar(50)','String','questionnaireId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-04-29 14:40:36','',NULL),(88,8,'student_id',NULL,'varchar(50)','String','studentId','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-04-29 14:40:36','',NULL),(89,8,'submit_time',NULL,'timestamp','Date','submitTime','0','0','0','1','1','1','1','EQ','datetime','',4,'admin','2025-04-29 14:40:36','',NULL),(90,8,'time_spent','填写用时(分钟)','varchar(20)','String','timeSpent','0','0','0','1','1','1','1','EQ','input','',5,'admin','2025-04-29 14:40:36','',NULL),(91,8,'status',NULL,'enum(\'进行中\',\'已完成\')','String','status','0','0','1','1','1','1','1','EQ','radio','',6,'admin','2025-04-29 14:40:36','',NULL),(92,8,'comment',NULL,'text','String','comment','0','0','0','1','1','1','1','EQ','textarea','',7,'admin','2025-04-29 14:40:36','',NULL),(93,8,'created_at',NULL,'timestamp','Date','createdAt','0','0','0','1','1','1','1','EQ','datetime','',8,'admin','2025-04-29 14:40:36','',NULL),(94,8,'updated_at',NULL,'timestamp','Date','updatedAt','0','0','0','1','1','1','1','EQ','datetime','',9,'admin','2025-04-29 14:40:36','',NULL),(95,8,'start_time',NULL,'timestamp','Date','startTime','0','0','1','1','1','1','1','EQ','datetime','',10,'admin','2025-04-29 14:40:36','',NULL),(96,9,'class_id',NULL,'int','Long','classId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-06 13:24:26','','2025-05-06 13:25:04'),(97,9,'class_name',NULL,'varchar(50)','String','className','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-05-06 13:24:26','','2025-05-06 13:25:04'),(98,9,'created_by',NULL,'varchar(30)','String','createdBy','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-05-06 13:24:26','','2025-05-06 13:25:04'),(99,9,'is_delete',NULL,'int','Long','isDelete','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-05-06 13:24:26','','2025-05-06 13:25:04'),(113,11,'user_id','学号','varchar(50)','String','userId','1','0','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(114,11,'username','姓名','varchar(50)','String','username','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(115,11,'password','密码','varchar(255)','String','password','0','0','1','1','1','0','0','EQ','input','',3,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(116,11,'email','邮箱','varchar(100)','String','email','0','0','0','1','1','1','1','EQ','input','',4,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(117,11,'phone','手机号','varchar(20)','String','phone','0','0','0','1','1','1','1','EQ','input','',5,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(118,11,'class_id','班级id','int','Long','classId','0','0','1','1','1','1','1','EQ','input','',6,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(119,11,'avatar','头像','varchar(255)','String','avatar','0','0','0','1','1','1','1','EQ','input','',7,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(120,11,'role_id',NULL,'varchar(20)','String','roleId','0','0','1','0','0','0','0','EQ','input','',8,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(121,11,'status',NULL,'enum(\'active\',\'inactive\')','String','status','0','0','0','1','1','1','1','EQ','radio','',9,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(122,11,'last_login',NULL,'timestamp','Date','lastLogin','0','0','0','1','1','0','1','EQ','datetime','',10,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(123,11,'created_at',NULL,'timestamp','Date','createdAt','0','0','0','1','1','0','1','EQ','datetime','',11,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(124,11,'updated_at',NULL,'timestamp','Date','updatedAt','0','0','0','1','1','0','1','EQ','datetime','',12,'admin','2025-05-06 15:17:08','','2025-05-06 15:18:50'),(125,12,'supervisor_id','导师ID，主键，自增长','int','Long','supervisorId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(126,12,'supervisor_name','导师姓名','varchar(100)','String','supervisorName','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(127,12,'department','所属院系','varchar(100)','String','department','0','0','1','1','1','1','1','EQ','select','',3,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(128,12,'title','职称，如教授、副教授等','varchar(50)','String','title','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(129,12,'email','导师邮箱，具有唯一性','varchar(100)','String','email','0','0','1','1','1','1','1','EQ','input','',5,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(130,12,'phone_number','电话号码，可为空','varchar(20)','String','phoneNumber','0','0','0','1','1','1','1','EQ','input','',6,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(131,12,'create_time',NULL,'datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',7,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(132,12,'edit_time',NULL,'datetime','Date','editTime','0','0','0','1','1','1','1','EQ','datetime','',8,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(133,12,'is_delete',NULL,'int','Long','isDelete','0','0','0','1','1','0','1','EQ','input','',9,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:16'),(134,13,'topic_id','','int','Long','topicId','1','1','0','0',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(135,13,'title','题目','varchar(200)','String','title','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(136,13,'description','描述','text','String','description','0','0','0','1','1','1','0','EQ','textarea','',3,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(137,13,'supervisor_id','导师','int','Long','supervisorId','0','0','1','1','1','1','0','EQ','select','',4,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(138,13,'category_id','类别','int','Long','categoryId','0','0','1','1','1','1','0','EQ','select','',5,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(139,13,'tag','标签','varchar(100)','String','tag','0','0','1','1','1','1','1','LIKE','input','',6,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(140,13,'publish_date','','datetime','Date','publishDate','0','0','1','0','0','0','0','EQ','datetime','',7,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(141,13,'deadline','截止时间','datetime','Date','deadline','0','0','1','0','0','0','0','EQ','datetime','',8,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(142,13,'create_time',NULL,'datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',9,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(143,13,'edit_time','修改时间','datetime','Date','editTime','0','0','0','1','1','1','0','EQ','datetime','',10,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(144,13,'is_delete',NULL,'int','Long','isDelete','0','0','0','1','1','0','0','EQ','input','',11,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(145,13,'status','课题状态','int','Long','status','0','0','0','1','1','1','1','EQ','select','',12,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(146,13,'quota','','int','Long','quota','0','0','0','0','0','0','0','EQ','input','',13,'admin','2025-05-20 11:45:15','','2025-05-20 13:37:29'),(147,14,'application_id','报名ID，主键，自增','int','Long','applicationId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 11:45:15','',NULL),(148,14,'u_id','学生ID（关联学生表）','bigint','Long','uId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-05-20 11:45:15','',NULL),(149,14,'topic_id','选题ID，关联选题表','int','Long','topicId','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-05-20 11:45:15','',NULL),(150,14,'application_date','报名时间','datetime','Date','applicationDate','0','0','1','1','1','1','1','EQ','datetime','',4,'admin','2025-05-20 11:45:15','',NULL),(151,14,'create_time','创建时间','datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',5,'admin','2025-05-20 11:45:15','',NULL),(152,14,'edit_time','修改时间','datetime','Date','editTime','0','0','0','1','1','1','1','EQ','datetime','',6,'admin','2025-05-20 11:45:15','',NULL),(153,14,'is_delete','删除标识','int','Long','isDelete','0','0','0','1','1','1','1','EQ','input','',7,'admin','2025-05-20 11:45:15','',NULL),(154,14,'status','审核状态 0：待审核 1：通过 2：未通过','int','Long','status','0','0','0','1','1','1','1','EQ','radio','',8,'admin','2025-05-20 11:45:15','',NULL),(155,15,'category_id','选题类别ID，主键，自增','int','Long','categoryId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:38'),(156,15,'category_name','类别名称，如人工智能、大数据、前端开发等','varchar(100)','String','categoryName','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:38'),(157,15,'create_time',NULL,'datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',3,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:38'),(158,15,'edit_time',NULL,'datetime','Date','editTime','0','0','0','1','1','1','1','EQ','datetime','',4,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:38'),(159,15,'is_delete',NULL,'int','Long','isDelete','0','0','0','1','1','0','1','EQ','input','',5,'admin','2025-05-20 11:45:15','','2025-05-20 13:05:38'),(160,16,'selection_id','选择记录ID，主键，自增','int','Long','selectionId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 11:45:15','',NULL),(161,16,'u_id','学生ID（假设关联学生表）','bigint','Long','uId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-05-20 11:45:15','',NULL),(162,16,'topic_id','选题ID，关联选题表','int','Long','topicId','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-05-20 11:45:15','',NULL),(163,16,'selection_date','选择时间','datetime','Date','selectionDate','0','0','1','1','1','1','1','EQ','datetime','',4,'admin','2025-05-20 11:45:15','',NULL),(164,16,'status',NULL,'int','Long','status','0','0','0','1','1','1','1','EQ','radio','',5,'admin','2025-05-20 11:45:15','',NULL),(165,16,'create_time',NULL,'datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',6,'admin','2025-05-20 11:45:15','',NULL),(166,16,'edit_time',NULL,'datetime','Date','editTime','0','0','0','1','1','1','1','EQ','datetime','',7,'admin','2025-05-20 11:45:15','',NULL),(167,16,'is_delete',NULL,'int','Long','isDelete','0','0','0','1','1','1','1','EQ','input','',8,'admin','2025-05-20 11:45:15','',NULL),(168,17,'ski_id',NULL,'bigint','Long','skiId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 18:52:04','','2025-05-20 19:05:43'),(169,17,'name','技能名称','varchar(50)','String','name','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-05-20 18:52:04','','2025-05-20 19:05:43'),(170,17,'proficiency','熟练度（一般、良好、熟练、擅长、精通）','varchar(20)','String','proficiency','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-05-20 18:52:04','','2025-05-20 19:05:43'),(171,17,'description','技能描述（生成话语）','text','String','description','0','0','1','1','1','1','1','EQ','textarea','',4,'admin','2025-05-20 18:52:04','','2025-05-20 19:05:43'),(172,17,'create_time','创建时间','datetime','Date','createTime','0','0','1','1',NULL,NULL,NULL,'EQ','datetime','',5,'admin','2025-05-20 18:52:04','','2025-05-20 19:05:43'),(173,17,'update_time','更新时间','datetime','Date','updateTime','0','0','1','1','1',NULL,NULL,'EQ','datetime','',6,'admin','2025-05-20 18:52:04','','2025-05-20 19:05:43'),(174,18,'seg_id',NULL,'bigint','Long','segId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 18:52:04','',NULL),(175,18,'skill_id','技能ID','bigint','Long','skillId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-05-20 18:52:04','',NULL),(176,18,'text','段落文本','text','String','text','0','0','1','1','1','1','1','EQ','textarea','',3,'admin','2025-05-20 18:52:04','',NULL),(177,18,'is_bold','是否加粗：0-否，1-是','tinyint(1)','Integer','isBold','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-05-20 18:52:04','',NULL),(178,18,'segment_order','段落顺序','int','Long','segmentOrder','0','0','1','1','1','1','1','EQ','input','',5,'admin','2025-05-20 18:52:04','',NULL),(179,18,'create_time','创建时间','datetime','Date','createTime','0','0','1','1',NULL,NULL,NULL,'EQ','datetime','',6,'admin','2025-05-20 18:52:04','',NULL),(180,18,'update_time','更新时间','datetime','Date','updateTime','0','0','1','1','1',NULL,NULL,'EQ','datetime','',7,'admin','2025-05-20 18:52:04','',NULL),(181,19,'pro_id',NULL,'bigint','Long','proId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(182,19,'name','项目名称','varchar(100)','String','name','0','0','1','1','1','1','1','LIKE','input','',2,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(183,19,'start_time','开始时间','date','Date','startTime','0','0','1','1','1','1','0','EQ','datetime','',3,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(184,19,'end_time','结束时间','date','Date','endTime','0','0','1','1','1','1','0','EQ','datetime','',4,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(185,19,'cat_id','职位类别id','bigint','Long','catId','0','0','1','1','1','1','0','EQ','input','',5,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(186,19,'content','项目内容（字符串形式，用于兼容）','text','String','content','0','0','1','1','1','1','0','EQ','editor','',6,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(187,19,'create_at',NULL,'varchar(20)','String','createAt','0','0','0','1','1','1','0','EQ','input','',7,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(188,19,'create_time','创建时间','datetime','Date','createTime','0','0','1','1',NULL,NULL,NULL,'EQ','datetime','',8,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(189,19,'update_time','更新时间','datetime','Date','updateTime','0','0','1','1','0',NULL,NULL,'EQ','datetime','',9,'admin','2025-05-20 22:24:11','','2025-05-20 22:25:21'),(190,20,'con_id',NULL,'bigint','Long','conId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 22:24:11','',NULL),(191,20,'project_id','项目ID','bigint','Long','projectId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-05-20 22:24:11','',NULL),(192,20,'text','内容文本','text','String','text','0','0','1','1','1','1','1','EQ','textarea','',3,'admin','2025-05-20 22:24:11','',NULL),(193,20,'content_order','内容顺序','int','Long','contentOrder','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-05-20 22:24:11','',NULL),(194,20,'create_time','创建时间','datetime','Date','createTime','0','0','1','1',NULL,NULL,NULL,'EQ','datetime','',5,'admin','2025-05-20 22:24:11','',NULL),(195,20,'update_time','更新时间','datetime','Date','updateTime','0','0','1','1','1',NULL,NULL,'EQ','datetime','',6,'admin','2025-05-20 22:24:11','',NULL),(196,21,'que_id',NULL,'bigint','Long','queId','1','1','0','1',NULL,NULL,NULL,'EQ','input','',1,'admin','2025-05-20 22:24:11','',NULL),(197,21,'project_id','项目ID','bigint','Long','projectId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-05-20 22:24:11','',NULL),(198,21,'question','问题内容','text','String','question','0','0','1','1','1','1','1','EQ','textarea','',3,'admin','2025-05-20 22:24:11','',NULL),(199,21,'question_order','问题顺序','int','Long','questionOrder','0','0','1','1','1','1','1','EQ','input','',4,'admin','2025-05-20 22:24:11','',NULL),(200,21,'create_time','创建时间','datetime','Date','createTime','0','0','1','1',NULL,NULL,NULL,'EQ','datetime','',5,'admin','2025-05-20 22:24:11','',NULL),(201,21,'update_time','更新时间','datetime','Date','updateTime','0','0','1','1','1',NULL,NULL,'EQ','datetime','',6,'admin','2025-05-20 22:24:11','',NULL),(202,22,'resume_id','简历ID','bigint','Long','resumeId','1','1','0','1',NULL,'1',NULL,'EQ','input','',1,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(203,22,'user_id','用户ID','bigint','Long','userId','0','0','1','1','1','1','1','EQ','input','',2,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(204,22,'title','简历标题','varchar(100)','String','title','0','0','1','1','1','1','1','EQ','input','',3,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(205,22,'status','简历状态(0-草稿,1-已保存)','tinyint','Long','status','0','0','0','1','1','1','0','EQ','radio','',4,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(206,22,'is_default','认是否默简历(0-否,1-是)','tinyint','Long','isDefault','0','0','0','1','1','1','0','EQ','input','',5,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(207,22,'template_id','简历模板ID','bigint','Long','templateId','0','0','0','1','1','1','0','EQ','input','',6,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(208,22,'create_time','创建时间','datetime','Date','createTime','0','0','0','1',NULL,NULL,NULL,'EQ','datetime','',7,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(209,22,'update_time','修改时间','datetime','Date','updateTime','0','0','0','1','1',NULL,NULL,'EQ','datetime','',8,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(210,22,'create_at','创建人','varchar(50)','String','createAt','0','0','0','1','1','1','1','EQ','input','',9,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32'),(211,22,'is_delete','删除标识(0-未删除,1-已删除)','tinyint','Long','isDelete','0','0','0','1','1','1','1','EQ','input','',10,'admin','2025-05-21 19:16:20','','2025-05-21 19:19:32');
/*!40000 ALTER TABLE `gen_table_column` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ks_user`
--

DROP TABLE IF EXISTS `ks_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ks_user` (
  `u_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '昵称',
  `passwd` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `pass_type` int DEFAULT '0' COMMENT '密码类型',
  `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '规则输入内容',
  `state` int DEFAULT '0' COMMENT '用户状态，0表示正常',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `lasr_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `memo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  `user_level` tinyint DEFAULT NULL COMMENT '用户级别 1:超级管理员 0:普通会员',
  `foreign_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `direction_type_id` bigint DEFAULT NULL COMMENT '体系id',
  `is_check` int DEFAULT NULL COMMENT '是否需要检查权限1：不需要 0：需要(如果已经线下报名，免费学习所有课程)',
  `school_type` int DEFAULT NULL COMMENT '学校type',
  `school_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '学校名称',
  `user_type` tinyint DEFAULT '0' COMMENT '0免费散客，22实训客户，66付费用户，99内部员工',
  PRIMARY KEY (`u_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ks_user`
--

LOCK TABLES `ks_user` WRITE;
/*!40000 ALTER TABLE `ks_user` DISABLE KEYS */;
INSERT INTO `ks_user` VALUES (1,'<EMAIL>','张三','6a817ee3201dab473f2406d3d9f7c061',1,'00000000000',0,'2025-05-07 10:40:49',NULL,'2025-05-08 18:39:59',NULL,100,NULL,1,NULL,NULL,NULL,0),(2,'','李四','6a817ee3201dab473f2406d3d9f7c061',1,'11111111111',0,'2025-05-07 10:42:29',NULL,NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,0),(3,'','王五','6a817ee3201dab473f2406d3d9f7c061',1,'22222222222',0,'2025-05-07 10:42:29',NULL,NULL,NULL,NULL,NULL,1,NULL,NULL,NULL,0);
/*!40000 ALTER TABLE `ks_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `labels`
--

DROP TABLE IF EXISTS `labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `labels` (
  `label_id` int NOT NULL AUTO_INCREMENT,
  `label_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_by` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `create_time` timestamp NOT NULL,
  `is_delete` int DEFAULT '0' COMMENT '1删除',
  PRIMARY KEY (`label_id`),
  UNIQUE KEY `label_id` (`label_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `labels`
--

LOCK TABLES `labels` WRITE;
/*!40000 ALTER TABLE `labels` DISABLE KEYS */;
INSERT INTO `labels` VALUES (1,'试卷','admin001','2025-04-25 06:36:51',0),(2,'作业','admin001','2025-04-30 09:55:38',0),(3,'个性化问卷','admin001','2025-04-25 06:39:31',0),(4,'收集问卷','admin001','2025-04-25 06:36:52',0);
/*!40000 ALTER TABLE `labels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `notification_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`notification_id`),
  KEY `idx_notifications_user_id` (`user_id`),
  KEY `idx_notifications_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
INSERT INTO `notifications` VALUES ('noti001','student001','考试通知','Java基础测试将于2024-04-22 09:00开始，请准时参加。','exam',0,'2025-04-22 05:13:14'),('noti002','student002','考试通知','Java基础测试将于2024-04-22 09:00开始，请准时参加。','exam',0,'2025-04-22 05:13:14'),('noti003','student003','考试通知','Java基础测试将于2024-04-22 09:00开始，请准时参加。','exam',0,'2025-04-22 05:13:14'),('noti004','student001','成绩通知','您的Java基础测试成绩为85分，请查看详情。','score',1,'2025-04-22 05:13:14'),('noti005','student002','成绩通知','您的Java基础测试成绩为75分，请查看详情。','score',1,'2025-04-22 05:13:14'),('noti006','student003','成绩通知','您的Java基础测试成绩为95分，请查看详情。','score',1,'2025-04-22 05:13:14');
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_blob_triggers`
--

DROP TABLE IF EXISTS `qrtz_blob_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_blob_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Blob类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_blob_triggers`
--

LOCK TABLES `qrtz_blob_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_blob_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_blob_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_calendars`
--

DROP TABLE IF EXISTS `qrtz_calendars`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_calendars` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`,`calendar_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='日历信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_calendars`
--

LOCK TABLES `qrtz_calendars` WRITE;
/*!40000 ALTER TABLE `qrtz_calendars` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_calendars` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_cron_triggers`
--

DROP TABLE IF EXISTS `qrtz_cron_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_cron_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Cron类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_cron_triggers`
--

LOCK TABLES `qrtz_cron_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_cron_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_cron_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_fired_triggers`
--

DROP TABLE IF EXISTS `qrtz_fired_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_fired_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`,`entry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='已触发的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_fired_triggers`
--

LOCK TABLES `qrtz_fired_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_fired_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_fired_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_job_details`
--

DROP TABLE IF EXISTS `qrtz_job_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_job_details` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`job_name`,`job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='任务详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_job_details`
--

LOCK TABLES `qrtz_job_details` WRITE;
/*!40000 ALTER TABLE `qrtz_job_details` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_job_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_locks`
--

DROP TABLE IF EXISTS `qrtz_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_locks` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`,`lock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='存储的悲观锁信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_locks`
--

LOCK TABLES `qrtz_locks` WRITE;
/*!40000 ALTER TABLE `qrtz_locks` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_paused_trigger_grps`
--

DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_paused_trigger_grps` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='暂停的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_paused_trigger_grps`
--

LOCK TABLES `qrtz_paused_trigger_grps` WRITE;
/*!40000 ALTER TABLE `qrtz_paused_trigger_grps` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_paused_trigger_grps` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_scheduler_state`
--

DROP TABLE IF EXISTS `qrtz_scheduler_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_scheduler_state` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`,`instance_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='调度器状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_scheduler_state`
--

LOCK TABLES `qrtz_scheduler_state` WRITE;
/*!40000 ALTER TABLE `qrtz_scheduler_state` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_scheduler_state` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_simple_triggers`
--

DROP TABLE IF EXISTS `qrtz_simple_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simple_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='简单触发器的信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_simple_triggers`
--

LOCK TABLES `qrtz_simple_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_simple_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_simple_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_simprop_triggers`
--

DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_simprop_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='同步机制的行锁表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_simprop_triggers`
--

LOCK TABLES `qrtz_simprop_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_simprop_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_simprop_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qrtz_triggers`
--

DROP TABLE IF EXISTS `qrtz_triggers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `qrtz_triggers` (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  KEY `sched_name` (`sched_name`,`job_name`,`job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='触发器详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qrtz_triggers`
--

LOCK TABLES `qrtz_triggers` WRITE;
/*!40000 ALTER TABLE `qrtz_triggers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qrtz_triggers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `questionnaire_answers`
--

DROP TABLE IF EXISTS `questionnaire_answers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `questionnaire_answers` (
  `answer_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案ID',
  `questionnaire_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷ID',
  `score_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学生ID',
  `questions_id` int NOT NULL COMMENT '问题ID',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '答案内容',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `submit_time` timestamp NOT NULL COMMENT '提交时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_delete` int NOT NULL DEFAULT '0' COMMENT '0存在1删除',
  PRIMARY KEY (`answer_id`),
  KEY `idx_answers_questionnaire` (`questionnaire_id`),
  KEY `idx_answers_question` (`questions_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='问卷答案表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `questionnaire_answers`
--

LOCK TABLES `questionnaire_answers` WRITE;
/*!40000 ALTER TABLE `questionnaire_answers` DISABLE KEYS */;
INSERT INTO `questionnaire_answers` VALUES ('1','Q002','1920724129355456513','1',21,'Ban','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('10','Q002','1920724129355456514','2',21,'Kiven','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('11','Q002','1920724129355456514','2',22,'0110200088','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('12','Q002','1920724129355456514','2',23,'13333333333','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('13','Q002','1920724129355456514','2',24,'男','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('14','Q002','1920724129355456514','2',25,'部长','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('15','Q002','1920724129355456514','2',26,'B','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('16','Q002','1920724129355456514','2',27,'B','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('17','Q002','1920724129355456514','2',28,'B','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('18','Q002','1920724129355456514','2',29,'B','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('2','Q002','1920724129355456513','1',22,'0110200077','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('3','Q002','1920724129355456513','1',23,'18383838399','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('4','Q002','1920724129355456513','1',24,'男','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('5','Q002','1920724129355456513','1',25,'班长','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('6','Q002','1920724129355456513','1',26,'A','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('7','Q002','1920724129355456513','1',27,'A','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('8','Q002','1920724129355456513','1',28,'A','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0),('9','Q002','1920724129355456513','1',29,'A','已完成','2025-05-10 06:54:17','2025-05-10 06:55:02',0);
/*!40000 ALTER TABLE `questionnaire_answers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `questionnaire_scores`
--

DROP TABLE IF EXISTS `questionnaire_scores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `questionnaire_scores` (
  `score_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `questionnaire_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `student_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `submit_time` timestamp NULL DEFAULT NULL,
  `time_spent` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '填写用时(分钟)',
  `status` enum('进行中','已完成') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `start_time` timestamp NOT NULL,
  PRIMARY KEY (`score_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `questionnaire_scores`
--

LOCK TABLES `questionnaire_scores` WRITE;
/*!40000 ALTER TABLE `questionnaire_scores` DISABLE KEYS */;
INSERT INTO `questionnaire_scores` VALUES ('1920724129355456513','Q002','1','2025-05-09 06:15:32','01:00:39','已完成',NULL,'2025-05-09 06:14:52','2025-05-10 08:57:52','2025-05-09 06:14:52'),('1920724129355456514','Q002','2','2025-05-09 06:15:32','00:00:39','已完成',NULL,'2025-05-09 06:14:52','2025-05-10 08:57:40','2025-05-09 06:14:52');
/*!40000 ALTER TABLE `questionnaire_scores` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `questionnaires`
--

DROP TABLE IF EXISTS `questionnaires`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `questionnaires` (
  `questionnaire_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷标题',
  `lable_id` int NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '问卷描述',
  `class_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `duration` int NOT NULL COMMENT '填写时长(分钟)',
  `status` enum('草稿状态','未开始','进行中','已结束') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷状态',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` int NOT NULL DEFAULT '0' COMMENT '0存在1删除',
  PRIMARY KEY (`questionnaire_id`),
  KEY `idx_questionnaires_status` (`status`),
  KEY `idx_questionnaires_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='问卷主表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `questionnaires`
--

LOCK TABLES `questionnaires` WRITE;
/*!40000 ALTER TABLE `questionnaires` DISABLE KEYS */;
INSERT INTO `questionnaires` VALUES ('1921137188028837890','基本信息调查009',4,'收集学生的基本信息','1','2025-04-27 00:00:00','2025-05-15 23:59:59',5,'进行中','admin','2025-05-10 09:36:13','2025-05-16 11:03:45',0),('1923276967545778177','基本信息调查3',4,'收集学生的基本信息','1','2025-04-27 00:00:00','2025-05-15 23:59:59',5,'已结束','admin','2025-05-16 07:18:56','2025-05-16 07:18:56',0),('1923277619982348290','520',4,'2','1','2025-05-15 00:00:00','2025-05-31 15:21:11',5,'进行中','admin','2025-05-16 07:21:32','2025-05-16 10:27:12',1),('1923278637713436674','评语',4,'1','1','2025-05-01 00:00:00','2025-05-31 00:00:00',5,'进行中','admin','2025-05-16 07:25:34','2025-05-16 08:44:33',1),('1923308128556814337','宝贝宝贝',4,'1','1','2025-05-16 00:00:00','2025-05-31 00:00:00',5,'进行中','admin','2025-05-16 09:22:45','2025-05-16 09:31:59',1),('1923310305052463105','张雪荣11',4,'0000','1','2025-05-17 00:00:00','2025-05-31 00:00:00',5,'未开始','admin','2025-05-16 09:31:24','2025-05-16 09:54:57',0),('1923324242842202114','自我介绍',4,'1','1','2025-05-24 00:00:00','2025-05-31 00:00:00',5,'未开始','admin','2025-05-16 10:26:47','2025-05-16 10:27:08',0),('1923329411243876353','11',4,'','1','2025-05-16 00:00:00','2025-05-31 00:00:00',5,'进行中','admin','2025-05-16 10:47:20','2025-05-16 10:47:20',0),('1923331585541709826','001',4,'11','1','2025-05-29 00:00:00','2025-05-31 00:00:00',5,'未开始','admin','2025-05-16 10:55:58','2025-05-16 10:55:58',0),('1923332985042501634','33333333333333333',4,'33333333333333333333333','1','2025-05-26 00:00:00','2025-05-16 19:01:25',5,'未开始','admin','2025-05-16 11:01:32','2025-05-16 11:01:32',0),('1923333694085398530','008',4,'008','1','2025-05-28 00:00:00','2025-05-31 00:00:00',5,'未开始','admin','2025-05-16 11:04:21','2025-05-16 11:04:21',0),('Q001','FACT模式调查问卷',3,'FACT模式调查问卷','1','2025-04-28 00:00:00','2025-05-30 23:59:59',18,'进行中','admin','2025-04-28 02:07:33','2025-05-10 09:30:20',0),('Q002','基本信息调查',4,'收集学生的基本信息','1','2025-04-27 00:00:00','2025-05-15 23:59:59',5,'进行中','admin','2025-04-28 02:07:33','2025-05-10 09:30:20',0),('Q003','课程反馈调查',4,'收集学生对课程内容和进度的反馈','1','2024-03-01 00:00:00','2024-03-31 23:59:59',15,'已结束','admin','2025-04-28 02:07:33','2025-05-10 09:30:20',0),('Q004','校园生活调查',4,'了解学生在校生活状况的问卷','1','2025-10-01 00:00:00','2025-05-30 23:59:59',25,'未开始','admin','2025-04-28 02:07:33','2025-05-10 09:30:20',0),('Q005','毕业去向调查',4,'调查应届毕业生的就业意向','1','2025-06-01 00:00:00','2025-06-30 23:59:59',20,'未开始','admin','2025-04-28 02:07:33','2025-05-10 09:30:20',0);
/*!40000 ALTER TABLE `questionnaires` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `questions`
--

DROP TABLE IF EXISTS `questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `questions` (
  `question_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `type` enum('single','不定项选择题','单选题','多选题','填空题','简答题') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `subject` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `difficulty` enum('easy','medium','hard') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `label` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `points` int DEFAULT NULL,
  `options` json DEFAULT NULL,
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `answering_time` int DEFAULT NULL,
  `topic_classification` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `randomly` tinyint(1) DEFAULT '0',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` int NOT NULL DEFAULT '0' COMMENT '0存在，1删除',
  PRIMARY KEY (`question_id`),
  KEY `idx_questions_type` (`type`),
  KEY `idx_questions_subject` (`subject`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `questions`
--

LOCK TABLES `questions` WRITE;
/*!40000 ALTER TABLE `questions` DISABLE KEYS */;
INSERT INTO `questions` VALUES ('q001','Java中哪个关键词用于定义常量？','单选题','Java','easy','基础语法',2,'{\"A\": \"const\", \"B\": \"final\", \"C\": \"static\", \"D\": \"volatile\"}','A','final关键字用于定义常量，表示该变量的值不能被修改',30,'2',1,'admin','2025-04-22 05:20:32','2025-05-19 02:02:54',0),('q002','以下哪些是Java的基本数据类型？','多选题','Java','easy','数据类型',3,'{\"A\": \"int\", \"B\": \"String\", \"C\": \"boolean\", \"D\": \"Integer\"}','AC','Java的基本数据类型包括：byte、short、int、long、float、double、char、boolean。',45,'1',1,'admin','2025-04-22 05:20:32','2025-05-19 02:02:58',0),('q004','以下哪些是Java的访问修饰符？','不定项选择题','Java','medium','访问控制',3,'{\"A\": \"public\", \"B\": \"private\", \"C\": \"protected\", \"D\": \"default\"}','A,B,C,D','Java的访问修饰符包括：public、private、protected和默认（不写修饰符）。',45,'1',1,'admin','2025-04-22 05:20:32','2025-05-09 02:42:11',0),('q006','Java中，以下哪个不是基本数据类型？','单选题','Java','easy','数据类型',2,'{\"A\": \"int\", \"B\": \"String\", \"C\": \"boolean\", \"D\": \"char\"}','C','String是引用类型，不是基本数据类型。',30,'1',1,'admin','2025-04-22 05:20:32','2025-05-19 02:02:54',0),('q007','Java中，以下哪个关键字用于继承？','单选题','Java','easy','面向对象',2,'{\"A\": \"extends\", \"B\": \"implements\", \"C\": \"import\", \"D\": \"package\"}','A','extends关键字用于类的继承。',30,'1',1,'admin','2025-04-22 05:20:32','2025-05-19 02:02:54',0),('q008','以下哪些是Java的访问修饰符？','单选题','Java','medium','访问控制',3,'{\"A\": \"public\", \"B\": \"private\", \"C\": \"protected\", \"D\": \"default\"}','A','Java的访问修饰符包括：public、private、protected和默认（不写修饰符）。11',45,'1',1,'admin','2025-04-22 05:20:32','2025-05-09 02:42:40',0),('q010','以下哪些是Java的循环语句？','多选题','Java','medium','流程控制',3,'{\"A\": \"for\", \"B\": \"while\", \"C\": \"do-while\", \"D\": \"foreach\"}','ABCD','Java的循环语句包括：for、while、do-while和foreach。',45,'1',1,'admin','2025-04-22 05:20:32','2025-05-19 02:02:54',0);
/*!40000 ALTER TABLE `questions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume`
--

DROP TABLE IF EXISTS `resume`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume` (
  `resume_id` bigint NOT NULL AUTO_INCREMENT COMMENT '简历ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(100) NOT NULL COMMENT '简历标题',
  `status` tinyint DEFAULT '1' COMMENT '简历状态(0-草稿,1-已保存)',
  `is_default` tinyint DEFAULT '0' COMMENT '是否默认简历(0-否,1-是)',
  `template_id` bigint DEFAULT NULL COMMENT '简历模板ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`resume_id`),
  KEY `idx_resume_user` (`user_id`),
  KEY `idx_resume_status` (`status`),
  KEY `idx_resume_default` (`is_default`),
  KEY `idx_resume_template` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='简历主表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume`
--

LOCK TABLES `resume` WRITE;
/*!40000 ALTER TABLE `resume` DISABLE KEYS */;
INSERT INTO `resume` VALUES (1,1,'张三的Java开发工程师简历',1,1,1,'2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,2,'李四的全栈开发工程师简历',1,1,2,'2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,3,'王五的架构师简历',1,1,3,'2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,1,'未命名简历',1,0,1,'2025-05-20 16:09:36','2025-05-20 16:09:36','张三',0),(5,1,'王五的简历',1,0,2,'2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_campus`
--

DROP TABLE IF EXISTS `resume_campus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_campus` (
  `cam_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `campus_experience` text NOT NULL COMMENT '校园经历',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`cam_id`),
  KEY `idx_campus_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='校园经历表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_campus`
--

LOCK TABLES `resume_campus` WRITE;
/*!40000 ALTER TABLE `resume_campus` DISABLE KEYS */;
INSERT INTO `resume_campus` VALUES (1,1,'担任计算机协会会长，组织多次技术分享会','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,1,'参与ACM程序设计大赛，获得省级二等奖','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,2,'担任学生会技术部部长，组织校园技术讲座','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,3,'参与全国大学生计算机设计大赛，获得一等奖','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(5,4,'担任计算机协会会长，组织多次技术分享会','2025-05-20 16:10:48','2025-05-20 16:10:48','张三',0),(6,5,'担任计算机协会会长，组织多次技术分享会','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_campus` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_category`
--

DROP TABLE IF EXISTS `resume_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_category` (
  `cat_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '职位类别名称',
  `code` varchar(50) NOT NULL COMMENT '职位类别编码',
  `description` text COMMENT '职位类别描述',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `status` tinyint DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`cat_id`),
  UNIQUE KEY `uk_category_code` (`code`),
  KEY `idx_category_name` (`name`),
  KEY `idx_category_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职位类别表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_category`
--

LOCK TABLES `resume_category` WRITE;
/*!40000 ALTER TABLE `resume_category` DISABLE KEYS */;
INSERT INTO `resume_category` VALUES (1,'开发人员','DEV','包括前端开发、后端开发、全栈开发等开发相关职位',1,1,'2025-05-20 11:32:48','2025-05-20 11:32:48','admin',0),(2,'运维人员','OPS','包括系统运维、网络运维、DevOps等运维相关职位',2,1,'2025-05-20 11:32:48','2025-05-20 11:32:48','admin',0),(3,'测试人员','QA','包括功能测试、性能测试、自动化测试等测试相关职位',3,1,'2025-05-20 11:32:48','2025-05-20 11:32:48','admin',0),(4,'产品经理','PM','包括产品设计、需求分析、产品规划等产品相关职位',4,1,'2025-05-20 11:32:48','2025-05-20 11:32:48','admin',0),(5,'UI设计师','UI','包括界面设计、交互设计、视觉设计等设计相关职位',5,1,'2025-05-20 11:32:48','2025-05-20 11:32:48','admin',0),(6,'数据分析师','DA','包括数据分析、数据挖掘、商业智能等数据相关职位',6,1,'2025-05-20 11:32:48','2025-05-20 11:32:48','admin',0),(7,'算法工程师','ALG','包括机器学习、深度学习、自然语言处理等算法相关职位',7,1,'2025-05-20 11:32:48','2025-05-20 11:32:48','admin',0),(8,'安全工程师','SEC','包括网络安全、应用安全、安全运维等安全相关职位',8,1,'2025-05-20 11:32:48','2025-05-20 11:32:48','admin',0);
/*!40000 ALTER TABLE `resume_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_certificate`
--

DROP TABLE IF EXISTS `resume_certificate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_certificate` (
  `cer_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `certificate_name` varchar(200) NOT NULL COMMENT '证书奖项',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`cer_id`),
  KEY `idx_cert_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书奖项表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_certificate`
--

LOCK TABLES `resume_certificate` WRITE;
/*!40000 ALTER TABLE `resume_certificate` DISABLE KEYS */;
INSERT INTO `resume_certificate` VALUES (1,1,'Java高级工程师认证','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,1,'阿里云架构师认证','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,1,'优秀员工奖','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,2,'前端开发工程师认证','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(5,2,'优秀新人奖','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(6,3,'系统架构师认证','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(7,3,'技术专家认证','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(8,4,'Java高级工程师认证','2025-05-20 16:10:48','2025-05-20 16:10:48','张三',0),(9,5,'Java高级工程师认证','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_certificate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_educational`
--

DROP TABLE IF EXISTS `resume_educational`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_educational` (
  `edu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `school` varchar(100) NOT NULL COMMENT '学校',
  `major` varchar(100) DEFAULT NULL COMMENT '专业',
  `education` varchar(50) DEFAULT NULL COMMENT '学历',
  `time_period` varchar(100) DEFAULT NULL COMMENT '时间段',
  `main_courses` text COMMENT '主修课程',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`edu_id`),
  KEY `idx_edu_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='教育经历表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_educational`
--

LOCK TABLES `resume_educational` WRITE;
/*!40000 ALTER TABLE `resume_educational` DISABLE KEYS */;
INSERT INTO `resume_educational` VALUES (1,1,'北京大学','计算机科学与技术','本科','2013-09至2017-06','数据结构、算法分析、操作系统、计算机网络、数据库系统','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,1,'清华大学','软件工程','硕士','2017-09至2020-06','高级软件工程、人工智能、机器学习、分布式系统','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,2,'上海交通大学','软件工程','本科','2016-09至2020-06','Java编程、Web开发、数据库、软件工程','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,3,'浙江大学','计算机科学与技术','本科','2011-09至2015-06','计算机基础、算法、操作系统、网络编程','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(5,4,'北京大学','计算机科学与技术','本科','2013-09至2017-06','数据结构、算法分析、操作系统、计算机网络、数据库系统','2025-05-20 16:10:47','2025-05-20 16:10:47','张三',0),(6,5,'北京大学','计算机科学与技术','本科','2013-09至2017-06','数据结构、算法分析、操作系统、计算机网络、数据库系统','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_educational` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_evaluate`
--

DROP TABLE IF EXISTS `resume_evaluate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_evaluate` (
  `eva_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `self_evaluation` text NOT NULL COMMENT '自我评价',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`eva_id`),
  KEY `idx_evaluate_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='自我评价表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_evaluate`
--

LOCK TABLES `resume_evaluate` WRITE;
/*!40000 ALTER TABLE `resume_evaluate` DISABLE KEYS */;
INSERT INTO `resume_evaluate` VALUES (1,1,'热爱技术，善于学习，有良好的团队协作能力和沟通能力。工作认真负责，能够承受压力，有较强的问题解决能力。','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,2,'具有扎实的计算机基础知识，熟悉常用算法和数据结构。热爱编程，有良好的代码风格和编程习惯。','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,3,'具有丰富的系统架构经验，善于解决复杂技术问题。注重团队协作，有较强的领导能力和沟通能力。','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,4,'热爱技术，善于学习，有良好的团队协作能力和沟通能力。','2025-05-20 16:10:48','2025-05-20 16:10:48','张三',0),(5,5,'热爱技术，善于学习，有良好的团队协作能力和沟通能力。','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_evaluate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_information`
--

DROP TABLE IF EXISTS `resume_information`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_information` (
  `infor_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `hometown` varchar(100) DEFAULT NULL COMMENT '籍贯',
  `nationality` varchar(50) DEFAULT NULL COMMENT '民族',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`infor_id`),
  KEY `idx_info_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='基本信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_information`
--

LOCK TABLES `resume_information` WRITE;
/*!40000 ALTER TABLE `resume_information` DISABLE KEYS */;
INSERT INTO `resume_information` VALUES (1,1,'avatar1.jpg','张三','男','1995-05-15','13800138000','<EMAIL>','北京市','汉族','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,2,'avatar2.jpg','李四','女','1998-08-20','13900139000','<EMAIL>','上海市','汉族','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,3,'avatar3.jpg','王五','男','1993-03-10','13700137000','<EMAIL>','广州市','汉族','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,4,'avatar1.jpg','张三','男','1995-05-15','13800138000','<EMAIL>','北京市','汉族','2025-05-20 16:10:21','2025-05-20 16:10:21','张三',0),(5,5,'avatar1.jpg','王五','男','1995-05-15','13800138000','<EMAIL>','北京市','汉族','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_information` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_interest`
--

DROP TABLE IF EXISTS `resume_interest`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_interest` (
  `int_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `interest` varchar(200) NOT NULL COMMENT '兴趣爱好',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`int_id`),
  KEY `idx_interest_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='兴趣爱好表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_interest`
--

LOCK TABLES `resume_interest` WRITE;
/*!40000 ALTER TABLE `resume_interest` DISABLE KEYS */;
INSERT INTO `resume_interest` VALUES (1,1,'阅读技术书籍，关注新技术发展','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,1,'参与开源项目，贡献代码','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,1,'健身，篮球','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,2,'摄影，旅行','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(5,2,'音乐，电影','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(6,3,'阅读，写作','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(7,3,'游泳，跑步','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(8,4,'阅读技术书籍，关注新技术发展','2025-05-20 16:10:48','2025-05-20 16:10:48','张三',0),(9,5,'阅读技术书籍，关注新技术发展','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_interest` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_practice`
--

DROP TABLE IF EXISTS `resume_practice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_practice` (
  `pra_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `time_period` varchar(100) DEFAULT NULL COMMENT '时间段',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `role` varchar(100) DEFAULT NULL COMMENT '担任角色',
  `project_description` text COMMENT '项目描述',
  `project_url` varchar(255) DEFAULT NULL COMMENT '项目地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`pra_id`),
  KEY `idx_practice_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='练手项目表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_practice`
--

LOCK TABLES `resume_practice` WRITE;
/*!40000 ALTER TABLE `resume_practice` DISABLE KEYS */;
INSERT INTO `resume_practice` VALUES (1,1,'2023-01至2023-03','个人博客系统','独立开发','使用Spring Boot + Vue开发的个人博客系统','https://github.com/example/blog','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,1,'2023-04至2023-06','在线简历系统','独立开发','基于Spring Cloud的分布式简历管理系统','https://github.com/example/resume','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,2,'2022-01至2022-03','在线教育平台','独立开发','使用Vue3 + Node.js开发的在线教育平台','https://github.com/example/edu','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,3,'2021-01至2021-03','微服务框架','独立开发','基于Spring Cloud的微服务框架','https://github.com/example/cloud','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(5,4,'2023-01至2023-03','个人博客系统','独立开发','使用Spring Boot + Vue开发的个人博客系统','https://github.com/example/blog','2025-05-20 16:10:47','2025-05-20 16:10:47','张三',0),(6,5,'2023-01至2023-03','个人博客系统','独立开发','使用Spring Boot + Vue开发的个人博客系统','https://github.com/example/blog','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_practice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_project`
--

DROP TABLE IF EXISTS `resume_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_project` (
  `pro_id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '项目名称',
  `start_time` date NOT NULL COMMENT '开始时间',
  `end_time` date NOT NULL COMMENT '结束时间',
  `cat_id` bigint NOT NULL COMMENT '职位类别id',
  `content` text NOT NULL COMMENT '项目内容（字符串形式，用于兼容）',
  `create_at` varchar(20) DEFAULT NULL,
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`pro_id`),
  KEY `idx_project_name` (`name`),
  KEY `idx_project_role` (`cat_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_project`
--

LOCK TABLES `resume_project` WRITE;
/*!40000 ALTER TABLE `resume_project` DISABLE KEYS */;
INSERT INTO `resume_project` VALUES (1,'电商平台重构','2022-01-01','2022-06-30',1,'负责电商平台核心业务重构','admin','2025-05-19 19:22:35','2025-05-20 15:04:39'),(2,'用户中心系统','2022-07-01','2022-12-31',1,'设计并实现用户中心系统','admin','2025-05-19 19:22:35','2025-05-20 15:04:39'),(3,'支付系统优化','2023-01-01','2023-03-31',1,'优化支付系统性能','admin','2025-05-19 19:22:35','2025-05-20 15:04:39');
/*!40000 ALTER TABLE `resume_project` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_project_content`
--

DROP TABLE IF EXISTS `resume_project_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_project_content` (
  `con_id` bigint NOT NULL AUTO_INCREMENT,
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `text` text NOT NULL COMMENT '内容文本',
  `content_order` int NOT NULL COMMENT '内容顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`con_id`),
  KEY `project_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目内容表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_project_content`
--

LOCK TABLES `resume_project_content` WRITE;
/*!40000 ALTER TABLE `resume_project_content` DISABLE KEYS */;
INSERT INTO `resume_project_content` VALUES (1,1,'负责系统架构设计，制定技术方案',1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(2,1,'带领团队完成核心业务重构，提升系统性能50%',2,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(3,1,'优化数据库设计，实现分库分表',3,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(4,2,'设计并实现用户中心系统，支持千万级用户',1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(5,2,'实现分布式Session管理，提高系统可用性',2,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(6,3,'优化支付流程，提升支付成功率',1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(7,3,'实现分布式事务，保证数据一致性',2,'2025-05-19 19:22:35','2025-05-19 19:22:35');
/*!40000 ALTER TABLE `resume_project_content` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_project_content_segment`
--

DROP TABLE IF EXISTS `resume_project_content_segment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_project_content_segment` (
  `seg_id` bigint NOT NULL AUTO_INCREMENT,
  `content_id` bigint NOT NULL COMMENT '项目内容ID',
  `text` text NOT NULL COMMENT '段落文本',
  `is_bold` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加粗：0-否，1-是',
  `segment_order` int NOT NULL COMMENT '段落顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`seg_id`),
  KEY `content_id` (`content_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目内容段落表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_project_content_segment`
--

LOCK TABLES `resume_project_content_segment` WRITE;
/*!40000 ALTER TABLE `resume_project_content_segment` DISABLE KEYS */;
INSERT INTO `resume_project_content_segment` VALUES (1,1,'设计微服务架构，实现服务解耦',1,1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(2,1,'使用Spring Cloud实现服务治理',0,2,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(3,2,'优化系统性能，提升响应速度',1,1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(4,2,'实现缓存机制，减少数据库压力',0,2,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(5,3,'设计分库分表方案',1,1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(6,3,'实现数据迁移工具',0,2,'2025-05-19 19:22:35','2025-05-19 19:22:35');
/*!40000 ALTER TABLE `resume_project_content_segment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_project_experience`
--

DROP TABLE IF EXISTS `resume_project_experience`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_project_experience` (
  `exp_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `time_period` varchar(100) DEFAULT NULL COMMENT '时间段',
  `position_type` varchar(50) DEFAULT NULL COMMENT '职位类别',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `role` varchar(100) DEFAULT NULL COMMENT '担任角色',
  `project_description` text COMMENT '项目描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`exp_id`),
  KEY `idx_exp_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目经验表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_project_experience`
--

LOCK TABLES `resume_project_experience` WRITE;
/*!40000 ALTER TABLE `resume_project_experience` DISABLE KEYS */;
INSERT INTO `resume_project_experience` VALUES (1,1,'2021-03至2021-12','后端开发','电商订单系统重构','技术负责人','负责系统架构设计，带领团队完成订单系统重构，提升系统性能50%','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,1,'2022-01至2022-06','全栈开发','用户中心系统','核心开发','设计并实现用户中心系统，支持千万级用户数据','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,2,'2021-01至2021-06','前端开发','短视频编辑器','技术负责人','负责短视频编辑器的前端架构设计和核心功能开发','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,3,'2019-01至2019-12','架构设计','支付系统重构','架构师','负责支付系统的整体架构设计，解决高并发、分布式事务等问题','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(5,4,'2021-03至2021-12','后端开发','电商订单系统重构','技术负责人','负责系统架构设计，带领团队完成订单系统重构，提升系统性能50%','2025-05-20 16:10:47','2025-05-20 16:10:47','张三',0),(6,5,'2021-03至2021-12','后端开发','电商订单系统重构','技术负责人','负责系统架构设计，带领团队完成订单系统重构，提升系统性能50%','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_project_experience` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_project_question`
--

DROP TABLE IF EXISTS `resume_project_question`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_project_question` (
  `que_id` bigint NOT NULL AUTO_INCREMENT,
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `question` text NOT NULL COMMENT '问题内容',
  `question_order` int NOT NULL COMMENT '问题顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`que_id`),
  KEY `project_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='项目问题表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_project_question`
--

LOCK TABLES `resume_project_question` WRITE;
/*!40000 ALTER TABLE `resume_project_question` DISABLE KEYS */;
INSERT INTO `resume_project_question` VALUES (1,1,'如何保证系统的高可用性？',1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(2,1,'如何处理分布式事务？',2,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(3,1,'如何优化系统性能？',3,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(4,2,'如何设计用户认证系统？',1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(5,2,'如何处理并发问题？',2,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(6,3,'如何保证支付系统的安全性？',1,'2025-05-19 19:22:35','2025-05-19 19:22:35'),(7,3,'如何处理支付超时问题？',2,'2025-05-19 19:22:35','2025-05-19 19:22:35');
/*!40000 ALTER TABLE `resume_project_question` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_skill`
--

DROP TABLE IF EXISTS `resume_skill`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_skill` (
  `ski_id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '技能名称',
  `proficiency` varchar(20) NOT NULL COMMENT '熟练度（一般、良好、熟练、擅长、精通）',
  `description` text NOT NULL COMMENT '技能描述（生成话语）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ski_id`),
  KEY `idx_skill_name` (`name`),
  KEY `idx_skill_proficiency` (`proficiency`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_skill`
--

LOCK TABLES `resume_skill` WRITE;
/*!40000 ALTER TABLE `resume_skill` DISABLE KEYS */;
INSERT INTO `resume_skill` VALUES (1,'Java','精通','Java技能描述','2025-05-19 19:22:35','2025-05-22 14:22:54'),(2,'Go','熟练','Go技能描述','2025-05-19 19:22:35','2025-05-21 17:35:11'),(3,'C#','熟练','C#技能描述','2025-05-19 19:22:35','2025-05-20 13:39:27'),(4,'PHP','熟练','PHP技能描述','2025-05-19 19:22:35','2025-05-20 13:39:27'),(5,'Python','良好','Python技能描述','2025-05-19 19:22:35','2025-05-20 13:39:27');
/*!40000 ALTER TABLE `resume_skill` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_skill_segment`
--

DROP TABLE IF EXISTS `resume_skill_segment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_skill_segment` (
  `seg_id` bigint NOT NULL AUTO_INCREMENT,
  `skill_id` bigint NOT NULL COMMENT '技能ID',
  `text` text NOT NULL COMMENT '段落文本',
  `is_bold` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加粗：0-否，1-是',
  `segment_order` int NOT NULL COMMENT '段落顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`seg_id`),
  KEY `skill_id` (`skill_id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能段落表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_skill_segment`
--

LOCK TABLES `resume_skill_segment` WRITE;
/*!40000 ALTER TABLE `resume_skill_segment` DISABLE KEYS */;
INSERT INTO `resume_skill_segment` VALUES (1,1,'具备良好Java基础，能够独立完成常规开发任务，对常见问题有一定的解决能力。',1,1,'2025-05-19 19:22:35','2025-05-20 13:38:26'),(2,1,'在多个项目中使用Java进行开发，熟悉代码规范和最佳实践，善于编写高质量、可维护的代码。',0,2,'2025-05-19 19:22:35','2025-05-20 13:38:26'),(3,1,'在多个项目中使用Java进行开发，熟悉代码规范和最佳实践，善于编写高质量、可维护的代码。',0,3,'2025-05-19 19:22:35','2025-05-20 13:38:26'),(4,2,'精通Go语言，能够独立完成常规开发任务，对常见问题有一定的解决能力。',1,1,'2025-05-19 19:22:35','2025-05-20 13:38:26'),(5,2,'在多个项目中使用Go语言进行开发，熟悉代码规范和最佳实践，善于编写高质量、可维护的代码。',0,2,'2025-05-19 19:22:35','2025-05-20 13:38:26'),(6,3,'熟练掌握C#语言，能够独立完成常规开发任务，对常见问题有一定的解决能力。',1,1,'2025-05-19 19:22:35','2025-05-20 13:38:26'),(7,3,'在多个项目中使用C#语言进行开发，熟悉代码规范和最佳实践，善于编写高质量、可维护的代码。',0,2,'2025-05-19 19:22:35','2025-05-20 13:38:26');
/*!40000 ALTER TABLE `resume_skill_segment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_talent`
--

DROP TABLE IF EXISTS `resume_talent`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_talent` (
  `tal_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `skill_name` varchar(100) NOT NULL COMMENT '技能名称',
  `proficiency` varchar(50) DEFAULT NULL COMMENT '熟练度',
  `skill_description` text COMMENT '技能描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`tal_id`),
  KEY `idx_talent_resume` (`resume_id`)

) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能特长表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_talent`
--

LOCK TABLES `resume_talent` WRITE;
/*!40000 ALTER TABLE `resume_talent` DISABLE KEYS */;
INSERT INTO `resume_talent` VALUES (1,1,'Java','精通','熟悉Java核心编程，多线程，JVM调优','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,1,'Spring Boot','精通','熟练使用Spring Boot开发Web应用，了解其原理','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,1,'MySQL','熟练','熟悉数据库设计，SQL优化，索引设计','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,1,'Redis','熟练','熟悉Redis数据结构，缓存设计，性能优化','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(5,2,'Vue.js','精通','熟练使用Vue.js进行前端开发，了解其原理','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(6,2,'Node.js','熟练','熟悉Node.js后端开发，了解其性能优化','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(7,3,'系统架构','精通','熟悉分布式系统架构设计，微服务架构','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(8,3,'性能优化','精通','擅长系统性能优化，解决高并发问题','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(9,4,'Java','精通','熟悉Java核心编程，多线程，JVM调优','2025-05-20 16:10:48','2025-05-20 16:10:48','张三',0),(10,5,'Java','精通','熟悉Java核心编程，多线程，JVM调优','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_talent` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `resume_work`
--

DROP TABLE IF EXISTS `resume_work`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `resume_work` (
  `res_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resume_id` bigint NOT NULL COMMENT '简历ID',
  `company` varchar(100) NOT NULL COMMENT '公司',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `time_period` varchar(100) DEFAULT NULL COMMENT '时间段',
  `work_description` text COMMENT '工作描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_at` varchar(50) DEFAULT NULL COMMENT '创建人',
  `is_delete` tinyint DEFAULT '0' COMMENT '删除标识(0-未删除,1-已删除)',
  PRIMARY KEY (`res_id`),
  KEY `idx_work_resume` (`resume_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='工作经验表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `resume_work`
--

LOCK TABLES `resume_work` WRITE;
/*!40000 ALTER TABLE `resume_work` DISABLE KEYS */;
INSERT INTO `resume_work` VALUES (1,1,'阿里巴巴','Java开发工程师','2020-07至2022-12','负责电商平台核心业务开发，优化系统性能，参与架构设计','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(2,1,'腾讯','高级开发工程师','2023-01至今','负责社交平台后端开发，带领团队完成多个重要项目','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(3,2,'字节跳动','全栈开发工程师','2020-07至今','负责短视频平台前端开发，参与后端接口设计','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(4,3,'美团','技术架构师','2018-01至今','负责公司核心业务系统架构设计，技术选型，性能优化','2025-05-19 19:22:35','2025-05-19 19:22:35','admin',0),(5,4,'阿里巴巴','Java开发工程师','2020-07至2022-12','负责电商平台核心业务开发，优化系统性能，参与架构设计','2025-05-20 16:10:47','2025-05-20 16:10:47','张三',0),(6,5,'阿里巴巴','Java开发工程师','2020-07至2022-12','负责电商平台核心业务开发，优化系统性能，参与架构设计','2025-05-20 16:58:22','2025-05-20 16:58:22','张三',0);
/*!40000 ALTER TABLE `resume_work` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `role_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES ('admin','管理员','系统管理员','2025-04-22 05:13:04','2025-04-22 05:13:04'),('student','学生','学生用户','2025-04-22 05:13:04','2025-04-22 05:13:04'),('teacher','教师','教师用户','2025-04-22 05:13:04','2025-04-22 05:13:04');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `score_details`
--

DROP TABLE IF EXISTS `score_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `score_details` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `score_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `question_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `score` double DEFAULT NULL,
  `is_correct` int DEFAULT NULL COMMENT '0是错误，1是正确，2是部分正确',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `score_id` (`score_id`),
  KEY `question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `score_details`
--

LOCK TABLES `score_details` WRITE;
/*!40000 ALTER TABLE `score_details` DISABLE KEYS */;
INSERT INTO `score_details` VALUES ('1924288382993739778','1924288330669797377','q001','B',0,0,'2025-05-19 02:17:56'),('1924288382993739779','1924288330669797377','q002','D',0,0,'2025-05-19 02:17:56'),('1924288382993739780','1924288330669797377','q006','C',10,1,'2025-05-19 02:17:56'),('1924288382993739781','1924288330669797377','q007','C',0,0,'2025-05-19 02:17:56'),('1924288382993739782','1924288330669797377','q008','D',0,0,'2025-05-19 02:17:56'),('1924288382993739783','1924288330669797377','q010','D,C',0,0,'2025-05-19 02:17:56');
/*!40000 ALTER TABLE `score_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `scores`
--

DROP TABLE IF EXISTS `scores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `scores` (
  `score_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `exam_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `student_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `total_score` double NOT NULL,
  `submit_time` timestamp NULL DEFAULT NULL,
  `time_spent` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '答题用时(分钟)',
  `status` enum('考试中','待批改','已完成') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `start_time` timestamp NOT NULL,
  PRIMARY KEY (`score_id`),
  KEY `idx_scores_exam_id` (`exam_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `scores`
--

LOCK TABLES `scores` WRITE;
/*!40000 ALTER TABLE `scores` DISABLE KEYS */;
INSERT INTO `scores` VALUES ('1924286348370423809','exam003','1',0,NULL,NULL,'考试中',NULL,'2025-05-19 02:09:51','2025-05-19 02:09:51','2025-05-19 02:09:52'),('1924288330669797377','exam004','1',10,'2025-05-19 02:17:57','00:00:12','已完成',NULL,'2025-05-19 02:17:44','2025-05-19 02:17:44','2025-05-19 02:17:44');
/*!40000 ALTER TABLE `scores` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `student_class`
--

DROP TABLE IF EXISTS `student_class`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `student_class` (
  `class_id` int NOT NULL AUTO_INCREMENT,
  `class_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `class_num` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_by` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `is_delete` int NOT NULL DEFAULT '0',
  PRIMARY KEY (`class_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `student_class`
--

LOCK TABLES `student_class` WRITE;
/*!40000 ALTER TABLE `student_class` DISABLE KEYS */;
INSERT INTO `student_class` VALUES (1,'计算机科学与技术1班','2','admin001',0),(2,'计算机科学与技术2班','1','admin001',0);
/*!40000 ALTER TABLE `student_class` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `supervisor`
--

DROP TABLE IF EXISTS `supervisor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `supervisor` (
  `supervisor_id` int NOT NULL AUTO_INCREMENT COMMENT '导师ID，主键，自增长',
  `supervisor_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '导师姓名',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属院系',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '职称，如教授、副教授等',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '导师邮箱，具有唯一性',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话号码，可为空',
  `create_time` datetime DEFAULT NULL,
  `edit_time` datetime DEFAULT NULL,
  `is_delete` int DEFAULT '0',
  PRIMARY KEY (`supervisor_id`) USING BTREE,
  UNIQUE KEY `email` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='导师信息表，用于记录导师的基本资料';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `supervisor`
--

LOCK TABLES `supervisor` WRITE;
/*!40000 ALTER TABLE `supervisor` DISABLE KEYS */;
INSERT INTO `supervisor` VALUES (1,'李强','计算机科学与技术学院','教授','<EMAIL>','13812345678',NULL,'2025-05-21 05:52:20',0),(2,'王芳','电子信息工程学院','副教授','<EMAIL>','13987654321',NULL,NULL,0),(3,'张伟','机械工程学院','讲师','<EMAIL>','13711223344',NULL,NULL,0),(4,'刘洋','计算机科学与技术学院','副教授','<EMAIL>','13699887766',NULL,'2025-05-21 03:46:38',0),(5,'陈静','管理学院','教授','<EMAIL>','13566778899',NULL,NULL,0),(6,'赵磊','软件工程学院','讲师','<EMAIL>','13455667788',NULL,NULL,0),(7,'孙梅','信息与通信工程学院','教授','<EMAIL>','13344556677',NULL,NULL,0),(8,'周浩','人工智能学院','副教授','<EMAIL>','13233445566',NULL,NULL,0),(9,'张三','经济管理学院','讲师','<EMAIL>',NULL,NULL,NULL,1),(10,'张三','物联网学院','助教','<EMAIL>',NULL,NULL,NULL,1),(11,'测试','测试','测试','1111111111111',NULL,'2025-05-21 03:43:25','2025-05-21 03:43:25',1),(12,'1','1','1','11',NULL,NULL,'2025-05-21 09:44:42',1),(13,'测试','测试','测试','aa',NULL,NULL,'2025-05-21 11:38:07',1);
/*!40000 ALTER TABLE `supervisor` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_config`
--

DROP TABLE IF EXISTS `sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_config` (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_config`
--

LOCK TABLES `sys_config` WRITE;
/*!40000 ALTER TABLE `sys_config` DISABLE KEYS */;
INSERT INTO `sys_config` VALUES (1,'主框架页-默认皮肤样式名称','sys.index.skinName','skin-blue','Y','admin','2025-04-28 18:49:20','',NULL,'蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),(2,'用户管理-账号初始密码','sys.user.initPassword','123456','Y','admin','2025-04-28 18:49:20','',NULL,'初始化密码 123456'),(3,'主框架页-侧边栏主题','sys.index.sideTheme','theme-dark','Y','admin','2025-04-28 18:49:20','',NULL,'深色主题theme-dark，浅色主题theme-light'),(4,'账号自助-验证码开关','sys.account.captchaEnabled','true','Y','admin','2025-04-28 18:49:20','',NULL,'是否开启验证码功能（true开启，false关闭）'),(5,'账号自助-是否开启用户注册功能','sys.account.registerUser','false','Y','admin','2025-04-28 18:49:20','',NULL,'是否开启注册用户功能（true开启，false关闭）'),(6,'用户登录-黑名单列表','sys.login.blackIPList','','Y','admin','2025-04-28 18:49:20','',NULL,'设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
/*!40000 ALTER TABLE `sys_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dept`
--

DROP TABLE IF EXISTS `sys_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部门名称',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=200 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部门表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dept`
--

LOCK TABLES `sys_dept` WRITE;
/*!40000 ALTER TABLE `sys_dept` DISABLE KEYS */;
INSERT INTO `sys_dept` VALUES (100,0,'0','北京笔墨屋',0,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(101,100,'0,100','北京总公司',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(102,100,'0,100','山西分公司',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(103,101,'0,100,101','研发部门',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(104,101,'0,100,101','市场部门',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(105,101,'0,100,101','测试部门',3,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(106,101,'0,100,101','财务部门',4,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(107,101,'0,100,101','运维部门',5,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(108,102,'0,100,102','市场部门',1,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL),(109,102,'0,100,102','财务部门',2,'若依','15888888888','<EMAIL>','0','0','admin','2025-04-28 18:49:19','',NULL);
/*!40000 ALTER TABLE `sys_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_data`
--

DROP TABLE IF EXISTS `sys_dict_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_data`
--

LOCK TABLES `sys_dict_data` WRITE;
/*!40000 ALTER TABLE `sys_dict_data` DISABLE KEYS */;
INSERT INTO `sys_dict_data` VALUES (1,1,'男','0','sys_user_sex','','','Y','0','admin','2025-04-28 18:49:20','',NULL,'性别男'),(2,2,'女','1','sys_user_sex','','','N','0','admin','2025-04-28 18:49:20','',NULL,'性别女'),(3,3,'未知','2','sys_user_sex','','','N','0','admin','2025-04-28 18:49:20','',NULL,'性别未知'),(4,1,'显示','0','sys_show_hide','','primary','Y','0','admin','2025-04-28 18:49:20','',NULL,'显示菜单'),(5,2,'隐藏','1','sys_show_hide','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'隐藏菜单'),(6,1,'正常','0','sys_normal_disable','','primary','Y','0','admin','2025-04-28 18:49:20','',NULL,'正常状态'),(7,2,'停用','1','sys_normal_disable','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'停用状态'),(8,1,'正常','0','sys_job_status','','primary','Y','0','admin','2025-04-28 18:49:20','',NULL,'正常状态'),(9,2,'暂停','1','sys_job_status','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'停用状态'),(10,1,'默认','DEFAULT','sys_job_group','','','Y','0','admin','2025-04-28 18:49:20','',NULL,'默认分组'),(11,2,'系统','SYSTEM','sys_job_group','','','N','0','admin','2025-04-28 18:49:20','',NULL,'系统分组'),(12,1,'是','Y','sys_yes_no','','primary','Y','0','admin','2025-04-28 18:49:20','',NULL,'系统默认是'),(13,2,'否','N','sys_yes_no','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'系统默认否'),(14,1,'通知','1','sys_notice_type','','warning','Y','0','admin','2025-04-28 18:49:20','',NULL,'通知'),(15,2,'公告','2','sys_notice_type','','success','N','0','admin','2025-04-28 18:49:20','',NULL,'公告'),(16,1,'正常','0','sys_notice_status','','primary','Y','0','admin','2025-04-28 18:49:20','',NULL,'正常状态'),(17,2,'关闭','1','sys_notice_status','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'关闭状态'),(18,99,'其他','0','sys_oper_type','','info','N','0','admin','2025-04-28 18:49:20','',NULL,'其他操作'),(19,1,'新增','1','sys_oper_type','','info','N','0','admin','2025-04-28 18:49:20','',NULL,'新增操作'),(20,2,'修改','2','sys_oper_type','','info','N','0','admin','2025-04-28 18:49:20','',NULL,'修改操作'),(21,3,'删除','3','sys_oper_type','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'删除操作'),(22,4,'授权','4','sys_oper_type','','primary','N','0','admin','2025-04-28 18:49:20','',NULL,'授权操作'),(23,5,'导出','5','sys_oper_type','','warning','N','0','admin','2025-04-28 18:49:20','',NULL,'导出操作'),(24,6,'导入','6','sys_oper_type','','warning','N','0','admin','2025-04-28 18:49:20','',NULL,'导入操作'),(25,7,'强退','7','sys_oper_type','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'强退操作'),(26,8,'生成代码','8','sys_oper_type','','warning','N','0','admin','2025-04-28 18:49:20','',NULL,'生成操作'),(27,9,'清空数据','9','sys_oper_type','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'清空操作'),(28,1,'成功','0','sys_common_status','','primary','N','0','admin','2025-04-28 18:49:20','',NULL,'正常状态'),(29,2,'失败','1','sys_common_status','','danger','N','0','admin','2025-04-28 18:49:20','',NULL,'停用状态');
/*!40000 ALTER TABLE `sys_dict_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_type`
--

DROP TABLE IF EXISTS `sys_dict_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`),
  UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='字典类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_type`
--

LOCK TABLES `sys_dict_type` WRITE;
/*!40000 ALTER TABLE `sys_dict_type` DISABLE KEYS */;
INSERT INTO `sys_dict_type` VALUES (1,'用户性别','sys_user_sex','0','admin','2025-04-28 18:49:20','',NULL,'用户性别列表'),(2,'菜单状态','sys_show_hide','0','admin','2025-04-28 18:49:20','',NULL,'菜单状态列表'),(3,'系统开关','sys_normal_disable','0','admin','2025-04-28 18:49:20','',NULL,'系统开关列表'),(4,'任务状态','sys_job_status','0','admin','2025-04-28 18:49:20','',NULL,'任务状态列表'),(5,'任务分组','sys_job_group','0','admin','2025-04-28 18:49:20','',NULL,'任务分组列表'),(6,'系统是否','sys_yes_no','0','admin','2025-04-28 18:49:20','',NULL,'系统是否列表'),(7,'通知类型','sys_notice_type','0','admin','2025-04-28 18:49:20','',NULL,'通知类型列表'),(8,'通知状态','sys_notice_status','0','admin','2025-04-28 18:49:20','',NULL,'通知状态列表'),(9,'操作类型','sys_oper_type','0','admin','2025-04-28 18:49:20','',NULL,'操作类型列表'),(10,'系统状态','sys_common_status','0','admin','2025-04-28 18:49:20','',NULL,'登录状态列表');
/*!40000 ALTER TABLE `sys_dict_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job`
--

DROP TABLE IF EXISTS `sys_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job` (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`,`job_name`,`job_group`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='定时任务调度表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job`
--

LOCK TABLES `sys_job` WRITE;
/*!40000 ALTER TABLE `sys_job` DISABLE KEYS */;
INSERT INTO `sys_job` VALUES (1,'系统默认（无参）','DEFAULT','ryTask.ryNoParams','0/10 * * * * ?','3','1','1','admin','2025-04-28 18:49:20','',NULL,''),(2,'系统默认（有参）','DEFAULT','ryTask.ryParams(\'ry\')','0/15 * * * * ?','3','1','1','admin','2025-04-28 18:49:20','',NULL,''),(3,'系统默认（多参）','DEFAULT','ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)','0/20 * * * * ?','3','1','1','admin','2025-04-28 18:49:20','',NULL,'');
/*!40000 ALTER TABLE `sys_job` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job_log`
--

DROP TABLE IF EXISTS `sys_job_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job_log` (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '异常信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='定时任务调度日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job_log`
--

LOCK TABLES `sys_job_log` WRITE;
/*!40000 ALTER TABLE `sys_job_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_job_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_logininfor`
--

DROP TABLE IF EXISTS `sys_logininfor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_logininfor` (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '提示消息',
  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`),
  KEY `idx_sys_logininfor_s` (`status`),
  KEY `idx_sys_logininfor_lt` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=209 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统访问记录';
/*!40101 SET character_set_client = @saved_cs_client */;



--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '路由名称',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2075 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='菜单权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu`
--

LOCK TABLES `sys_menu` WRITE;
/*!40000 ALTER TABLE `sys_menu` DISABLE KEYS */;
INSERT INTO `sys_menu` VALUES (1,'系统管理',0,1,'system',NULL,'','',1,0,'M','0','0','','system','admin','2025-04-28 18:49:19','',NULL,'系统管理目录'),(2,'系统监控',0,2,'monitor',NULL,'','',1,0,'M','0','0','','monitor','admin','2025-04-28 18:49:19','',NULL,'系统监控目录'),(3,'系统工具',0,3,'tool',NULL,'','',1,0,'M','0','0','','tool','admin','2025-04-28 18:49:19','',NULL,'系统工具目录'),(4,'若依官网',0,4,'http://ruoyi.vip',NULL,'','',0,0,'M','0','0','','guide','admin','2025-04-28 18:49:19','',NULL,'若依官网地址'),(100,'用户管理',1,1,'user','system/user/index','','',1,0,'C','0','0','system:user:list','user','admin','2025-04-28 18:49:19','',NULL,'用户管理菜单'),(101,'角色管理',1,2,'role','system/role/index','','',1,0,'C','0','0','system:role:list','peoples','admin','2025-04-28 18:49:19','',NULL,'角色管理菜单'),(102,'菜单管理',1,3,'menu','system/menu/index','','',1,0,'C','0','0','system:menu:list','tree-table','admin','2025-04-28 18:49:19','',NULL,'菜单管理菜单'),(103,'部门管理',1,4,'dept','system/dept/index','','',1,0,'C','0','0','system:dept:list','tree','admin','2025-04-28 18:49:19','',NULL,'部门管理菜单'),(104,'岗位管理',1,5,'post','system/post/index','','',1,0,'C','0','0','system:post:list','post','admin','2025-04-28 18:49:19','',NULL,'岗位管理菜单'),(105,'字典管理',1,6,'dict','system/dict/index','','',1,0,'C','0','0','system:dict:list','dict','admin','2025-04-28 18:49:19','',NULL,'字典管理菜单'),(106,'参数设置',1,7,'config','system/config/index','','',1,0,'C','0','0','system:config:list','edit','admin','2025-04-28 18:49:19','',NULL,'参数设置菜单'),(107,'通知公告',1,8,'notice','system/notice/index','','',1,0,'C','0','0','system:notice:list','message','admin','2025-04-28 18:49:19','',NULL,'通知公告菜单'),(108,'日志管理',1,9,'log','','','',1,0,'M','0','0','','log','admin','2025-04-28 18:49:19','',NULL,'日志管理菜单'),(109,'在线用户',2,1,'online','monitor/online/index','','',1,0,'C','0','0','monitor:online:list','online','admin','2025-04-28 18:49:19','',NULL,'在线用户菜单'),(110,'定时任务',2,2,'job','monitor/job/index','','',1,0,'C','0','0','monitor:job:list','job','admin','2025-04-28 18:49:19','',NULL,'定时任务菜单'),(111,'数据监控',2,3,'druid','monitor/druid/index','','',1,0,'C','0','0','monitor:druid:list','druid','admin','2025-04-28 18:49:19','',NULL,'数据监控菜单'),(112,'服务监控',2,4,'server','monitor/server/index','','',1,0,'C','0','0','monitor:server:list','server','admin','2025-04-28 18:49:19','',NULL,'服务监控菜单'),(113,'缓存监控',2,5,'cache','monitor/cache/index','','',1,0,'C','0','0','monitor:cache:list','redis','admin','2025-04-28 18:49:19','',NULL,'缓存监控菜单'),(114,'缓存列表',2,6,'cacheList','monitor/cache/list','','',1,0,'C','0','0','monitor:cache:list','redis-list','admin','2025-04-28 18:49:19','',NULL,'缓存列表菜单'),(115,'表单构建',3,1,'build','tool/build/index','','',1,0,'C','0','0','tool:build:list','build','admin','2025-04-28 18:49:19','',NULL,'表单构建菜单'),(116,'代码生成',3,2,'gen','tool/gen/index','','',1,0,'C','0','0','tool:gen:list','code','admin','2025-04-28 18:49:19','',NULL,'代码生成菜单'),(117,'系统接口',3,3,'swagger','tool/swagger/index','','',1,0,'C','0','0','tool:swagger:list','swagger','admin','2025-04-28 18:49:19','',NULL,'系统接口菜单'),(500,'操作日志',108,1,'operlog','monitor/operlog/index','','',1,0,'C','0','0','monitor:operlog:list','form','admin','2025-04-28 18:49:19','',NULL,'操作日志菜单'),(501,'登录日志',108,2,'logininfor','monitor/logininfor/index','','',1,0,'C','0','0','monitor:logininfor:list','logininfor','admin','2025-04-28 18:49:19','',NULL,'登录日志菜单'),(1000,'用户查询',100,1,'','','','',1,0,'F','0','0','system:user:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1001,'用户新增',100,2,'','','','',1,0,'F','0','0','system:user:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1002,'用户修改',100,3,'','','','',1,0,'F','0','0','system:user:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1003,'用户删除',100,4,'','','','',1,0,'F','0','0','system:user:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1004,'用户导出',100,5,'','','','',1,0,'F','0','0','system:user:export','#','admin','2025-04-28 18:49:19','',NULL,''),(1005,'用户导入',100,6,'','','','',1,0,'F','0','0','system:user:import','#','admin','2025-04-28 18:49:19','',NULL,''),(1006,'重置密码',100,7,'','','','',1,0,'F','0','0','system:user:resetPwd','#','admin','2025-04-28 18:49:19','',NULL,''),(1007,'角色查询',101,1,'','','','',1,0,'F','0','0','system:role:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1008,'角色新增',101,2,'','','','',1,0,'F','0','0','system:role:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1009,'角色修改',101,3,'','','','',1,0,'F','0','0','system:role:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1010,'角色删除',101,4,'','','','',1,0,'F','0','0','system:role:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1011,'角色导出',101,5,'','','','',1,0,'F','0','0','system:role:export','#','admin','2025-04-28 18:49:19','',NULL,''),(1012,'菜单查询',102,1,'','','','',1,0,'F','0','0','system:menu:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1013,'菜单新增',102,2,'','','','',1,0,'F','0','0','system:menu:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1014,'菜单修改',102,3,'','','','',1,0,'F','0','0','system:menu:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1015,'菜单删除',102,4,'','','','',1,0,'F','0','0','system:menu:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1016,'部门查询',103,1,'','','','',1,0,'F','0','0','system:dept:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1017,'部门新增',103,2,'','','','',1,0,'F','0','0','system:dept:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1018,'部门修改',103,3,'','','','',1,0,'F','0','0','system:dept:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1019,'部门删除',103,4,'','','','',1,0,'F','0','0','system:dept:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1020,'岗位查询',104,1,'','','','',1,0,'F','0','0','system:post:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1021,'岗位新增',104,2,'','','','',1,0,'F','0','0','system:post:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1022,'岗位修改',104,3,'','','','',1,0,'F','0','0','system:post:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1023,'岗位删除',104,4,'','','','',1,0,'F','0','0','system:post:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1024,'岗位导出',104,5,'','','','',1,0,'F','0','0','system:post:export','#','admin','2025-04-28 18:49:19','',NULL,''),(1025,'字典查询',105,1,'#','','','',1,0,'F','0','0','system:dict:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1026,'字典新增',105,2,'#','','','',1,0,'F','0','0','system:dict:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1027,'字典修改',105,3,'#','','','',1,0,'F','0','0','system:dict:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1028,'字典删除',105,4,'#','','','',1,0,'F','0','0','system:dict:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1029,'字典导出',105,5,'#','','','',1,0,'F','0','0','system:dict:export','#','admin','2025-04-28 18:49:19','',NULL,''),(1030,'参数查询',106,1,'#','','','',1,0,'F','0','0','system:config:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1031,'参数新增',106,2,'#','','','',1,0,'F','0','0','system:config:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1032,'参数修改',106,3,'#','','','',1,0,'F','0','0','system:config:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1033,'参数删除',106,4,'#','','','',1,0,'F','0','0','system:config:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1034,'参数导出',106,5,'#','','','',1,0,'F','0','0','system:config:export','#','admin','2025-04-28 18:49:19','',NULL,''),(1035,'公告查询',107,1,'#','','','',1,0,'F','0','0','system:notice:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1036,'公告新增',107,2,'#','','','',1,0,'F','0','0','system:notice:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1037,'公告修改',107,3,'#','','','',1,0,'F','0','0','system:notice:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1038,'公告删除',107,4,'#','','','',1,0,'F','0','0','system:notice:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1039,'操作查询',500,1,'#','','','',1,0,'F','0','0','monitor:operlog:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1040,'操作删除',500,2,'#','','','',1,0,'F','0','0','monitor:operlog:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1041,'日志导出',500,3,'#','','','',1,0,'F','0','0','monitor:operlog:export','#','admin','2025-04-28 18:49:19','',NULL,''),(1042,'登录查询',501,1,'#','','','',1,0,'F','0','0','monitor:logininfor:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1043,'登录删除',501,2,'#','','','',1,0,'F','0','0','monitor:logininfor:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1044,'日志导出',501,3,'#','','','',1,0,'F','0','0','monitor:logininfor:export','#','admin','2025-04-28 18:49:19','',NULL,''),(1045,'账户解锁',501,4,'#','','','',1,0,'F','0','0','monitor:logininfor:unlock','#','admin','2025-04-28 18:49:19','',NULL,''),(1046,'在线查询',109,1,'#','','','',1,0,'F','0','0','monitor:online:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1047,'批量强退',109,2,'#','','','',1,0,'F','0','0','monitor:online:batchLogout','#','admin','2025-04-28 18:49:19','',NULL,''),(1048,'单条强退',109,3,'#','','','',1,0,'F','0','0','monitor:online:forceLogout','#','admin','2025-04-28 18:49:19','',NULL,''),(1049,'任务查询',110,1,'#','','','',1,0,'F','0','0','monitor:job:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1050,'任务新增',110,2,'#','','','',1,0,'F','0','0','monitor:job:add','#','admin','2025-04-28 18:49:19','',NULL,''),(1051,'任务修改',110,3,'#','','','',1,0,'F','0','0','monitor:job:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1052,'任务删除',110,4,'#','','','',1,0,'F','0','0','monitor:job:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1053,'状态修改',110,5,'#','','','',1,0,'F','0','0','monitor:job:changeStatus','#','admin','2025-04-28 18:49:19','',NULL,''),(1054,'任务导出',110,6,'#','','','',1,0,'F','0','0','monitor:job:export','#','admin','2025-04-28 18:49:19','',NULL,''),(1055,'生成查询',116,1,'#','','','',1,0,'F','0','0','tool:gen:query','#','admin','2025-04-28 18:49:19','',NULL,''),(1056,'生成修改',116,2,'#','','','',1,0,'F','0','0','tool:gen:edit','#','admin','2025-04-28 18:49:19','',NULL,''),(1057,'生成删除',116,3,'#','','','',1,0,'F','0','0','tool:gen:remove','#','admin','2025-04-28 18:49:19','',NULL,''),(1058,'导入代码',116,4,'#','','','',1,0,'F','0','0','tool:gen:import','#','admin','2025-04-28 18:49:19','',NULL,''),(1059,'预览代码',116,5,'#','','','',1,0,'F','0','0','tool:gen:preview','#','admin','2025-04-28 18:49:19','',NULL,''),(1060,'生成代码',116,6,'#','','','',1,0,'F','0','0','tool:gen:code','#','admin','2025-04-28 18:49:19','',NULL,''),(2000,'答题系统后台',0,5,'aqsystem',NULL,NULL,'',1,0,'M','0','0','','server','admin','2025-04-29 14:29:09','admin','2025-04-29 14:29:59',''),(2006,'统计管理',2000,5,'statistics',NULL,NULL,'',1,0,'M','0','0',NULL,'server','admin','2025-04-29 14:34:28','',NULL,''),(2019,'问卷列表',2040,1,'questionnaires','aqsystem/questionnaires/index',NULL,'',1,0,'C','0','0','aqsystem:questionnaires:list','skill','admin','2025-04-29 17:11:27','admin','2025-04-30 13:53:31','问卷管理菜单'),(2020,'问卷管理查询',2019,1,'#','',NULL,'',1,0,'F','0','0','aqsystem:questionnaires:query','#','admin','2025-04-29 17:11:27','',NULL,''),(2021,'问卷管理新增',2019,2,'#','',NULL,'',1,0,'F','0','0','aqsystem:questionnaires:add','#','admin','2025-04-29 17:11:27','',NULL,''),(2022,'问卷管理修改',2019,3,'#','',NULL,'',1,0,'F','0','0','aqsystem:questionnaires:edit','#','admin','2025-04-29 17:11:27','',NULL,''),(2023,'问卷管理删除',2019,4,'#','',NULL,'',1,0,'F','0','0','aqsystem:questionnaires:remove','#','admin','2025-04-29 17:11:27','',NULL,''),(2024,'问卷管理导出',2019,5,'#','',NULL,'',1,0,'F','0','0','aqsystem:questionnaires:export','#','admin','2025-04-29 17:11:27','',NULL,''),(2025,'试卷列表',2042,1,'exams','aqsystem/exams/index',NULL,'',1,0,'C','0','0','aqsystem:exams:list','list','admin','2025-04-29 17:11:27','admin','2025-04-30 13:56:12','考试管理菜单'),(2026,'考试管理查询',2025,1,'#','',NULL,'',1,0,'F','0','0','aqsystem:exams:query','#','admin','2025-04-29 17:11:27','',NULL,''),(2027,'考试管理新增',2025,2,'#','',NULL,'',1,0,'F','0','0','aqsystem:exams:add','#','admin','2025-04-29 17:11:27','',NULL,''),(2028,'考试管理修改',2025,3,'#','',NULL,'',1,0,'F','0','0','aqsystem:exams:edit','#','admin','2025-04-29 17:11:27','',NULL,''),(2029,'考试管理删除',2025,4,'#','',NULL,'',1,0,'F','0','0','aqsystem:exams:remove','#','admin','2025-04-29 17:11:27','',NULL,''),(2030,'考试管理导出',2025,5,'#','',NULL,'',1,0,'F','0','0','aqsystem:exams:export','#','admin','2025-04-29 17:11:27','',NULL,''),(2040,'问卷管理',2000,4,'questionnaires',NULL,NULL,'',1,0,'M','0','0','','bug','admin','2025-04-30 13:52:26','admin','2025-04-30 13:57:00',''),(2042,'试卷管理',2000,4,'exams','',NULL,'',1,0,'M','0','0','','clipboard','admin','2025-04-30 13:55:51','admin','2025-04-30 13:57:14',''),(2044,'题库管理',2040,2,'setting','aqsystem/questionnaires/setting',NULL,'setting1',1,0,'C','0','0','','checkbox','admin','2025-04-30 14:27:15','admin','2025-04-30 14:32:54',''),(2045,'题库管理',2042,2,'setting','aqsystem/exams/setting',NULL,'setting2',1,0,'C','0','0','','checkbox','admin','2025-04-30 14:31:24','admin','2025-04-30 14:33:04',''),(2048,'学生管理',2000,1,'users','aqsystem/users/index','','',1,0,'C','0','0','system:users:list','bug','admin','2025-05-06 15:20:06','admin','2025-05-06 15:29:18','学生管理菜单'),(2049,'学生管理查询',2048,1,'#','',NULL,'',1,0,'F','0','0','system:users:query','#','admin','2025-05-06 15:20:07','',NULL,''),(2050,'学生管理新增',2048,2,'#','',NULL,'',1,0,'F','0','0','system:users:add','#','admin','2025-05-06 15:20:07','',NULL,''),(2051,'学生管理修改',2048,3,'#','',NULL,'',1,0,'F','0','0','system:users:edit','#','admin','2025-05-06 15:20:07','',NULL,''),(2052,'学生管理删除',2048,4,'#','',NULL,'',1,0,'F','0','0','system:users:remove','#','admin','2025-05-06 15:20:07','',NULL,''),(2053,'学生管理导出',2048,5,'#','',NULL,'',1,0,'F','0','0','system:users:export','#','admin','2025-05-06 15:20:07','',NULL,''),(2061,'班级管理查询',2066,1,'#','',NULL,'',1,0,'F','0','0','system:class:query','#','admin','2025-05-06 15:29:59','',NULL,''),(2062,'班级管理新增',2066,2,'#','',NULL,'',1,0,'F','0','0','system:class:add','#','admin','2025-05-06 15:29:59','',NULL,''),(2063,'班级管理修改',2066,3,'#','',NULL,'',1,0,'F','0','0','system:class:edit','#','admin','2025-05-06 15:29:59','',NULL,''),(2064,'班级管理删除',2066,4,'#','',NULL,'',1,0,'F','0','0','system:class:remove','#','admin','2025-05-06 15:29:59','',NULL,''),(2065,'班级管理导出',2066,5,'#','',NULL,'',1,0,'F','0','0','system:class:export','#','admin','2025-05-06 15:29:59','',NULL,''),(2066,'班级管理',2000,5,'class','aqsystem/class/index',NULL,'',1,0,'C','0','0',NULL,'date','admin','2025-05-06 15:31:47','',NULL,''),(2067,'毕设选题后台',0,6,'/selectTitle',NULL,NULL,'',1,0,'M','0','0','','build','admin','2025-05-20 11:41:50','admin','2025-05-21 10:28:50',''),(2068,'毕设题目管理',2067,1,'selecttitle/topic','selecttitle/topic/index',NULL,'Topic',1,0,'C','0','0','','education','admin','2025-05-20 11:43:09','admin','2025-05-21 10:13:01',''),(2069,'毕设类目管理',2067,2,'selecttitle/category','selecttitle/category/index',NULL,'TopicCategory',1,0,'C','0','0','','list','admin','2025-05-20 11:59:42','admin','2025-05-21 10:13:05',''),(2070,'毕设导师管理',2067,3,'selecttitle/supervisor','selecttitle/supervisor/index',NULL,'Supervisor',1,0,'C','0','0','','peoples','admin','2025-05-20 12:03:29','admin','2025-05-21 10:13:08',''),(2071,'简历系统后台',0,7,'/',NULL,NULL,'',1,0,'M','0','0','','job','admin','2025-05-20 18:23:35','admin','2025-05-20 18:55:21',''),(2072,'技能管理',2071,1,'interview/skills','interview/skills/index',NULL,'skills',1,0,'C','0','0','','skill','admin','2025-05-20 18:24:12','admin','2025-05-20 19:12:57',''),(2073,'项目管理',2071,2,'interview/projects','interview/projects/index',NULL,'projects',1,0,'C','0','0','','select','admin','2025-05-20 18:24:26','admin','2025-05-20 22:41:38',''),(2074,'简历管理',2071,3,'interview/resume','interview/resume/index',NULL,'resume',1,0,'C','0','0','','edit','admin','2025-05-21 17:58:12','admin','2025-05-21 17:58:35','');
/*!40000 ALTER TABLE `sys_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_notice`
--

DROP TABLE IF EXISTS `sys_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_notice` (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='通知公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_notice`
--

LOCK TABLES `sys_notice` WRITE;
/*!40000 ALTER TABLE `sys_notice` DISABLE KEYS */;
INSERT INTO `sys_notice` VALUES (1,'温馨提醒：2018-07-01 若依新版本发布啦','2',_binary '新版本内容','0','admin','2025-04-28 18:49:20','',NULL,'管理员'),(2,'维护通知：2018-07-01 若依系统凌晨维护','1',_binary '维护内容','0','admin','2025-04-28 18:49:20','',NULL,'管理员');
/*!40000 ALTER TABLE `sys_notice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_oper_log`
--

DROP TABLE IF EXISTS `sys_oper_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_oper_log` (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模块标题',
  `business_type` int DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求方式',
  `operator_type` int DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '返回参数',
  `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint DEFAULT '0' COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`),
  KEY `idx_sys_oper_log_bt` (`business_type`),
  KEY `idx_sys_oper_log_s` (`status`),
  KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=517 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志记录';

--
-- Table structure for table `sys_post`
--

DROP TABLE IF EXISTS `sys_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_post` (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='岗位信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_post`
--

LOCK TABLES `sys_post` WRITE;
/*!40000 ALTER TABLE `sys_post` DISABLE KEYS */;
INSERT INTO `sys_post` VALUES (1,'ceo','董事长',1,'0','admin','2025-04-28 18:49:19','',NULL,''),(2,'se','项目经理',2,'0','admin','2025-04-28 18:49:19','',NULL,''),(3,'hr','人力资源',3,'0','admin','2025-04-28 18:49:19','',NULL,''),(4,'user','普通员工',4,'0','admin','2025-04-28 18:49:19','',NULL,'');
/*!40000 ALTER TABLE `sys_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
INSERT INTO `sys_role` VALUES (1,'超级管理员','admin',1,'1',1,1,'0','0','admin','2025-04-28 18:49:19','',NULL,'超级管理员'),(2,'普通角色','common',2,'2',1,1,'0','0','admin','2025-04-28 18:49:19','',NULL,'普通角色'),(100,'学生','student',3,'1',0,1,'0','2','admin','2025-05-06 15:03:20','admin','2025-05-06 15:03:32',NULL);
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_dept`
--

DROP TABLE IF EXISTS `sys_role_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_dept` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色和部门关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_dept`
--

LOCK TABLES `sys_role_dept` WRITE;
/*!40000 ALTER TABLE `sys_role_dept` DISABLE KEYS */;
INSERT INTO `sys_role_dept` VALUES (2,100),(2,101),(2,105);
/*!40000 ALTER TABLE `sys_role_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_menu`
--

DROP TABLE IF EXISTS `sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色和菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_menu`
--

LOCK TABLES `sys_role_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_menu` DISABLE KEYS */;
INSERT INTO `sys_role_menu` VALUES (2,1),(2,2),(2,3),(2,4),(2,100),(2,101),(2,102),(2,103),(2,104),(2,105),(2,106),(2,107),(2,108),(2,109),(2,110),(2,111),(2,112),(2,113),(2,114),(2,115),(2,116),(2,117),(2,500),(2,501),(2,1000),(2,1001),(2,1002),(2,1003),(2,1004),(2,1005),(2,1006),(2,1007),(2,1008),(2,1009),(2,1010),(2,1011),(2,1012),(2,1013),(2,1014),(2,1015),(2,1016),(2,1017),(2,1018),(2,1019),(2,1020),(2,1021),(2,1022),(2,1023),(2,1024),(2,1025),(2,1026),(2,1027),(2,1028),(2,1029),(2,1030),(2,1031),(2,1032),(2,1033),(2,1034),(2,1035),(2,1036),(2,1037),(2,1038),(2,1039),(2,1040),(2,1041),(2,1042),(2,1043),(2,1044),(2,1045),(2,1046),(2,1047),(2,1048),(2,1049),(2,1050),(2,1051),(2,1052),(2,1053),(2,1054),(2,1055),(2,1056),(2,1057),(2,1058),(2,1059),(2,1060);
/*!40000 ALTER TABLE `sys_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,103,'admin','若依','00','<EMAIL>','15888888888','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','127.0.0.1','2025-05-22 12:56:54','admin','2025-04-28 18:49:19','','2025-05-22 12:56:53','管理员'),(2,105,'ry','若依','00','<EMAIL>','15666666666','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','127.0.0.1','2025-04-28 18:49:19','admin','2025-04-28 18:49:19','',NULL,'测试员');
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_post`
--

DROP TABLE IF EXISTS `sys_user_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_post` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户与岗位关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_post`
--

LOCK TABLES `sys_user_post` WRITE;
/*!40000 ALTER TABLE `sys_user_post` DISABLE KEYS */;
INSERT INTO `sys_user_post` VALUES (1,1),(2,2);
/*!40000 ALTER TABLE `sys_user_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_role` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户和角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
INSERT INTO `sys_user_role` VALUES (1,1),(2,2);
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_config`
--

DROP TABLE IF EXISTS `system_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置项键',
  `config_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置项值',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置项说明',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `config_key` (`config_key`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_config`
--

LOCK TABLES `system_config` WRITE;
/*!40000 ALTER TABLE `system_config` DISABLE KEYS */;
INSERT INTO `system_config` VALUES (1,'topic_start_time','2025-05-10 08:00:00','选题开放时间','2025-05-15 16:48:36'),(2,'topic_end_time','2025-05-21 18:00:00','选题截止时间','2025-05-20 09:49:20');
/*!40000 ALTER TABLE `system_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_project`
--

DROP TABLE IF EXISTS `t_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_project` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `start_time` date NOT NULL COMMENT '开始时间',
  `end_time` date NOT NULL COMMENT '结束时间',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目内容（字符串形式，用于兼容）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_project_name` (`name`) USING BTREE,
  KEY `idx_project_role` (`role`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_project`
--

LOCK TABLES `t_project` WRITE;
/*!40000 ALTER TABLE `t_project` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_project` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_project_content`
--

DROP TABLE IF EXISTS `t_project_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_project_content` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容文本',
  `content_order` int NOT NULL COMMENT '内容顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目内容表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_project_content`
--

LOCK TABLES `t_project_content` WRITE;
/*!40000 ALTER TABLE `t_project_content` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_project_content` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_project_question`
--

DROP TABLE IF EXISTS `t_project_question`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_project_question` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题内容',
  `question_order` int NOT NULL COMMENT '问题顺序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `project_id` (`project_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='项目问题表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_project_question`
--

LOCK TABLES `t_project_question` WRITE;
/*!40000 ALTER TABLE `t_project_question` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_project_question` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_skill`
--

DROP TABLE IF EXISTS `t_skill`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_skill` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '技能名称',
  `proficiency` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '熟练度（一般、良好、熟练、擅长、精通）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '技能描述（生成话语）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_skill_name` (`name`) USING BTREE,
  KEY `idx_skill_proficiency` (`proficiency`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='技能表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_skill`
--

LOCK TABLES `t_skill` WRITE;
/*!40000 ALTER TABLE `t_skill` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_skill` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topic`
--

DROP TABLE IF EXISTS `topic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `topic` (
  `topic_id` int NOT NULL AUTO_INCREMENT COMMENT '选题ID，主键，自增',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选题标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '选题描述',
  `supervisor_id` int NOT NULL COMMENT '导师ID，关联导师表',
  `category_id` int NOT NULL COMMENT '选题类别ID，关联选题类别表',
  `tag` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签',
  `publish_date` datetime DEFAULT NULL COMMENT '选题发布时间',
  `deadline` datetime DEFAULT NULL COMMENT '选题截止时间',
  `create_time` datetime DEFAULT NULL,
  `edit_time` datetime DEFAULT NULL,
  `is_delete` int DEFAULT '0',
  `status` int DEFAULT '1' COMMENT '课题状态 0：已选 1：可选',
  `quota` int DEFAULT NULL COMMENT '名额',
  PRIMARY KEY (`topic_id`) USING BTREE,
  KEY `fk_topic_supervisor` (`supervisor_id`) USING BTREE,
  KEY `fk_topic_category` (`category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='选题信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topic`
--

LOCK TABLES `topic` WRITE;
/*!40000 ALTER TABLE `topic` DISABLE KEYS */;
INSERT INTO `topic` VALUES (1,'基于深度学习的图像识别系统','利用卷积神经网络实现图像分类和识别。',1,1,'人工智能、深度学习','2025-02-28 18:00:00',NULL,NULL,'2025-05-20 15:51:42',0,1,NULL),(2,'大数据在智慧校园中的应用研究','分析大数据技术在高校管理中的应用场景。',6,2,'移动开发','2025-03-05 06:00:00',NULL,NULL,NULL,0,1,NULL),(3,'响应式Web界面开发与优化','设计并实现高交互性的前端页面。',3,3,'区块链','2025-03-10 09:00:00','2025-06-20 23:59:59',NULL,NULL,0,1,NULL),(4,'SpringBoot微服务架构实践','构建企业级后端服务系统。',4,4,'分布式系统','2025-03-12 11:30:00','2025-06-25 23:59:59',NULL,NULL,0,1,NULL),(5,'物联网环境下的智能安防系统设计','传感器网络的实时安防监控系统。',5,5,'微服务','2025-03-15 08:00:00',NULL,NULL,NULL,0,1,NULL),(6,'基于SSM的课题管理系统','本课题旨在实现高校选题系统的开发与部署。',8,2,'Java,SSM,Vue','2025-05-15 10:00:00',NULL,NULL,'2025-05-15 16:26:08',0,1,NULL),(8,'测试','测试',3,3,'测试','2025-05-20 15:34:44',NULL,NULL,'2025-05-21 10:45:39',1,1,1),(9,'测试','测试',1,1,'测试','2025-05-21 11:30:47',NULL,NULL,'2025-05-21 11:30:52',1,1,1),(10,'测试','测试',2,5,'测试','2025-05-21 13:32:27',NULL,NULL,'2025-05-21 13:32:31',1,1,1),(11,'测试1','测试1',2,1,'测试','2025-05-21 17:43:27',NULL,NULL,'2025-05-21 17:43:37',1,1,NULL),(12,'测试111','测试',2,3,'测试','2025-05-21 19:36:43',NULL,NULL,'2025-05-21 19:37:03',1,1,NULL);
/*!40000 ALTER TABLE `topic` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topic_application`
--

DROP TABLE IF EXISTS `topic_application`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `topic_application` (
  `application_id` int NOT NULL AUTO_INCREMENT COMMENT '报名ID，主键，自增',
  `u_id` bigint NOT NULL COMMENT '学生ID（关联学生表）',
  `topic_id` int NOT NULL COMMENT '选题ID，关联选题表',
  `application_date` datetime NOT NULL COMMENT '报名时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `edit_time` datetime DEFAULT NULL COMMENT '修改时间',
  `is_delete` int DEFAULT NULL COMMENT '删除标识',
  `status` int DEFAULT '0' COMMENT '审核状态 0：待审核 1：通过 2：未通过',
  PRIMARY KEY (`application_id`) USING BTREE,
  KEY `fk_application_topic` (`topic_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='选题报名表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topic_application`
--

LOCK TABLES `topic_application` WRITE;
/*!40000 ALTER TABLE `topic_application` DISABLE KEYS */;
INSERT INTO `topic_application` VALUES (9,1,1,'2025-05-14 16:49:59','2025-05-14 16:49:59',NULL,0,0),(10,2,2,'2025-05-17 11:05:06','2025-05-17 11:05:06',NULL,0,0),(11,1,6,'2025-05-19 22:15:52','2025-05-19 22:15:52',NULL,0,0),(12,1,5,'2025-05-19 22:17:18','2025-05-19 22:17:18',NULL,0,0),(13,1,4,'2025-05-19 22:22:00','2025-05-19 22:22:00',NULL,0,0),(14,1,3,'2025-05-19 22:23:20','2025-05-19 22:23:20',NULL,0,0);
/*!40000 ALTER TABLE `topic_application` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topic_category`
--

DROP TABLE IF EXISTS `topic_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `topic_category` (
  `category_id` int NOT NULL AUTO_INCREMENT COMMENT '选题类别ID，主键，自增',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类别名称，如人工智能、大数据、前端开发等',
  `create_time` datetime DEFAULT NULL,
  `edit_time` datetime DEFAULT NULL,
  `is_delete` int DEFAULT NULL,
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='选题类别表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topic_category`
--

LOCK TABLES `topic_category` WRITE;
/*!40000 ALTER TABLE `topic_category` DISABLE KEYS */;
INSERT INTO `topic_category` VALUES (1,'人工智能',NULL,'2025-05-21 13:38:05',0),(2,'物联网',NULL,'2025-05-21 13:38:12',0),(3,'Java',NULL,NULL,0),(4,'前端',NULL,NULL,0),(5,'C++',NULL,NULL,0),(6,'C#',NULL,NULL,0),(7,'算法',NULL,NULL,0),(22,'测试',NULL,NULL,1),(23,'11',NULL,'2025-05-21 17:43:51',1),(24,'测试11',NULL,'2025-05-21 19:37:25',1);
/*!40000 ALTER TABLE `topic_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `topic_selection`
--

DROP TABLE IF EXISTS `topic_selection`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `topic_selection` (
  `selection_id` int NOT NULL AUTO_INCREMENT COMMENT '选择记录ID，主键，自增',
  `u_id` bigint NOT NULL COMMENT '学生ID（假设关联学生表）',
  `topic_id` int NOT NULL COMMENT '选题ID，关联选题表',
  `selection_date` datetime NOT NULL COMMENT '选择时间',
  `status` int DEFAULT '0',
  `create_time` datetime DEFAULT NULL,
  `edit_time` datetime DEFAULT NULL,
  `is_delete` int DEFAULT NULL,
  PRIMARY KEY (`selection_id`) USING BTREE,
  KEY `fk_selection_topic` (`topic_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='学生选题记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `topic_selection`
--

LOCK TABLES `topic_selection` WRITE;
/*!40000 ALTER TABLE `topic_selection` DISABLE KEYS */;
INSERT INTO `topic_selection` VALUES (5,1,1,'2025-05-14 16:49:59',0,'2025-05-14 16:49:59',NULL,0),(6,2,2,'2025-05-17 11:05:06',0,'2025-05-17 11:05:06',NULL,0),(7,1,6,'2025-05-19 22:15:52',0,'2025-05-19 22:15:52',NULL,0),(8,1,5,'2025-05-19 22:17:18',0,'2025-05-19 22:17:18',NULL,0),(9,1,4,'2025-05-19 22:22:00',0,'2025-05-19 22:22:00',NULL,0),(10,1,3,'2025-05-19 22:23:20',0,'2025-05-19 22:23:20',NULL,0);
/*!40000 ALTER TABLE `topic_selection` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_login_device`
--

DROP TABLE IF EXISTS `user_login_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_login_device` (
  `ult_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `token` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录token',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `login_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录类型,pc/app',
  `status` int DEFAULT NULL COMMENT '状态，0正常，-1无效',
  `phone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登陆用户名',
  `device_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备号',
  `device_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备类型',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `modify_time` datetime DEFAULT NULL COMMENT '更新时间',
  `invalid_time` datetime DEFAULT NULL COMMENT '失效时间',
  PRIMARY KEY (`ult_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户登录token表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_login_device`
--

LOCK TABLES `user_login_device` WRITE;
/*!40000 ALTER TABLE `user_login_device` DISABLE KEYS */;
INSERT INTO `user_login_device` VALUES (1,'7fd43e45df8443ea9de5b39d2c6ed1e3',1,'pc',0,'00000000000','','','2025-05-09 12:15:26','2025-05-09 12:19:52','2025-08-07 12:19:52');
/*!40000 ALTER TABLE `user_login_device` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `class_id` int NOT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `role_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'active',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('1','1','21',NULL,NULL,1,NULL,'student','active',NULL,'2025-05-08 02:19:19','2025-05-08 02:19:19'),('admin001','admin','$2a$10$IX5kKtuC60jChKIXOWuJJ.67BrSrEeOcpGQiVf8//8fVsCoHukxkG','<EMAIL>','13800138000',0,NULL,'admin','active',NULL,'2025-04-22 05:13:14','2025-04-22 05:39:27'),('student001','student1','$2a$10$AIviDu.gjLNZArvl4VIBjevDieWhMEbCQ2ffjJooLNZl69Z6HZIZi','<EMAIL>','13800138003',1,'https://img1.baidu.com/it/u=3217838212,795208401&fm=253&fmt=auto&app=138&f=JPEG?w=514&h=500','student','active','2025-04-29 05:08:48','2025-04-22 05:13:14','2025-04-29 05:08:48'),('student002','student2','$2a$10$IX5kKtuC60jChKIXOWuJJ.67BrSrEeOcpGQiVf8//8fVsCoHukxkG','<EMAIL>','13800138004',1,NULL,'student','active',NULL,'2025-04-22 05:13:14','2025-04-25 03:49:19'),('student003','student3','$2a$10$IX5kKtuC60jChKIXOWuJJ.67BrSrEeOcpGQiVf8//8fVsCoHukxkG','<EMAIL>','13800138005',1,NULL,'student','active',NULL,'2025-04-22 05:13:14','2025-04-25 03:49:19'),('teacher001','teacher1','$2a$10$IX5kKtuC60jChKIXOWuJJ.67BrSrEeOcpGQiVf8//8fVsCoHukxkG','<EMAIL>','13800138001',0,NULL,'teacher','active',NULL,'2025-04-22 05:13:14','2025-04-22 05:39:27'),('teacher002','teacher2','$2a$10$IX5kKtuC60jChKIXOWuJJ.67BrSrEeOcpGQiVf8//8fVsCoHukxkG','<EMAIL>','13800138002',0,NULL,'teacher','active',NULL,'2025-04-22 05:13:14','2025-04-22 05:39:27');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `wj_questions`
--

DROP TABLE IF EXISTS `wj_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `wj_questions` (
  `questions_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题内容',
  `image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `cueword` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问题提示词',
  `type` enum('单选题','多选题','填空题','简答题','排序题') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题类型',
  `label_id` int NOT NULL COMMENT '所属类型',
  `points` int DEFAULT NULL COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项(JSON格式)',
  `answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '参考答案',
  `analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '解析',
  `randomly` tinyint(1) DEFAULT '0' COMMENT '是否随机选项',
  `required` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否必答',
  `questionnaire_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问卷id',
  `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` int NOT NULL DEFAULT '0' COMMENT '0存在，1删除',
  PRIMARY KEY (`questions_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='问卷问题表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `wj_questions`
--

LOCK TABLES `wj_questions` WRITE;
/*!40000 ALTER TABLE `wj_questions` DISABLE KEYS */;
INSERT INTO `wj_questions` VALUES ('1923272388259209218','1',NULL,'1','单选题',4,1,'{\"A\": \"11\", \"B\": \"11\", \"C\": \"111\", \"D\": \"777\"}',NULL,'111',0,1,'Q002','admin','2025-05-16 07:00:44','2025-05-16 07:00:44',0),('1923272862383333377','QQ',NULL,'10','填空题',4,1,NULL,NULL,'11',0,1,'Q002','admin','2025-05-16 07:02:37','2025-05-16 07:02:37',0),('1923276967570944001','您的姓名是？',NULL,'请填写真实姓名','简答题',4,1,NULL,NULL,NULL,0,1,'1923276967545778177','admin','2025-05-16 07:18:56','2025-05-16 07:33:32',0),('1923276967583526913','选择一下下面的题',NULL,'1','单选题',4,1,'{\"A\": \"11\", \"B\": \"11\", \"C\": \"111\", \"D\": \"777\"}',NULL,'111',0,1,'1923276967545778177','admin','2025-05-16 07:18:56','2025-05-16 07:18:56',0),('1923278319495786498','对实训老师的评价',NULL,'10个字','填空题',4,1,NULL,NULL,'美丽 大方',0,1,'Q002','admin','2025-05-16 07:24:18','2025-05-16 07:24:18',0),('1923278637721825282','1',NULL,NULL,'单选题',4,1,NULL,NULL,NULL,0,1,'1923278637713436674','admin','2025-05-16 07:25:34','2025-05-16 09:02:47',1),('1923278637721825283','QQ',NULL,NULL,'填空题',4,1,NULL,NULL,NULL,0,1,'1923278637713436674','admin','2025-05-16 07:25:34','2025-05-16 09:02:47',1),('1923278637721825284','对实训老师的评价',NULL,NULL,'填空题',4,1,NULL,NULL,NULL,0,1,'1923278637713436674','admin','2025-05-16 07:25:34','2025-05-16 09:02:47',1),('1923278637730213889','对毕业前的规划？',NULL,NULL,'单选题',4,1,NULL,NULL,NULL,0,1,'1923278637713436674','admin','2025-05-16 07:25:34','2025-05-16 07:33:32',0),('1923297740167684098','您在班级是否担任什么职务？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923277619982348290','admin','2025-05-16 08:41:29','2025-05-16 09:02:12',1),('1923297740167684099','您的姓名是？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923277619982348290','admin','2025-05-16 08:41:29','2025-05-16 09:02:12',1),('1923297740167684100','您的学号是？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923277619982348290','admin','2025-05-16 08:41:29','2025-05-16 09:02:12',1),('1923297740167684101','对毕业前的规划？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923277619982348290','admin','2025-05-16 08:41:29','2025-05-16 09:02:12',1),('1923297740192849922','您上学期考试综合成绩在班中的排名？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923277619982348290','admin','2025-05-16 08:41:29','2025-05-16 08:41:29',0),('1923297740192849923','您的性别是？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923277619982348290','admin','2025-05-16 08:41:29','2025-05-16 08:41:29',0),('1923304395462672385','宝贝计划',NULL,'2','单选题',4,1,'{\"A\": \"30\", \"B\": \"40\", \"C\": \"50\", \"D\": \"60\"}',NULL,NULL,0,1,'Q002','admin','2025-05-16 09:07:55','2025-05-16 09:07:55',0),('1923308128556814338','宝贝计划',NULL,'2','单选题',2,1,'{\"A\": \"30\", \"B\": \"40\", \"C\": \"50\", \"D\": \"60\"}',NULL,NULL,0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923308128598757377','您上学期考试综合成绩在班中的排名？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923308128598757378','对毕业前的规划？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923308128598757379','您的性别是？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923308128598757380','您的姓名是？',NULL,'请填写真实姓名','简答题',2,1,NULL,NULL,NULL,0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923308128598757381','对实训老师的评价',NULL,'10个字','填空题',2,1,NULL,NULL,'美丽 大方',0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923308128598757382','选择一下下面的题',NULL,'1','单选题',2,1,'{\"A\": \"11\", \"B\": \"11\", \"C\": \"111\", \"D\": \"777\"}',NULL,'111',0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923308128598757383','QQ',NULL,'10','填空题',2,1,NULL,NULL,'11',0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923308128665866242','1',NULL,'1','单选题',2,1,'{\"A\": \"11\", \"B\": \"11\", \"C\": \"111\", \"D\": \"777\"}',NULL,'111',0,1,'1923308128556814337','admin','2025-05-16 09:22:45','2025-05-16 09:22:45',0),('1923310000445329410','我爱班旭东1',NULL,'我爱班旭东','单选题',4,1,'{\"A\": \"3322222\", \"B\": \"44\", \"C\": \"55\", \"D\": \"66\"}',NULL,'0000',0,1,'Q002','admin','2025-05-16 09:30:12','2025-05-16 09:30:33',1),('1923316229666246657','您的姓名是？',NULL,'请填写真实姓名','简答题',2,1,NULL,NULL,NULL,0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 10:25:40',1),('1923316229666246658','QQ',NULL,'10','填空题',2,1,NULL,NULL,'11',0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 10:25:40',1),('1923316229666246659','宝贝计划',NULL,'2','单选题',2,1,'{\"A\": \"30\", \"B\": \"40\", \"C\": \"50\", \"D\": \"60\"}',NULL,NULL,0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 10:25:40',1),('1923316229666246660','1',NULL,'1','单选题',2,1,'{\"A\": \"11\", \"B\": \"11\", \"C\": \"111\", \"D\": \"777\"}',NULL,'111',0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 10:25:40',1),('1923316229733355521','对毕业前的规划？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 09:54:57',0),('1923316229733355522','选择一下下面的题',NULL,'1','单选题',2,1,'{\"A\": \"11\", \"B\": \"11\", \"C\": \"111\", \"D\": \"777\"}',NULL,'111',0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 09:54:57',0),('1923316229733355523','您在班级是否担任什么职务？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 09:54:57',0),('1923316229733355524','您的性别是？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 09:54:57',0),('1923316229733355525','您上学期考试综合成绩在班中的排名？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 09:54:57',0),('1923316229733355526','对实训老师的评价',NULL,'10个字','填空题',2,1,NULL,NULL,'美丽 大方',0,1,'1923310305052463105','admin','2025-05-16 09:54:57','2025-05-16 09:54:57',0),('1923323554372366338','11',NULL,'3333333333333333333','单选题',1,1,'{\"A\": \"1\", \"B\": \"1\", \"C\": \"3\", \"D\": \"4\"}',NULL,'233',0,1,'Q002','admin','2025-05-16 10:24:03','2025-05-16 10:24:35',1),('1923324329148395521','您上学期考试综合成绩在班中的排名？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923324242842202114','admin','2025-05-16 10:27:08','2025-05-16 10:27:08',0),('1923324329148395522','对毕业前的规划？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923324242842202114','admin','2025-05-16 10:27:08','2025-05-16 10:27:08',0),('1923324329148395523','您在班级是否担任什么职务？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923324242842202114','admin','2025-05-16 10:27:08','2025-05-16 10:27:08',0),('1923324329198727169','对实训老师的评价',NULL,'10个字','填空题',2,1,NULL,NULL,'美丽 大方',0,1,'1923324242842202114','admin','2025-05-16 10:27:08','2025-05-16 10:27:08',0),('1923329411722027009','对实训老师的评价',NULL,'10个字','填空题',2,1,NULL,NULL,'美丽 大方',0,1,'1923329411243876353','admin','2025-05-16 10:47:20','2025-05-16 10:47:20',0),('1923329411730415618','对毕业前的规划？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923329411243876353','admin','2025-05-16 10:47:20','2025-05-16 10:47:20',0),('1923329411730415619','您上学期考试综合成绩在班中的排名？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923329411243876353','admin','2025-05-16 10:47:20','2025-05-16 10:47:20',0),('1923329411738804226','您在班级是否担任什么职务？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923329411243876353','admin','2025-05-16 10:47:20','2025-05-16 10:47:20',0),('1923331585558487041','您的性别是？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923331585541709826','admin','2025-05-16 10:55:58','2025-05-16 10:55:58',0),('1923331585566875649','您在班级是否担任什么职务？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1923331585541709826','admin','2025-05-16 10:55:58','2025-05-16 10:55:58',0),('1923332985403211777','您上学期考试综合成绩在班中的排名？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1923332985042501634','admin','2025-05-16 11:01:32','2025-05-16 11:01:32',0),('1923333546206822402','您的姓名是？',NULL,'请填写真实姓名','简答题',2,1,NULL,NULL,NULL,0,1,'1921137188028837890','admin','2025-05-16 11:03:45','2025-05-16 11:03:45',0),('1923333546215211009','您的性别是？',NULL,NULL,'简答题',2,1,NULL,NULL,NULL,0,1,'1921137188028837890','admin','2025-05-16 11:03:45','2025-05-16 11:03:45',0),('1923333546231988225','对实训老师的评价',NULL,'10个字','填空题',2,1,NULL,NULL,'美丽 大方',0,1,'1921137188028837890','admin','2025-05-16 11:03:45','2025-05-16 11:03:45',0),('1923333546236182529','对毕业前的规划？',NULL,NULL,'单选题',2,1,NULL,NULL,NULL,0,1,'1921137188028837890','admin','2025-05-16 11:03:45','2025-05-16 11:03:45',0),('1923333546244571138','选择一下下面的题',NULL,'1','单选题',2,1,'{\"A\": \"11\", \"B\": \"11\", \"C\": \"111\", \"D\": \"777\"}',NULL,'111',0,1,'1921137188028837890','admin','2025-05-16 11:03:45','2025-05-16 11:03:45',0),('1923333694085398531','对实训老师的评价',NULL,'10个字','填空题',2,1,NULL,NULL,'美丽 大方',0,1,'1923333694085398530','admin','2025-05-16 11:04:21','2025-05-16 11:04:21',0),('21','您的姓名是？',NULL,'请填写真实姓名','简答题',4,1,NULL,NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0),('22','您的学号是？',NULL,NULL,'简答题',4,1,NULL,NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0),('23','您的手机号是？',NULL,'请务必正确填写，否则无法正确为您开通学习系统权限','简答题',4,1,NULL,NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0),('24','您的性别是？',NULL,NULL,'单选题',4,1,'[\"A.男\", \"B.女\"]',NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0),('25','您在班级是否担任什么职务？',NULL,NULL,'简答题',4,1,NULL,NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0),('26','您上学期考试综合成绩在班中的排名？',NULL,NULL,'单选题',4,1,'[\"A.前20%\", \"B.前50%\", \"C.前80%\", \"D.后20%\"]',NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0),('27','请评估一下您对Java开发的运用程度？',NULL,NULL,'单选题',4,1,'[\"A.能够熟练运用，研发过完整项目\", \"B.能运用，实现一些简单项目需求\", \"C.只记得基本语法，欠缺项目研发经验\", \"D.不太行\"]',NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0),('28','请评估一下自己的算法与数据结构能力？',NULL,NULL,'单选题',4,1,'[\"A.能手写，刷过Leetcode\", \"B.良好，熟悉常见算法，能手写\", \"C.能大概讲出算法思路，写不出来\", \"D.不太行\"]',NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0),('29','对毕业前的规划？',NULL,NULL,'单选题',4,1,'[\"A.考研\", \"B.就业\", \"C.自主创业\", \"D.直播\", \"E.还没完全想好\"]',NULL,NULL,0,1,'Q002','admin','2025-04-28 03:11:03','2025-05-13 07:15:41',0);
/*!40000 ALTER TABLE `wj_questions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-22 14:39:28
